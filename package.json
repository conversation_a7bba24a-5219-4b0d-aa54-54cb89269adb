{"name": "biypod-customizer", "version": "0.1.0", "private": true, "description": "Biypod Customizer - A Shopify app for customizable print-on-demand products", "author": "Biypod Team", "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch"}, "dependencies": {"@headlessui/react": "^2.2.4", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^5.1.1", "@shopify/app-bridge": "^3.7.10", "@shopify/app-bridge-react": "^4.2.0", "@shopify/shopify-api": "^11.13.0", "@stripe/stripe-js": "^7.5.0", "@supabase/auth-helpers-nextjs": "^0.10.0", "@supabase/auth-helpers-react": "^0.5.0", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.52.0", "@types/three": "^0.178.1", "@types/uuid": "^10.0.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "dotenv": "^17.2.0", "fabric": "^5.3.0", "lucide-react": "^0.525.0", "next": "15.4.2", "react": "19.1.0", "react-dom": "19.1.0", "react-hook-form": "^7.60.0", "stripe": "^18.3.0", "tailwind-merge": "^3.3.1", "three": "^0.178.0", "uuid": "^11.1.0", "zod": "^4.0.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@testing-library/jest-dom": "^6.6.4", "@testing-library/react": "^16.3.0", "@types/fabric": "^5.3.10", "@types/jest": "^30.0.0", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.2", "jest": "^30.0.5", "jest-environment-jsdom": "^30.0.5", "tailwindcss": "^4", "typescript": "^5"}}