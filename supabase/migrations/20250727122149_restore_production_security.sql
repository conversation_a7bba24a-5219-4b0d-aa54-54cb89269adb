-- Restore production-level security for product_mapping_areas
-- This replaces the temporary testing setup with proper RLS policies

-- Re-enable RLS
ALTER TABLE public.product_mapping_areas ENABLE ROW LEVEL SECURITY;

-- Revoke broad permissions
REVOKE ALL ON public.product_mapping_areas FROM anon;

-- Create production-level RLS policies
CREATE POLICY "Super admin full access" ON public.product_mapping_areas
    FOR ALL USING (is_super_admin());

CREATE POLICY "Merchants can view their product areas" ON public.product_mapping_areas
    FOR SELECT USING (
        is_super_admin() OR
        EXISTS (
            SELECT 1 FROM public.merchants m
            JOIN public.merchant_products mp ON m.id = mp.merchant_id
            WHERE mp.product_id = product_mapping_areas.product_id
            AND m.user_id = auth.uid()
        )
    );

CREATE POLICY "Public can view active product areas" ON public.product_mapping_areas
    FOR SELECT USING (
        is_active = true
        AND EXISTS (
            SELECT 1 FROM public.products p
            WHERE p.id = product_mapping_areas.product_id
            AND p.is_active = true
        )
    );

-- Ensure current user has super_admin role for continued access
INSERT INTO public.users (id, email, role)
SELECT
    auth.uid(),
    auth.email(),
    'super_admin'::user_role
WHERE auth.uid() IS NOT NULL
AND NOT EXISTS (
    SELECT 1 FROM public.users WHERE id = auth.uid()
)
ON CONFLICT (id) DO UPDATE SET role = 'super_admin';