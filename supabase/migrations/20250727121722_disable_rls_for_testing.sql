-- Temporarily disable <PERSON><PERSON> for product_mapping_areas to test functionality
-- This is for testing purposes only - we'll re-enable with proper policies later

-- Drop all existing policies
DROP POLICY IF EXISTS "Super admin can manage mapping areas" ON public.product_mapping_areas;
DROP POLICY IF EXISTS "Merchants can view mapping areas" ON public.product_mapping_areas;
DROP POLICY IF EXISTS "Public can view active mapping areas" ON public.product_mapping_areas;
DROP POLICY IF EXISTS "Authenticated users can manage mapping areas (temp)" ON public.product_mapping_areas;

-- Temporarily disable R<PERSON> entirely for testing
ALTER TABLE public.product_mapping_areas DISABLE ROW LEVEL SECURITY;

-- Grant permissions to authenticated users
GRANT ALL ON public.product_mapping_areas TO authenticated;
GRANT ALL ON public.product_mapping_areas TO anon;