create type "public"."customization_type" as enum ('text', 'image', 'color', 'size', 'font', 'position');

create type "public"."order_status" as enum ('pending', 'processing', 'shipped', 'delivered', 'cancelled', 'refunded');

create type "public"."payment_status" as enum ('pending', 'paid', 'failed', 'refunded', 'partially_refunded');

create type "public"."subscription_status" as enum ('active', 'inactive', 'cancelled', 'past_due', 'trialing');

create type "public"."subscription_tier" as enum ('free', 'basic', 'pro', 'enterprise');

create type "public"."ticket_priority" as enum ('low', 'medium', 'high', 'urgent');

create type "public"."ticket_status" as enum ('open', 'in_progress', 'resolved', 'closed');

create type "public"."user_role" as enum ('super_admin', 'merchant', 'customer');

create table "public"."activity_logs" (
    "id" uuid not null default uuid_generate_v4(),
    "user_id" uuid,
    "merchant_id" uuid,
    "action" text not null,
    "resource_type" text not null,
    "resource_id" uuid,
    "details" jsonb default '{}'::jsonb,
    "ip_address" inet,
    "user_agent" text,
    "created_at" timestamp with time zone default now()
);


alter table "public"."activity_logs" enable row level security;

create table "public"."customization_options" (
    "id" uuid not null default uuid_generate_v4(),
    "product_id" uuid not null,
    "type" customization_type not null,
    "name" text not null,
    "description" text,
    "required" boolean default false,
    "options" jsonb not null default '[]'::jsonb,
    "price_modifier" numeric(10,2) default 0,
    "max_length" integer,
    "allowed_file_types" text[],
    "position_constraints" jsonb,
    "sort_order" integer default 0,
    "is_active" boolean default true,
    "created_at" timestamp with time zone default now(),
    "updated_at" timestamp with time zone default now()
);


alter table "public"."customization_options" enable row level security;

create table "public"."designs" (
    "id" uuid not null default uuid_generate_v4(),
    "customer_email" text not null,
    "merchant_id" uuid not null,
    "product_id" uuid not null,
    "name" text not null,
    "design_data" jsonb not null,
    "preview_url" text,
    "thumbnail_url" text,
    "is_saved" boolean default false,
    "is_public" boolean default false,
    "created_at" timestamp with time zone default now(),
    "updated_at" timestamp with time zone default now()
);


alter table "public"."designs" enable row level security;

create table "public"."merchant_products" (
    "id" uuid not null default uuid_generate_v4(),
    "merchant_id" uuid not null,
    "product_id" uuid not null,
    "shopify_product_id" text,
    "custom_price" numeric(10,2),
    "is_published" boolean default false,
    "published_at" timestamp with time zone,
    "created_at" timestamp with time zone default now(),
    "updated_at" timestamp with time zone default now()
);


alter table "public"."merchant_products" enable row level security;

create table "public"."merchants" (
    "id" uuid not null default uuid_generate_v4(),
    "user_id" uuid not null,
    "shop_domain" text not null,
    "shop_name" text not null,
    "access_token" text not null,
    "subscription_tier" subscription_tier not null default 'free'::subscription_tier,
    "subscription_status" subscription_status not null default 'trialing'::subscription_status,
    "trial_ends_at" timestamp with time zone default (now() + '14 days'::interval),
    "billing_address" jsonb,
    "settings" jsonb default '{"auto_fulfill": false, "customizer_enabled": true}'::jsonb,
    "webhook_url" text,
    "is_active" boolean default true,
    "created_at" timestamp with time zone default now(),
    "updated_at" timestamp with time zone default now()
);


alter table "public"."merchants" enable row level security;

create table "public"."order_items" (
    "id" uuid not null default uuid_generate_v4(),
    "order_id" uuid not null,
    "legacy_product_id" uuid not null,
    "design_id" uuid,
    "quantity" integer not null default 1,
    "legacy_unit_price" numeric(10,2) not null,
    "total_price" numeric(10,2) not null default 0,
    "customization_data" jsonb,
    "production_files" jsonb,
    "sku" text,
    "created_at" timestamp with time zone default now(),
    "merchant_product_id" uuid,
    "product_variant_id" uuid,
    "shopify_line_item_id" text,
    "product_name" text not null,
    "variant_title" text,
    "price" numeric(10,2) default 0,
    "fulfillment_status" text default 'unfulfilled'::text,
    "updated_at" timestamp with time zone default now()
);


alter table "public"."order_items" enable row level security;

create table "public"."orders" (
    "id" uuid not null default uuid_generate_v4(),
    "order_number" text not null,
    "merchant_id" uuid not null,
    "customer_email" text not null,
    "customer_name" text,
    "customer_phone" text,
    "total_amount" numeric(10,2) not null,
    "subtotal" numeric(10,2) not null,
    "tax_amount" numeric(10,2) default 0,
    "shipping_amount" numeric(10,2) default 0,
    "discount_amount" numeric(10,2) default 0,
    "status" order_status not null default 'pending'::order_status,
    "payment_status" payment_status not null default 'pending'::payment_status,
    "payment_intent_id" text,
    "shipping_address" jsonb,
    "billing_address" jsonb,
    "tracking_number" text,
    "tracking_url" text,
    "notes" text,
    "shopify_order_id" text,
    "fulfillment_service" text default 'manual'::text,
    "created_at" timestamp with time zone default now(),
    "updated_at" timestamp with time zone default now()
);


alter table "public"."orders" enable row level security;

create table "public"."product_categories" (
    "id" uuid not null default uuid_generate_v4(),
    "name" text not null,
    "description" text,
    "slug" text not null,
    "image_url" text,
    "sort_order" integer default 0,
    "is_active" boolean default true,
    "created_at" timestamp with time zone default now(),
    "updated_at" timestamp with time zone default now()
);


alter table "public"."product_categories" enable row level security;

create table "public"."product_images" (
    "id" uuid not null default uuid_generate_v4(),
    "product_id" uuid not null,
    "url" text not null,
    "alt_text" text,
    "is_primary" boolean default false,
    "sort_order" integer default 0,
    "created_at" timestamp with time zone default now()
);


alter table "public"."product_images" enable row level security;

create table "public"."product_variants" (
    "id" uuid not null default gen_random_uuid(),
    "merchant_product_id" uuid not null,
    "shopify_variant_id" text,
    "title" text not null,
    "price" numeric(10,2) not null default 0,
    "compare_at_price" numeric(10,2),
    "sku" text,
    "barcode" text,
    "inventory_quantity" integer default 0,
    "inventory_policy" text default 'deny'::text,
    "fulfillment_service" text default 'manual'::text,
    "inventory_management" text,
    "option1" text,
    "option2" text,
    "option3" text,
    "weight" numeric(8,2),
    "weight_unit" text default 'kg'::text,
    "requires_shipping" boolean default true,
    "taxable" boolean default true,
    "position" integer default 1,
    "image_url" text,
    "is_active" boolean default true,
    "created_at" timestamp with time zone default now(),
    "updated_at" timestamp with time zone default now()
);


alter table "public"."product_variants" enable row level security;

create table "public"."products" (
    "id" uuid not null default uuid_generate_v4(),
    "name" text not null,
    "description" text,
    "slug" text not null,
    "base_price" numeric(10,2) not null,
    "category_id" uuid,
    "tags" text[] default '{}'::text[],
    "sku" text,
    "weight" numeric(8,2),
    "dimensions" jsonb,
    "materials" text[],
    "care_instructions" text,
    "is_active" boolean default true,
    "featured" boolean default false,
    "sort_order" integer default 0,
    "created_at" timestamp with time zone default now(),
    "updated_at" timestamp with time zone default now(),
    "print_files" jsonb default '[]'::jsonb,
    "mockup_files" jsonb default '[]'::jsonb
);


alter table "public"."products" enable row level security;

create table "public"."ticket_messages" (
    "id" uuid not null default uuid_generate_v4(),
    "ticket_id" uuid not null,
    "user_id" uuid not null,
    "message" text not null,
    "is_internal" boolean default false,
    "attachments" jsonb default '[]'::jsonb,
    "created_at" timestamp with time zone default now()
);


alter table "public"."ticket_messages" enable row level security;

create table "public"."tickets" (
    "id" uuid not null default uuid_generate_v4(),
    "ticket_number" text not null,
    "user_id" uuid not null,
    "merchant_id" uuid,
    "subject" text not null,
    "description" text not null,
    "status" ticket_status not null default 'open'::ticket_status,
    "priority" ticket_priority not null default 'medium'::ticket_priority,
    "assigned_to" uuid,
    "tags" text[] default '{}'::text[],
    "created_at" timestamp with time zone default now(),
    "updated_at" timestamp with time zone default now()
);


alter table "public"."tickets" enable row level security;

create table "public"."user_profiles" (
    "id" uuid not null default uuid_generate_v4(),
    "user_id" uuid not null,
    "first_name" text,
    "last_name" text,
    "avatar_url" text,
    "phone" text,
    "company" text,
    "created_at" timestamp with time zone default now(),
    "updated_at" timestamp with time zone default now()
);


alter table "public"."user_profiles" enable row level security;

create table "public"."users" (
    "id" uuid not null,
    "email" text not null,
    "role" user_role not null default 'customer'::user_role,
    "created_at" timestamp with time zone default now(),
    "updated_at" timestamp with time zone default now()
);


alter table "public"."users" enable row level security;

create table "public"."webhooks" (
    "id" uuid not null default uuid_generate_v4(),
    "merchant_id" uuid not null,
    "event_type" text not null,
    "url" text not null,
    "secret" text not null,
    "is_active" boolean default true,
    "last_triggered_at" timestamp with time zone,
    "failure_count" integer default 0,
    "created_at" timestamp with time zone default now(),
    "updated_at" timestamp with time zone default now()
);


alter table "public"."webhooks" enable row level security;

CREATE UNIQUE INDEX activity_logs_pkey ON public.activity_logs USING btree (id);

CREATE UNIQUE INDEX customization_options_pkey ON public.customization_options USING btree (id);

CREATE UNIQUE INDEX designs_pkey ON public.designs USING btree (id);

CREATE INDEX idx_activity_logs_created_at ON public.activity_logs USING btree (created_at);

CREATE INDEX idx_activity_logs_user_id ON public.activity_logs USING btree (user_id);

CREATE INDEX idx_customization_options_product_id ON public.customization_options USING btree (product_id);

CREATE INDEX idx_designs_customer_email ON public.designs USING btree (customer_email);

CREATE INDEX idx_designs_merchant_id ON public.designs USING btree (merchant_id);

CREATE INDEX idx_merchant_products_merchant_id ON public.merchant_products USING btree (merchant_id);

CREATE INDEX idx_merchant_products_product_id ON public.merchant_products USING btree (product_id);

CREATE INDEX idx_merchants_shop_domain ON public.merchants USING btree (shop_domain);

CREATE INDEX idx_merchants_user_id ON public.merchants USING btree (user_id);

CREATE INDEX idx_order_items_merchant_product_id ON public.order_items USING btree (merchant_product_id);

CREATE INDEX idx_order_items_order_id ON public.order_items USING btree (order_id);

CREATE INDEX idx_order_items_product_variant_id ON public.order_items USING btree (product_variant_id);

CREATE INDEX idx_order_items_shopify_line_item_id ON public.order_items USING btree (shopify_line_item_id);

CREATE INDEX idx_orders_customer_email ON public.orders USING btree (customer_email);

CREATE INDEX idx_orders_merchant_id ON public.orders USING btree (merchant_id);

CREATE INDEX idx_orders_status ON public.orders USING btree (status);

CREATE INDEX idx_product_images_product_id ON public.product_images USING btree (product_id);

CREATE INDEX idx_product_variants_active ON public.product_variants USING btree (is_active);

CREATE INDEX idx_product_variants_merchant_product_id ON public.product_variants USING btree (merchant_product_id);

CREATE INDEX idx_product_variants_shopify_variant_id ON public.product_variants USING btree (shopify_variant_id);

CREATE INDEX idx_product_variants_sku ON public.product_variants USING btree (sku);

CREATE INDEX idx_products_category_id ON public.products USING btree (category_id);

CREATE INDEX idx_products_is_active ON public.products USING btree (is_active);

CREATE INDEX idx_ticket_messages_ticket_id ON public.ticket_messages USING btree (ticket_id);

CREATE INDEX idx_tickets_status ON public.tickets USING btree (status);

CREATE INDEX idx_tickets_user_id ON public.tickets USING btree (user_id);

CREATE INDEX idx_users_email ON public.users USING btree (email);

CREATE INDEX idx_users_role ON public.users USING btree (role);

CREATE UNIQUE INDEX merchant_products_merchant_id_product_id_key ON public.merchant_products USING btree (merchant_id, product_id);

CREATE UNIQUE INDEX merchant_products_pkey ON public.merchant_products USING btree (id);

CREATE UNIQUE INDEX merchants_pkey ON public.merchants USING btree (id);

CREATE UNIQUE INDEX merchants_shop_domain_key ON public.merchants USING btree (shop_domain);

CREATE UNIQUE INDEX order_items_pkey ON public.order_items USING btree (id);

CREATE UNIQUE INDEX orders_order_number_key ON public.orders USING btree (order_number);

CREATE UNIQUE INDEX orders_pkey ON public.orders USING btree (id);

CREATE UNIQUE INDEX product_categories_name_key ON public.product_categories USING btree (name);

CREATE UNIQUE INDEX product_categories_pkey ON public.product_categories USING btree (id);

CREATE UNIQUE INDEX product_categories_slug_key ON public.product_categories USING btree (slug);

CREATE UNIQUE INDEX product_images_pkey ON public.product_images USING btree (id);

CREATE UNIQUE INDEX product_variants_pkey ON public.product_variants USING btree (id);

CREATE UNIQUE INDEX products_pkey ON public.products USING btree (id);

CREATE UNIQUE INDEX products_sku_key ON public.products USING btree (sku);

CREATE UNIQUE INDEX products_slug_key ON public.products USING btree (slug);

CREATE UNIQUE INDEX ticket_messages_pkey ON public.ticket_messages USING btree (id);

CREATE UNIQUE INDEX tickets_pkey ON public.tickets USING btree (id);

CREATE UNIQUE INDEX tickets_ticket_number_key ON public.tickets USING btree (ticket_number);

CREATE UNIQUE INDEX user_profiles_pkey ON public.user_profiles USING btree (id);

CREATE UNIQUE INDEX user_profiles_user_id_key ON public.user_profiles USING btree (user_id);

CREATE UNIQUE INDEX users_email_key ON public.users USING btree (email);

CREATE UNIQUE INDEX users_pkey ON public.users USING btree (id);

CREATE UNIQUE INDEX webhooks_pkey ON public.webhooks USING btree (id);

alter table "public"."activity_logs" add constraint "activity_logs_pkey" PRIMARY KEY using index "activity_logs_pkey";

alter table "public"."customization_options" add constraint "customization_options_pkey" PRIMARY KEY using index "customization_options_pkey";

alter table "public"."designs" add constraint "designs_pkey" PRIMARY KEY using index "designs_pkey";

alter table "public"."merchant_products" add constraint "merchant_products_pkey" PRIMARY KEY using index "merchant_products_pkey";

alter table "public"."merchants" add constraint "merchants_pkey" PRIMARY KEY using index "merchants_pkey";

alter table "public"."order_items" add constraint "order_items_pkey" PRIMARY KEY using index "order_items_pkey";

alter table "public"."orders" add constraint "orders_pkey" PRIMARY KEY using index "orders_pkey";

alter table "public"."product_categories" add constraint "product_categories_pkey" PRIMARY KEY using index "product_categories_pkey";

alter table "public"."product_images" add constraint "product_images_pkey" PRIMARY KEY using index "product_images_pkey";

alter table "public"."product_variants" add constraint "product_variants_pkey" PRIMARY KEY using index "product_variants_pkey";

alter table "public"."products" add constraint "products_pkey" PRIMARY KEY using index "products_pkey";

alter table "public"."ticket_messages" add constraint "ticket_messages_pkey" PRIMARY KEY using index "ticket_messages_pkey";

alter table "public"."tickets" add constraint "tickets_pkey" PRIMARY KEY using index "tickets_pkey";

alter table "public"."user_profiles" add constraint "user_profiles_pkey" PRIMARY KEY using index "user_profiles_pkey";

alter table "public"."users" add constraint "users_pkey" PRIMARY KEY using index "users_pkey";

alter table "public"."webhooks" add constraint "webhooks_pkey" PRIMARY KEY using index "webhooks_pkey";

alter table "public"."activity_logs" add constraint "activity_logs_merchant_id_fkey" FOREIGN KEY (merchant_id) REFERENCES merchants(id) ON DELETE SET NULL not valid;

alter table "public"."activity_logs" validate constraint "activity_logs_merchant_id_fkey";

alter table "public"."activity_logs" add constraint "activity_logs_user_id_fkey" FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL not valid;

alter table "public"."activity_logs" validate constraint "activity_logs_user_id_fkey";

alter table "public"."customization_options" add constraint "customization_options_product_id_fkey" FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE not valid;

alter table "public"."customization_options" validate constraint "customization_options_product_id_fkey";

alter table "public"."designs" add constraint "designs_merchant_id_fkey" FOREIGN KEY (merchant_id) REFERENCES merchants(id) ON DELETE CASCADE not valid;

alter table "public"."designs" validate constraint "designs_merchant_id_fkey";

alter table "public"."designs" add constraint "designs_product_id_fkey" FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE not valid;

alter table "public"."designs" validate constraint "designs_product_id_fkey";

alter table "public"."merchant_products" add constraint "merchant_products_merchant_id_fkey" FOREIGN KEY (merchant_id) REFERENCES merchants(id) ON DELETE CASCADE not valid;

alter table "public"."merchant_products" validate constraint "merchant_products_merchant_id_fkey";

alter table "public"."merchant_products" add constraint "merchant_products_merchant_id_product_id_key" UNIQUE using index "merchant_products_merchant_id_product_id_key";

alter table "public"."merchant_products" add constraint "merchant_products_product_id_fkey" FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE not valid;

alter table "public"."merchant_products" validate constraint "merchant_products_product_id_fkey";

alter table "public"."merchants" add constraint "merchants_shop_domain_key" UNIQUE using index "merchants_shop_domain_key";

alter table "public"."merchants" add constraint "merchants_user_id_fkey" FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE not valid;

alter table "public"."merchants" validate constraint "merchants_user_id_fkey";

alter table "public"."order_items" add constraint "fk_order_items_product_variant_id" FOREIGN KEY (product_variant_id) REFERENCES product_variants(id) ON DELETE SET NULL not valid;

alter table "public"."order_items" validate constraint "fk_order_items_product_variant_id";

alter table "public"."order_items" add constraint "order_items_design_id_fkey" FOREIGN KEY (design_id) REFERENCES designs(id) not valid;

alter table "public"."order_items" validate constraint "order_items_design_id_fkey";

alter table "public"."order_items" add constraint "order_items_merchant_product_id_fkey" FOREIGN KEY (merchant_product_id) REFERENCES merchant_products(id) ON DELETE SET NULL not valid;

alter table "public"."order_items" validate constraint "order_items_merchant_product_id_fkey";

alter table "public"."order_items" add constraint "order_items_order_id_fkey" FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE not valid;

alter table "public"."order_items" validate constraint "order_items_order_id_fkey";

alter table "public"."order_items" add constraint "order_items_product_id_fkey" FOREIGN KEY (legacy_product_id) REFERENCES products(id) not valid;

alter table "public"."order_items" validate constraint "order_items_product_id_fkey";

alter table "public"."orders" add constraint "orders_merchant_id_fkey" FOREIGN KEY (merchant_id) REFERENCES merchants(id) ON DELETE CASCADE not valid;

alter table "public"."orders" validate constraint "orders_merchant_id_fkey";

alter table "public"."orders" add constraint "orders_order_number_key" UNIQUE using index "orders_order_number_key";

alter table "public"."product_categories" add constraint "product_categories_name_key" UNIQUE using index "product_categories_name_key";

alter table "public"."product_categories" add constraint "product_categories_slug_key" UNIQUE using index "product_categories_slug_key";

alter table "public"."product_images" add constraint "product_images_product_id_fkey" FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE not valid;

alter table "public"."product_images" validate constraint "product_images_product_id_fkey";

alter table "public"."product_variants" add constraint "product_variants_merchant_product_id_fkey" FOREIGN KEY (merchant_product_id) REFERENCES merchant_products(id) ON DELETE CASCADE not valid;

alter table "public"."product_variants" validate constraint "product_variants_merchant_product_id_fkey";

alter table "public"."products" add constraint "products_category_id_fkey" FOREIGN KEY (category_id) REFERENCES product_categories(id) not valid;

alter table "public"."products" validate constraint "products_category_id_fkey";

alter table "public"."products" add constraint "products_sku_key" UNIQUE using index "products_sku_key";

alter table "public"."products" add constraint "products_slug_key" UNIQUE using index "products_slug_key";

alter table "public"."ticket_messages" add constraint "ticket_messages_ticket_id_fkey" FOREIGN KEY (ticket_id) REFERENCES tickets(id) ON DELETE CASCADE not valid;

alter table "public"."ticket_messages" validate constraint "ticket_messages_ticket_id_fkey";

alter table "public"."ticket_messages" add constraint "ticket_messages_user_id_fkey" FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE not valid;

alter table "public"."ticket_messages" validate constraint "ticket_messages_user_id_fkey";

alter table "public"."tickets" add constraint "tickets_assigned_to_fkey" FOREIGN KEY (assigned_to) REFERENCES users(id) ON DELETE SET NULL not valid;

alter table "public"."tickets" validate constraint "tickets_assigned_to_fkey";

alter table "public"."tickets" add constraint "tickets_merchant_id_fkey" FOREIGN KEY (merchant_id) REFERENCES merchants(id) ON DELETE SET NULL not valid;

alter table "public"."tickets" validate constraint "tickets_merchant_id_fkey";

alter table "public"."tickets" add constraint "tickets_ticket_number_key" UNIQUE using index "tickets_ticket_number_key";

alter table "public"."tickets" add constraint "tickets_user_id_fkey" FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE not valid;

alter table "public"."tickets" validate constraint "tickets_user_id_fkey";

alter table "public"."user_profiles" add constraint "user_profiles_user_id_fkey" FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE not valid;

alter table "public"."user_profiles" validate constraint "user_profiles_user_id_fkey";

alter table "public"."user_profiles" add constraint "user_profiles_user_id_key" UNIQUE using index "user_profiles_user_id_key";

alter table "public"."users" add constraint "users_email_key" UNIQUE using index "users_email_key";

alter table "public"."users" add constraint "users_id_fkey" FOREIGN KEY (id) REFERENCES auth.users(id) ON DELETE CASCADE not valid;

alter table "public"."users" validate constraint "users_id_fkey";

alter table "public"."webhooks" add constraint "webhooks_merchant_id_fkey" FOREIGN KEY (merchant_id) REFERENCES merchants(id) ON DELETE CASCADE not valid;

alter table "public"."webhooks" validate constraint "webhooks_merchant_id_fkey";

set check_function_bodies = off;

CREATE OR REPLACE FUNCTION public.calculate_order_totals(order_id uuid)
 RETURNS void
 LANGUAGE plpgsql
AS $function$
DECLARE
    subtotal DECIMAL(10,2);
    tax_rate DECIMAL(5,4) := 0.0875; -- Default 8.75% tax rate
    tax_amount DECIMAL(10,2);
    shipping_amount DECIMAL(10,2) := 9.99; -- Default shipping
    total_amount DECIMAL(10,2);
BEGIN
    -- Calculate subtotal from order items
    SELECT COALESCE(SUM(total_price), 0) INTO subtotal
    FROM public.order_items
    WHERE order_items.order_id = calculate_order_totals.order_id;
    
    -- Calculate tax
    tax_amount := subtotal * tax_rate;
    
    -- Free shipping over $75
    IF subtotal >= 75 THEN
        shipping_amount := 0;
    END IF;
    
    -- Calculate total
    total_amount := subtotal + tax_amount + shipping_amount;
    
    -- Update order
    UPDATE public.orders SET
        subtotal = calculate_order_totals.subtotal,
        tax_amount = calculate_order_totals.tax_amount,
        shipping_amount = calculate_order_totals.shipping_amount,
        total_amount = calculate_order_totals.total_amount,
        updated_at = NOW()
    WHERE id = calculate_order_totals.order_id;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.generate_order_number()
 RETURNS text
 LANGUAGE plpgsql
AS $function$
DECLARE
    new_number TEXT;
    exists_check BOOLEAN;
BEGIN
    LOOP
        -- Generate order number: BO-YYYYMMDD-XXXX (BO = Biypod Order)
        new_number := 'BO-' || TO_CHAR(NOW(), 'YYYYMMDD') || '-' || 
                     LPAD(FLOOR(RANDOM() * 10000)::TEXT, 4, '0');
        
        -- Check if this number already exists
        SELECT EXISTS(SELECT 1 FROM public.orders WHERE order_number = new_number) INTO exists_check;
        
        -- If it doesn't exist, we can use it
        IF NOT exists_check THEN
            EXIT;
        END IF;
    END LOOP;
    
    RETURN new_number;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.generate_ticket_number()
 RETURNS text
 LANGUAGE plpgsql
AS $function$
DECLARE
    new_number TEXT;
    exists_check BOOLEAN;
BEGIN
    LOOP
        -- Generate ticket number: TK-YYYYMMDD-XXXX
        new_number := 'TK-' || TO_CHAR(NOW(), 'YYYYMMDD') || '-' || 
                     LPAD(FLOOR(RANDOM() * 10000)::TEXT, 4, '0');
        
        -- Check if this number already exists
        SELECT EXISTS(SELECT 1 FROM public.tickets WHERE ticket_number = new_number) INTO exists_check;
        
        -- If it doesn't exist, we can use it
        IF NOT exists_check THEN
            EXIT;
        END IF;
    END LOOP;
    
    RETURN new_number;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.get_admin_stats()
 RETURNS jsonb
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
DECLARE
    stats JSONB;
BEGIN
    SELECT jsonb_build_object(
        'total_merchants', (SELECT COUNT(*) FROM public.merchants WHERE is_active = true),
        'total_orders', (SELECT COUNT(*) FROM public.orders),
        'total_revenue', (SELECT COALESCE(SUM(total_amount), 0) FROM public.orders WHERE payment_status = 'paid'),
        'total_products', (SELECT COUNT(*) FROM public.products WHERE is_active = true),
        'total_designs', (SELECT COUNT(*) FROM public.designs),
        'open_tickets', (SELECT COUNT(*) FROM public.tickets WHERE status IN ('open', 'in_progress')),
        'monthly_revenue', (
            SELECT COALESCE(SUM(total_amount), 0) 
            FROM public.orders 
            WHERE payment_status = 'paid' 
            AND created_at >= DATE_TRUNC('month', NOW())
        ),
        'monthly_orders', (
            SELECT COUNT(*) 
            FROM public.orders 
            WHERE created_at >= DATE_TRUNC('month', NOW())
        )
    ) INTO stats;
    
    RETURN stats;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.get_merchant_stats(merchant_id uuid)
 RETURNS jsonb
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
DECLARE
    stats JSONB;
BEGIN
    SELECT jsonb_build_object(
        'total_orders', COUNT(o.id),
        'total_revenue', COALESCE(SUM(o.total_amount), 0),
        'pending_orders', COUNT(o.id) FILTER (WHERE o.status = 'pending'),
        'processing_orders', COUNT(o.id) FILTER (WHERE o.status = 'processing'),
        'shipped_orders', COUNT(o.id) FILTER (WHERE o.status = 'shipped'),
        'total_designs', (
            SELECT COUNT(*) FROM public.designs d 
            WHERE d.merchant_id = get_merchant_stats.merchant_id
        ),
        'published_products', (
            SELECT COUNT(*) FROM public.merchant_products mp 
            WHERE mp.merchant_id = get_merchant_stats.merchant_id AND mp.is_published = true
        )
    ) INTO stats
    FROM public.orders o
    WHERE o.merchant_id = get_merchant_stats.merchant_id;
    
    RETURN stats;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.get_user_merchant_id()
 RETURNS uuid
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
BEGIN
    RETURN (
        SELECT m.id FROM public.merchants m
        JOIN public.users u ON u.id = m.user_id
        WHERE u.id = auth.uid()
    );
END;
$function$
;

CREATE OR REPLACE FUNCTION public.handle_new_user()
 RETURNS trigger
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
BEGIN
    -- Create user record
    INSERT INTO public.users (id, email, role)
    VALUES (NEW.id, NEW.email, 'customer');
    
    -- Create user profile with metadata from auth.users
    INSERT INTO public.user_profiles (
        user_id,
        first_name,
        last_name,
        company
    ) VALUES (
        NEW.id,
        COALESCE(NEW.raw_user_meta_data->>'first_name', ''),
        COALESCE(NEW.raw_user_meta_data->>'last_name', ''),
        COALESCE(NEW.raw_user_meta_data->>'company', '')
    );
    
    RETURN NEW;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.is_merchant()
 RETURNS boolean
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM public.users 
        WHERE id = auth.uid() AND role = 'merchant'
    );
END;
$function$
;

CREATE OR REPLACE FUNCTION public.is_super_admin()
 RETURNS boolean
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM public.users 
        WHERE id = auth.uid() AND role = 'super_admin'
    );
END;
$function$
;

CREATE OR REPLACE FUNCTION public.log_activity(p_action text, p_resource_type text, p_user_id text DEFAULT NULL::text, p_merchant_id text DEFAULT NULL::text, p_resource_id text DEFAULT NULL::text, p_details jsonb DEFAULT '{}'::jsonb)
 RETURNS uuid
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
DECLARE
    log_id UUID;
    final_user_id UUID;
    final_merchant_id UUID;
    final_resource_id UUID;
BEGIN
    -- Convert empty strings to NULL and validate UUIDs
    IF p_user_id IS NOT NULL AND p_user_id != '' THEN
        BEGIN
            final_user_id := p_user_id::UUID;
        EXCEPTION WHEN invalid_text_representation THEN
            final_user_id := NULL;
        END;
    ELSE
        final_user_id := NULL;
    END IF;
    
    IF p_merchant_id IS NOT NULL AND p_merchant_id != '' THEN
        BEGIN
            final_merchant_id := p_merchant_id::UUID;
        EXCEPTION WHEN invalid_text_representation THEN
            final_merchant_id := NULL;
        END;
    ELSE
        final_merchant_id := NULL;
    END IF;
    
    IF p_resource_id IS NOT NULL AND p_resource_id != '' THEN
        BEGIN
            final_resource_id := p_resource_id::UUID;
        EXCEPTION WHEN invalid_text_representation THEN
            final_resource_id := NULL;
        END;
    ELSE
        final_resource_id := NULL;
    END IF;
    
    -- Insert the activity log
    INSERT INTO public.activity_logs (
        user_id,
        merchant_id,
        action,
        resource_type,
        resource_id,
        details
    ) VALUES (
        final_user_id,
        final_merchant_id,
        p_action,
        p_resource_type,
        final_resource_id,
        p_details
    ) RETURNING id INTO log_id;
    
    RETURN log_id;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.log_order_status_change()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$
BEGIN
    IF OLD.status IS DISTINCT FROM NEW.status THEN
        PERFORM log_activity(
            auth.uid(),
            NEW.merchant_id,
            'order_status_changed',
            'order',
            NEW.id,
            jsonb_build_object(
                'old_status', OLD.status,
                'new_status', NEW.status,
                'order_number', NEW.order_number
            )
        );
    END IF;
    RETURN NEW;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.recalculate_order_totals()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$
BEGIN
    IF TG_OP = 'DELETE' THEN
        PERFORM calculate_order_totals(OLD.order_id);
        RETURN OLD;
    ELSE
        PERFORM calculate_order_totals(NEW.order_id);
        RETURN NEW;
    END IF;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.search_products(search_term text DEFAULT ''::text, category_id uuid DEFAULT NULL::uuid, limit_count integer DEFAULT 20, offset_count integer DEFAULT 0)
 RETURNS TABLE(id uuid, name text, description text, slug text, base_price numeric, category_name text, primary_image_url text, tags text[])
 LANGUAGE plpgsql
AS $function$
BEGIN
    RETURN QUERY
    SELECT 
        p.id,
        p.name,
        p.description,
        p.slug,
        p.base_price,
        pc.name as category_name,
        pi.url as primary_image_url,
        p.tags
    FROM public.products p
    LEFT JOIN public.product_categories pc ON pc.id = p.category_id
    LEFT JOIN public.product_images pi ON pi.product_id = p.id AND pi.is_primary = true
    WHERE p.is_active = true
    AND (search_term = '' OR p.name ILIKE '%' || search_term || '%' OR p.description ILIKE '%' || search_term || '%')
    AND (category_id IS NULL OR p.category_id = search_products.category_id)
    ORDER BY p.featured DESC, p.sort_order ASC, p.name ASC
    LIMIT limit_count
    OFFSET offset_count;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.set_order_number()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$
BEGIN
    IF NEW.order_number IS NULL OR NEW.order_number = '' THEN
        NEW.order_number := generate_order_number();
    END IF;
    RETURN NEW;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.set_ticket_number()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$
BEGIN
    IF NEW.ticket_number IS NULL OR NEW.ticket_number = '' THEN
        NEW.ticket_number := generate_ticket_number();
    END IF;
    RETURN NEW;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.update_updated_at_column()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$function$
;

grant delete on table "public"."activity_logs" to "anon";

grant insert on table "public"."activity_logs" to "anon";

grant references on table "public"."activity_logs" to "anon";

grant select on table "public"."activity_logs" to "anon";

grant trigger on table "public"."activity_logs" to "anon";

grant truncate on table "public"."activity_logs" to "anon";

grant update on table "public"."activity_logs" to "anon";

grant delete on table "public"."activity_logs" to "authenticated";

grant insert on table "public"."activity_logs" to "authenticated";

grant references on table "public"."activity_logs" to "authenticated";

grant select on table "public"."activity_logs" to "authenticated";

grant trigger on table "public"."activity_logs" to "authenticated";

grant truncate on table "public"."activity_logs" to "authenticated";

grant update on table "public"."activity_logs" to "authenticated";

grant delete on table "public"."activity_logs" to "service_role";

grant insert on table "public"."activity_logs" to "service_role";

grant references on table "public"."activity_logs" to "service_role";

grant select on table "public"."activity_logs" to "service_role";

grant trigger on table "public"."activity_logs" to "service_role";

grant truncate on table "public"."activity_logs" to "service_role";

grant update on table "public"."activity_logs" to "service_role";

grant delete on table "public"."customization_options" to "anon";

grant insert on table "public"."customization_options" to "anon";

grant references on table "public"."customization_options" to "anon";

grant select on table "public"."customization_options" to "anon";

grant trigger on table "public"."customization_options" to "anon";

grant truncate on table "public"."customization_options" to "anon";

grant update on table "public"."customization_options" to "anon";

grant delete on table "public"."customization_options" to "authenticated";

grant insert on table "public"."customization_options" to "authenticated";

grant references on table "public"."customization_options" to "authenticated";

grant select on table "public"."customization_options" to "authenticated";

grant trigger on table "public"."customization_options" to "authenticated";

grant truncate on table "public"."customization_options" to "authenticated";

grant update on table "public"."customization_options" to "authenticated";

grant delete on table "public"."customization_options" to "service_role";

grant insert on table "public"."customization_options" to "service_role";

grant references on table "public"."customization_options" to "service_role";

grant select on table "public"."customization_options" to "service_role";

grant trigger on table "public"."customization_options" to "service_role";

grant truncate on table "public"."customization_options" to "service_role";

grant update on table "public"."customization_options" to "service_role";

grant delete on table "public"."designs" to "anon";

grant insert on table "public"."designs" to "anon";

grant references on table "public"."designs" to "anon";

grant select on table "public"."designs" to "anon";

grant trigger on table "public"."designs" to "anon";

grant truncate on table "public"."designs" to "anon";

grant update on table "public"."designs" to "anon";

grant delete on table "public"."designs" to "authenticated";

grant insert on table "public"."designs" to "authenticated";

grant references on table "public"."designs" to "authenticated";

grant select on table "public"."designs" to "authenticated";

grant trigger on table "public"."designs" to "authenticated";

grant truncate on table "public"."designs" to "authenticated";

grant update on table "public"."designs" to "authenticated";

grant delete on table "public"."designs" to "service_role";

grant insert on table "public"."designs" to "service_role";

grant references on table "public"."designs" to "service_role";

grant select on table "public"."designs" to "service_role";

grant trigger on table "public"."designs" to "service_role";

grant truncate on table "public"."designs" to "service_role";

grant update on table "public"."designs" to "service_role";

grant delete on table "public"."merchant_products" to "anon";

grant insert on table "public"."merchant_products" to "anon";

grant references on table "public"."merchant_products" to "anon";

grant select on table "public"."merchant_products" to "anon";

grant trigger on table "public"."merchant_products" to "anon";

grant truncate on table "public"."merchant_products" to "anon";

grant update on table "public"."merchant_products" to "anon";

grant delete on table "public"."merchant_products" to "authenticated";

grant insert on table "public"."merchant_products" to "authenticated";

grant references on table "public"."merchant_products" to "authenticated";

grant select on table "public"."merchant_products" to "authenticated";

grant trigger on table "public"."merchant_products" to "authenticated";

grant truncate on table "public"."merchant_products" to "authenticated";

grant update on table "public"."merchant_products" to "authenticated";

grant delete on table "public"."merchant_products" to "service_role";

grant insert on table "public"."merchant_products" to "service_role";

grant references on table "public"."merchant_products" to "service_role";

grant select on table "public"."merchant_products" to "service_role";

grant trigger on table "public"."merchant_products" to "service_role";

grant truncate on table "public"."merchant_products" to "service_role";

grant update on table "public"."merchant_products" to "service_role";

grant delete on table "public"."merchants" to "anon";

grant insert on table "public"."merchants" to "anon";

grant references on table "public"."merchants" to "anon";

grant select on table "public"."merchants" to "anon";

grant trigger on table "public"."merchants" to "anon";

grant truncate on table "public"."merchants" to "anon";

grant update on table "public"."merchants" to "anon";

grant delete on table "public"."merchants" to "authenticated";

grant insert on table "public"."merchants" to "authenticated";

grant references on table "public"."merchants" to "authenticated";

grant select on table "public"."merchants" to "authenticated";

grant trigger on table "public"."merchants" to "authenticated";

grant truncate on table "public"."merchants" to "authenticated";

grant update on table "public"."merchants" to "authenticated";

grant delete on table "public"."merchants" to "service_role";

grant insert on table "public"."merchants" to "service_role";

grant references on table "public"."merchants" to "service_role";

grant select on table "public"."merchants" to "service_role";

grant trigger on table "public"."merchants" to "service_role";

grant truncate on table "public"."merchants" to "service_role";

grant update on table "public"."merchants" to "service_role";

grant delete on table "public"."order_items" to "anon";

grant insert on table "public"."order_items" to "anon";

grant references on table "public"."order_items" to "anon";

grant select on table "public"."order_items" to "anon";

grant trigger on table "public"."order_items" to "anon";

grant truncate on table "public"."order_items" to "anon";

grant update on table "public"."order_items" to "anon";

grant delete on table "public"."order_items" to "authenticated";

grant insert on table "public"."order_items" to "authenticated";

grant references on table "public"."order_items" to "authenticated";

grant select on table "public"."order_items" to "authenticated";

grant trigger on table "public"."order_items" to "authenticated";

grant truncate on table "public"."order_items" to "authenticated";

grant update on table "public"."order_items" to "authenticated";

grant delete on table "public"."order_items" to "service_role";

grant insert on table "public"."order_items" to "service_role";

grant references on table "public"."order_items" to "service_role";

grant select on table "public"."order_items" to "service_role";

grant trigger on table "public"."order_items" to "service_role";

grant truncate on table "public"."order_items" to "service_role";

grant update on table "public"."order_items" to "service_role";

grant delete on table "public"."orders" to "anon";

grant insert on table "public"."orders" to "anon";

grant references on table "public"."orders" to "anon";

grant select on table "public"."orders" to "anon";

grant trigger on table "public"."orders" to "anon";

grant truncate on table "public"."orders" to "anon";

grant update on table "public"."orders" to "anon";

grant delete on table "public"."orders" to "authenticated";

grant insert on table "public"."orders" to "authenticated";

grant references on table "public"."orders" to "authenticated";

grant select on table "public"."orders" to "authenticated";

grant trigger on table "public"."orders" to "authenticated";

grant truncate on table "public"."orders" to "authenticated";

grant update on table "public"."orders" to "authenticated";

grant delete on table "public"."orders" to "service_role";

grant insert on table "public"."orders" to "service_role";

grant references on table "public"."orders" to "service_role";

grant select on table "public"."orders" to "service_role";

grant trigger on table "public"."orders" to "service_role";

grant truncate on table "public"."orders" to "service_role";

grant update on table "public"."orders" to "service_role";

grant delete on table "public"."product_categories" to "anon";

grant insert on table "public"."product_categories" to "anon";

grant references on table "public"."product_categories" to "anon";

grant select on table "public"."product_categories" to "anon";

grant trigger on table "public"."product_categories" to "anon";

grant truncate on table "public"."product_categories" to "anon";

grant update on table "public"."product_categories" to "anon";

grant delete on table "public"."product_categories" to "authenticated";

grant insert on table "public"."product_categories" to "authenticated";

grant references on table "public"."product_categories" to "authenticated";

grant select on table "public"."product_categories" to "authenticated";

grant trigger on table "public"."product_categories" to "authenticated";

grant truncate on table "public"."product_categories" to "authenticated";

grant update on table "public"."product_categories" to "authenticated";

grant delete on table "public"."product_categories" to "service_role";

grant insert on table "public"."product_categories" to "service_role";

grant references on table "public"."product_categories" to "service_role";

grant select on table "public"."product_categories" to "service_role";

grant trigger on table "public"."product_categories" to "service_role";

grant truncate on table "public"."product_categories" to "service_role";

grant update on table "public"."product_categories" to "service_role";

grant delete on table "public"."product_images" to "anon";

grant insert on table "public"."product_images" to "anon";

grant references on table "public"."product_images" to "anon";

grant select on table "public"."product_images" to "anon";

grant trigger on table "public"."product_images" to "anon";

grant truncate on table "public"."product_images" to "anon";

grant update on table "public"."product_images" to "anon";

grant delete on table "public"."product_images" to "authenticated";

grant insert on table "public"."product_images" to "authenticated";

grant references on table "public"."product_images" to "authenticated";

grant select on table "public"."product_images" to "authenticated";

grant trigger on table "public"."product_images" to "authenticated";

grant truncate on table "public"."product_images" to "authenticated";

grant update on table "public"."product_images" to "authenticated";

grant delete on table "public"."product_images" to "service_role";

grant insert on table "public"."product_images" to "service_role";

grant references on table "public"."product_images" to "service_role";

grant select on table "public"."product_images" to "service_role";

grant trigger on table "public"."product_images" to "service_role";

grant truncate on table "public"."product_images" to "service_role";

grant update on table "public"."product_images" to "service_role";

grant delete on table "public"."product_variants" to "anon";

grant insert on table "public"."product_variants" to "anon";

grant references on table "public"."product_variants" to "anon";

grant select on table "public"."product_variants" to "anon";

grant trigger on table "public"."product_variants" to "anon";

grant truncate on table "public"."product_variants" to "anon";

grant update on table "public"."product_variants" to "anon";

grant delete on table "public"."product_variants" to "authenticated";

grant insert on table "public"."product_variants" to "authenticated";

grant references on table "public"."product_variants" to "authenticated";

grant select on table "public"."product_variants" to "authenticated";

grant trigger on table "public"."product_variants" to "authenticated";

grant truncate on table "public"."product_variants" to "authenticated";

grant update on table "public"."product_variants" to "authenticated";

grant delete on table "public"."product_variants" to "service_role";

grant insert on table "public"."product_variants" to "service_role";

grant references on table "public"."product_variants" to "service_role";

grant select on table "public"."product_variants" to "service_role";

grant trigger on table "public"."product_variants" to "service_role";

grant truncate on table "public"."product_variants" to "service_role";

grant update on table "public"."product_variants" to "service_role";

grant delete on table "public"."products" to "anon";

grant insert on table "public"."products" to "anon";

grant references on table "public"."products" to "anon";

grant select on table "public"."products" to "anon";

grant trigger on table "public"."products" to "anon";

grant truncate on table "public"."products" to "anon";

grant update on table "public"."products" to "anon";

grant delete on table "public"."products" to "authenticated";

grant insert on table "public"."products" to "authenticated";

grant references on table "public"."products" to "authenticated";

grant select on table "public"."products" to "authenticated";

grant trigger on table "public"."products" to "authenticated";

grant truncate on table "public"."products" to "authenticated";

grant update on table "public"."products" to "authenticated";

grant delete on table "public"."products" to "service_role";

grant insert on table "public"."products" to "service_role";

grant references on table "public"."products" to "service_role";

grant select on table "public"."products" to "service_role";

grant trigger on table "public"."products" to "service_role";

grant truncate on table "public"."products" to "service_role";

grant update on table "public"."products" to "service_role";

grant delete on table "public"."ticket_messages" to "anon";

grant insert on table "public"."ticket_messages" to "anon";

grant references on table "public"."ticket_messages" to "anon";

grant select on table "public"."ticket_messages" to "anon";

grant trigger on table "public"."ticket_messages" to "anon";

grant truncate on table "public"."ticket_messages" to "anon";

grant update on table "public"."ticket_messages" to "anon";

grant delete on table "public"."ticket_messages" to "authenticated";

grant insert on table "public"."ticket_messages" to "authenticated";

grant references on table "public"."ticket_messages" to "authenticated";

grant select on table "public"."ticket_messages" to "authenticated";

grant trigger on table "public"."ticket_messages" to "authenticated";

grant truncate on table "public"."ticket_messages" to "authenticated";

grant update on table "public"."ticket_messages" to "authenticated";

grant delete on table "public"."ticket_messages" to "service_role";

grant insert on table "public"."ticket_messages" to "service_role";

grant references on table "public"."ticket_messages" to "service_role";

grant select on table "public"."ticket_messages" to "service_role";

grant trigger on table "public"."ticket_messages" to "service_role";

grant truncate on table "public"."ticket_messages" to "service_role";

grant update on table "public"."ticket_messages" to "service_role";

grant delete on table "public"."tickets" to "anon";

grant insert on table "public"."tickets" to "anon";

grant references on table "public"."tickets" to "anon";

grant select on table "public"."tickets" to "anon";

grant trigger on table "public"."tickets" to "anon";

grant truncate on table "public"."tickets" to "anon";

grant update on table "public"."tickets" to "anon";

grant delete on table "public"."tickets" to "authenticated";

grant insert on table "public"."tickets" to "authenticated";

grant references on table "public"."tickets" to "authenticated";

grant select on table "public"."tickets" to "authenticated";

grant trigger on table "public"."tickets" to "authenticated";

grant truncate on table "public"."tickets" to "authenticated";

grant update on table "public"."tickets" to "authenticated";

grant delete on table "public"."tickets" to "service_role";

grant insert on table "public"."tickets" to "service_role";

grant references on table "public"."tickets" to "service_role";

grant select on table "public"."tickets" to "service_role";

grant trigger on table "public"."tickets" to "service_role";

grant truncate on table "public"."tickets" to "service_role";

grant update on table "public"."tickets" to "service_role";

grant delete on table "public"."user_profiles" to "anon";

grant insert on table "public"."user_profiles" to "anon";

grant references on table "public"."user_profiles" to "anon";

grant select on table "public"."user_profiles" to "anon";

grant trigger on table "public"."user_profiles" to "anon";

grant truncate on table "public"."user_profiles" to "anon";

grant update on table "public"."user_profiles" to "anon";

grant delete on table "public"."user_profiles" to "authenticated";

grant insert on table "public"."user_profiles" to "authenticated";

grant references on table "public"."user_profiles" to "authenticated";

grant select on table "public"."user_profiles" to "authenticated";

grant trigger on table "public"."user_profiles" to "authenticated";

grant truncate on table "public"."user_profiles" to "authenticated";

grant update on table "public"."user_profiles" to "authenticated";

grant delete on table "public"."user_profiles" to "service_role";

grant insert on table "public"."user_profiles" to "service_role";

grant references on table "public"."user_profiles" to "service_role";

grant select on table "public"."user_profiles" to "service_role";

grant trigger on table "public"."user_profiles" to "service_role";

grant truncate on table "public"."user_profiles" to "service_role";

grant update on table "public"."user_profiles" to "service_role";

grant delete on table "public"."users" to "anon";

grant insert on table "public"."users" to "anon";

grant references on table "public"."users" to "anon";

grant select on table "public"."users" to "anon";

grant trigger on table "public"."users" to "anon";

grant truncate on table "public"."users" to "anon";

grant update on table "public"."users" to "anon";

grant delete on table "public"."users" to "authenticated";

grant insert on table "public"."users" to "authenticated";

grant references on table "public"."users" to "authenticated";

grant select on table "public"."users" to "authenticated";

grant trigger on table "public"."users" to "authenticated";

grant truncate on table "public"."users" to "authenticated";

grant update on table "public"."users" to "authenticated";

grant delete on table "public"."users" to "service_role";

grant insert on table "public"."users" to "service_role";

grant references on table "public"."users" to "service_role";

grant select on table "public"."users" to "service_role";

grant trigger on table "public"."users" to "service_role";

grant truncate on table "public"."users" to "service_role";

grant update on table "public"."users" to "service_role";

grant delete on table "public"."webhooks" to "anon";

grant insert on table "public"."webhooks" to "anon";

grant references on table "public"."webhooks" to "anon";

grant select on table "public"."webhooks" to "anon";

grant trigger on table "public"."webhooks" to "anon";

grant truncate on table "public"."webhooks" to "anon";

grant update on table "public"."webhooks" to "anon";

grant delete on table "public"."webhooks" to "authenticated";

grant insert on table "public"."webhooks" to "authenticated";

grant references on table "public"."webhooks" to "authenticated";

grant select on table "public"."webhooks" to "authenticated";

grant trigger on table "public"."webhooks" to "authenticated";

grant truncate on table "public"."webhooks" to "authenticated";

grant update on table "public"."webhooks" to "authenticated";

grant delete on table "public"."webhooks" to "service_role";

grant insert on table "public"."webhooks" to "service_role";

grant references on table "public"."webhooks" to "service_role";

grant select on table "public"."webhooks" to "service_role";

grant trigger on table "public"."webhooks" to "service_role";

grant truncate on table "public"."webhooks" to "service_role";

grant update on table "public"."webhooks" to "service_role";

create policy "Merchants can view logs for their account"
on "public"."activity_logs"
as permissive
for select
to public
using ((merchant_id = get_user_merchant_id()));


create policy "Super admins can view all activity logs"
on "public"."activity_logs"
as permissive
for select
to public
using (is_super_admin());


create policy "Users can view their own activity logs"
on "public"."activity_logs"
as permissive
for select
to public
using ((user_id = auth.uid()));


create policy "Anyone can view customization options"
on "public"."customization_options"
as permissive
for select
to public
using (((is_active = true) AND (EXISTS ( SELECT 1
   FROM products p
  WHERE ((p.id = customization_options.product_id) AND (p.is_active = true))))));


create policy "Super admins can manage customization options"
on "public"."customization_options"
as permissive
for all
to public
using (is_super_admin());


create policy "Anyone can view public designs"
on "public"."designs"
as permissive
for select
to public
using ((is_public = true));


create policy "Customers can create designs"
on "public"."designs"
as permissive
for insert
to public
with check ((customer_email = (auth.jwt() ->> 'email'::text)));


create policy "Customers can update their own designs"
on "public"."designs"
as permissive
for update
to public
using ((customer_email = (auth.jwt() ->> 'email'::text)));


create policy "Customers can view their own designs"
on "public"."designs"
as permissive
for select
to public
using ((customer_email = (auth.jwt() ->> 'email'::text)));


create policy "Merchants can view designs for their products"
on "public"."designs"
as permissive
for select
to public
using ((merchant_id = get_user_merchant_id()));


create policy "Super admins can view all designs"
on "public"."designs"
as permissive
for select
to public
using (is_super_admin());


create policy "Anyone can view published merchant products"
on "public"."merchant_products"
as permissive
for select
to public
using ((is_published = true));


create policy "Merchants can manage their own products"
on "public"."merchant_products"
as permissive
for all
to public
using ((merchant_id = get_user_merchant_id()));


create policy "Merchants can view their own products"
on "public"."merchant_products"
as permissive
for select
to public
using ((merchant_id = get_user_merchant_id()));


create policy "Super admins can view all merchant products"
on "public"."merchant_products"
as permissive
for select
to public
using (is_super_admin());


create policy "Merchants can update their own record"
on "public"."merchants"
as permissive
for update
to public
using ((auth.uid() = user_id));


create policy "Merchants can view their own record"
on "public"."merchants"
as permissive
for select
to public
using ((auth.uid() = user_id));


create policy "Service role can insert merchants"
on "public"."merchants"
as permissive
for insert
to public
with check (true);


create policy "Service role can update merchants"
on "public"."merchants"
as permissive
for update
to public
using (true);


create policy "Super admins can view all merchants"
on "public"."merchants"
as permissive
for all
to public
using (is_super_admin());


create policy "Customers can view their order items"
on "public"."order_items"
as permissive
for select
to public
using ((EXISTS ( SELECT 1
   FROM orders o
  WHERE ((o.id = order_items.order_id) AND (o.customer_email = (auth.jwt() ->> 'email'::text))))));


create policy "Merchants can view their order items"
on "public"."order_items"
as permissive
for select
to public
using ((EXISTS ( SELECT 1
   FROM orders o
  WHERE ((o.id = order_items.order_id) AND (o.merchant_id = get_user_merchant_id())))));


create policy "Super admins can view all order items"
on "public"."order_items"
as permissive
for select
to public
using (is_super_admin());


create policy "Customers can view their own orders"
on "public"."orders"
as permissive
for select
to public
using ((customer_email = (auth.jwt() ->> 'email'::text)));


create policy "Merchants can update their own orders"
on "public"."orders"
as permissive
for update
to public
using ((merchant_id = get_user_merchant_id()));


create policy "Merchants can view their own orders"
on "public"."orders"
as permissive
for select
to public
using ((merchant_id = get_user_merchant_id()));


create policy "Super admins can view all orders"
on "public"."orders"
as permissive
for all
to public
using (is_super_admin());


create policy "Anyone can view active categories"
on "public"."product_categories"
as permissive
for select
to public
using ((is_active = true));


create policy "Super admins can manage categories"
on "public"."product_categories"
as permissive
for all
to public
using (is_super_admin());


create policy "Anyone can view product images"
on "public"."product_images"
as permissive
for select
to public
using ((EXISTS ( SELECT 1
   FROM products p
  WHERE ((p.id = product_images.product_id) AND (p.is_active = true)))));


create policy "Super admins can manage product images"
on "public"."product_images"
as permissive
for all
to public
using (is_super_admin());


create policy "Merchants can manage their product variants"
on "public"."product_variants"
as permissive
for all
to public
using ((EXISTS ( SELECT 1
   FROM (merchant_products mp
     JOIN merchants m ON ((mp.merchant_id = m.id)))
  WHERE ((mp.id = product_variants.merchant_product_id) AND (m.user_id = auth.uid())))));


create policy "Super admins can manage all product variants"
on "public"."product_variants"
as permissive
for all
to public
using ((EXISTS ( SELECT 1
   FROM users u
  WHERE ((u.id = auth.uid()) AND (u.role = 'super_admin'::user_role)))));


create policy "Users can view product variants"
on "public"."product_variants"
as permissive
for select
to public
using ((EXISTS ( SELECT 1
   FROM (merchant_products mp
     JOIN merchants m ON ((mp.merchant_id = m.id)))
  WHERE ((mp.id = product_variants.merchant_product_id) AND ((m.user_id = auth.uid()) OR (EXISTS ( SELECT 1
           FROM users u
          WHERE ((u.id = auth.uid()) AND (u.role = 'super_admin'::user_role)))))))));


create policy "Anyone can view active products"
on "public"."products"
as permissive
for select
to public
using ((is_active = true));


create policy "Super admins can manage products"
on "public"."products"
as permissive
for all
to public
using (is_super_admin());


create policy "Merchants can view messages for their tickets"
on "public"."ticket_messages"
as permissive
for select
to public
using ((EXISTS ( SELECT 1
   FROM tickets t
  WHERE ((t.id = ticket_messages.ticket_id) AND (t.merchant_id = get_user_merchant_id())))));


create policy "Super admins can view all ticket messages"
on "public"."ticket_messages"
as permissive
for all
to public
using (is_super_admin());


create policy "Users can create messages for their tickets"
on "public"."ticket_messages"
as permissive
for insert
to public
with check (((user_id = auth.uid()) AND (EXISTS ( SELECT 1
   FROM tickets t
  WHERE ((t.id = ticket_messages.ticket_id) AND (t.user_id = auth.uid()))))));


create policy "Users can view messages for their tickets"
on "public"."ticket_messages"
as permissive
for select
to public
using ((EXISTS ( SELECT 1
   FROM tickets t
  WHERE ((t.id = ticket_messages.ticket_id) AND (t.user_id = auth.uid())))));


create policy "Merchants can view tickets for their account"
on "public"."tickets"
as permissive
for select
to public
using ((merchant_id = get_user_merchant_id()));


create policy "Super admins can view all tickets"
on "public"."tickets"
as permissive
for all
to public
using (is_super_admin());


create policy "Users can create tickets"
on "public"."tickets"
as permissive
for insert
to public
with check ((user_id = auth.uid()));


create policy "Users can update their own tickets"
on "public"."tickets"
as permissive
for update
to public
using ((user_id = auth.uid()));


create policy "Users can view their own tickets"
on "public"."tickets"
as permissive
for select
to public
using ((user_id = auth.uid()));


create policy "Service role can insert user profiles"
on "public"."user_profiles"
as permissive
for insert
to public
with check (true);


create policy "Super admins can view all profiles"
on "public"."user_profiles"
as permissive
for select
to public
using (is_super_admin());


create policy "Users can insert their own profile"
on "public"."user_profiles"
as permissive
for insert
to public
with check ((auth.uid() = user_id));


create policy "Users can update their own profile"
on "public"."user_profiles"
as permissive
for update
to public
using ((auth.uid() = user_id));


create policy "Users can view their own profile"
on "public"."user_profiles"
as permissive
for select
to public
using ((auth.uid() = user_id));


create policy "Service role can insert users"
on "public"."users"
as permissive
for insert
to public
with check (true);


create policy "Super admins can view all users"
on "public"."users"
as permissive
for select
to public
using (is_super_admin());


create policy "Users can update their own record"
on "public"."users"
as permissive
for update
to public
using ((auth.uid() = id));


create policy "Users can view their own record"
on "public"."users"
as permissive
for select
to public
using ((auth.uid() = id));


create policy "Merchants can manage their own webhooks"
on "public"."webhooks"
as permissive
for all
to public
using ((merchant_id = get_user_merchant_id()));


create policy "Super admins can view all webhooks"
on "public"."webhooks"
as permissive
for select
to public
using (is_super_admin());


CREATE TRIGGER update_customization_options_updated_at BEFORE UPDATE ON public.customization_options FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_designs_updated_at BEFORE UPDATE ON public.designs FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_merchant_products_updated_at BEFORE UPDATE ON public.merchant_products FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_merchants_updated_at BEFORE UPDATE ON public.merchants FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trigger_recalculate_order_totals AFTER INSERT OR DELETE OR UPDATE ON public.order_items FOR EACH ROW EXECUTE FUNCTION recalculate_order_totals();

CREATE TRIGGER update_order_items_updated_at BEFORE UPDATE ON public.order_items FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trigger_log_order_status_change AFTER UPDATE ON public.orders FOR EACH ROW EXECUTE FUNCTION log_order_status_change();

CREATE TRIGGER trigger_set_order_number BEFORE INSERT ON public.orders FOR EACH ROW EXECUTE FUNCTION set_order_number();

CREATE TRIGGER update_orders_updated_at BEFORE UPDATE ON public.orders FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_product_categories_updated_at BEFORE UPDATE ON public.product_categories FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_product_variants_updated_at BEFORE UPDATE ON public.product_variants FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_products_updated_at BEFORE UPDATE ON public.products FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trigger_set_ticket_number BEFORE INSERT ON public.tickets FOR EACH ROW EXECUTE FUNCTION set_ticket_number();

CREATE TRIGGER update_tickets_updated_at BEFORE UPDATE ON public.tickets FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_profiles_updated_at BEFORE UPDATE ON public.user_profiles FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON public.users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_webhooks_updated_at BEFORE UPDATE ON public.webhooks FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();


