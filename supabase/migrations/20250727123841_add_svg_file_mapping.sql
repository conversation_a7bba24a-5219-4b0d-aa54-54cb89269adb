-- Add SVG file-based mapping system
-- This allows uploading SVG files to define complex mapping areas

-- Add columns to products table for SVG mapping files
ALTER TABLE public.products
ADD COLUMN IF NOT EXISTS mockup_svg_file JSONB,
ADD COLUMN IF NOT EXISTS print_svg_file JSONB;

-- Add comments to explain the new columns
COMMENT ON COLUMN public.products.mockup_svg_file IS 'SVG file that defines mapping areas for mockup view - {bucket: string, filePath: string}';
COMMENT ON COLUMN public.products.print_svg_file IS 'SVG file that defines mapping areas for print file view - {bucket: string, filePath: string}';

-- Update the product_mapping_areas table to support SVG-based mapping
ALTER TABLE public.product_mapping_areas
ADD COLUMN IF NOT EXISTS svg_element_id TEXT,
ADD COLUMN IF NOT EXISTS svg_path_data TEXT;

-- Add comments for new columns
COMMENT ON COLUMN public.product_mapping_areas.svg_element_id IS 'ID of the SVG element in the uploaded SVG file (e.g., "front-area", "back-area")';
COMMENT ON COLUMN public.product_mapping_areas.svg_path_data IS 'The actual SVG path data extracted from the uploaded SVG file';

-- Create index for faster lookups
CREATE INDEX IF NOT EXISTS idx_product_mapping_areas_svg_element_id
ON public.product_mapping_areas(product_id, svg_element_id);

-- Add a new table to store SVG mapping configurations
CREATE TABLE IF NOT EXISTS public.svg_mapping_configs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    product_id UUID NOT NULL REFERENCES public.products(id) ON DELETE CASCADE,
    view_type TEXT NOT NULL CHECK (view_type IN ('mockup', 'printfile')),
    svg_file JSONB NOT NULL, -- {bucket: string, filePath: string}
    svg_content TEXT, -- The actual SVG content for parsing
    svg_viewbox TEXT, -- SVG viewBox for coordinate calculations
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),

    UNIQUE(product_id, view_type)
);

-- Add RLS policies for svg_mapping_configs
ALTER TABLE public.svg_mapping_configs ENABLE ROW LEVEL SECURITY;


CREATE POLICY "Anonymous view active svg configs" ON public.svg_mapping_configs
    FOR SELECT
    TO anon
    USING (
        EXISTS (
            SELECT 1 FROM public.products p
            WHERE p.id = svg_mapping_configs.product_id
            AND p.is_active = true
        )
    );

-- Grant permissions
GRANT ALL ON public.svg_mapping_configs TO authenticated;
GRANT SELECT ON public.svg_mapping_configs TO anon;