-- Add canvas configuration columns to products table
-- These columns store the unified canvas configuration for both mockup and print file views

-- Add canvas configuration columns if they don't exist
DO $$
BEGIN
    -- Add canvas_width column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                   WHERE table_name = 'products' AND column_name = 'canvas_width') THEN
        ALTER TABLE public.products ADD COLUMN canvas_width INTEGER DEFAULT 3000;
    END IF;

    -- Add canvas_height column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                   WHERE table_name = 'products' AND column_name = 'canvas_height') THEN
        ALTER TABLE public.products ADD COLUMN canvas_height INTEGER DEFAULT 4000;
    END IF;

    -- Add canvas_dpi column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                   WHERE table_name = 'products' AND column_name = 'canvas_dpi') THEN
        ALTER TABLE public.products ADD COLUMN canvas_dpi INTEGER DEFAULT 300;
    END IF;
END $$;

-- Add comments to document the columns
COMMENT ON COLUMN public.products.canvas_width IS 'Unified canvas width in pixels for both mockup and print file views';
COMMENT ON COLUMN public.products.canvas_height IS 'Unified canvas height in pixels for both mockup and print file views';
COMMENT ON COLUMN public.products.canvas_dpi IS 'Unified DPI (dots per inch) for both mockup and print file views';