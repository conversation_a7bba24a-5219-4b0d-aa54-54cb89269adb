-- Fix RLS policies for svg_mapping_configs table
-- The current policies are too restrictive and causing 401/406 errors

-- Drop existing policies to start fresh
DROP POLICY IF EXISTS "Authenticated users full access svg configs" ON public.svg_mapping_configs;
DROP POLICY IF EXISTS "Anonymous view active svg configs" ON public.svg_mapping_configs;

-- Ensure RLS is enabled
ALTER TABLE public.svg_mapping_configs ENABLE ROW LEVEL SECURITY;

-- Create simple, permissive policies that match the working product_mapping_areas policies
CREATE POLICY "Authenticated users full access svg configs" ON public.svg_mapping_configs
    FOR ALL
    TO authenticated
    USING (true)
    WITH CHECK (true);

-- Ensure proper grants (these might be missing)
GRANT ALL ON public.svg_mapping_configs TO authenticated;
GRANT SELECT ON public.svg_mapping_configs TO anon;
GRANT USAGE ON SCHEMA public TO authenticated;
GRANT USAGE ON SCHEMA public TO anon;

-- Also ensure the sequence permissions if there are any
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO authenticated;
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO anon;