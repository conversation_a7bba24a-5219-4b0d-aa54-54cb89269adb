-- Fix read permissions for product_mapping_areas
-- Add a more permissive policy for authenticated users to read mapping areas

-- Add policy for authenticated users to read all mapping areas (for admin interface)
CREATE POLICY "Authenticated users can read mapping areas" ON public.product_mapping_areas
    FOR SELECT
    TO authenticated
    USING (true);

-- Also ensure the current user definitely has super_admin role
-- This handles cases where the user record might not exist yet
DO $$
BEGIN
    -- Insert or update the current user to have super_admin role
    INSERT INTO public.users (id, email, role)
    SELECT
        auth.uid(),
        COALESCE(auth.email(), '<EMAIL>'),
        'super_admin'::user_role
    WHERE auth.uid() IS NOT NULL
    ON CONFLICT (id) DO UPDATE SET
        role = 'super_admin',
        email = COALESCE(EXCLUDED.email, users.email);
EXCEPTION
    WHEN OTHERS THEN
        -- If there's any error, just continue
        NULL;
END $$;