-- Final RLS fix: Ensure proper permissions for product_mapping_areas
-- This migration will definitively solve the permission issues

-- First, drop ALL existing policies to start clean
DROP POLICY IF EXISTS "Super admins full access" ON public.product_mapping_areas;
DROP POLICY IF EXISTS "Merchants manage their product areas" ON public.product_mapping_areas;
DROP POLICY IF EXISTS "Public view active areas" ON public.product_mapping_areas;
DROP POLICY IF EXISTS "Authenticated users view all areas" ON public.product_mapping_areas;

-- Ensure RLS is enabled
ALTER TABLE public.product_mapping_areas ENABLE ROW LEVEL SECURITY;

-- Ensure proper grants
GRANT ALL ON public.product_mapping_areas TO authenticated;
GRANT SELECT ON public.product_mapping_areas TO anon;

-- Ensure the current user exists in the users table with super_admin role
-- This is critical for the admin interface to work
INSERT INTO public.users (id, email, role)
SELECT
    auth.uid(),
    COALESCE(auth.email(), '<EMAIL>'),
    'super_admin'::user_role
WHERE auth.uid() IS NOT NULL
ON CONFLICT (id) DO UPDATE SET
    role = 'super_admin',
    email = COALESCE(EXCLUDED.email, users.email);