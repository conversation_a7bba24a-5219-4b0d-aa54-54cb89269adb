-- Comprehensive fix for all RLS permission issues
-- This migration ensures all tables have proper permissions for development

-- Disable <PERSON><PERSON> on all relevant tables temporarily for development
ALTER TABLE public.products DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.product_mapping_areas DISABLE ROW LEVEL SECURITY;

-- Drop any existing RLS policies that might be causing issues
DROP POLICY IF EXISTS "Enable read access for all users" ON public.products;
DROP POLICY IF EXISTS "Enable insert for authenticated users only" ON public.products;
DROP POLICY IF EXISTS "Enable update for authenticated users only" ON public.products;
DROP POLICY IF EXISTS "Enable delete for authenticated users only" ON public.products;

DROP POLICY IF EXISTS "Enable read access for all users" ON public.product_mapping_areas;
DROP POLICY IF EXISTS "Enable insert for authenticated users only" ON public.product_mapping_areas;
DROP POLICY IF EXISTS "Enable update for authenticated users only" ON public.product_mapping_areas;
DROP POLICY IF EXISTS "Enable delete for authenticated users only" ON public.product_mapping_areas;

-- Grant full permissions to anon and authenticated roles
GRANT ALL ON public.products TO anon;
GRANT ALL ON public.products TO authenticated;
GRANT ALL ON public.product_mapping_areas TO anon;
GRANT ALL ON public.product_mapping_areas TO authenticated;

-- Grant usage on sequences if they exist
GRANT USAGE ON ALL SEQUENCES IN SCHEMA public TO anon;
GRANT USAGE ON ALL SEQUENCES IN SCHEMA public TO authenticated;

-- Add comments to track this change
COMMENT ON TABLE public.products IS 'RLS disabled and full permissions granted for development - 2025-01-27';
COMMENT ON TABLE public.product_mapping_areas IS 'RLS disabled and full permissions granted for development - 2025-01-27';