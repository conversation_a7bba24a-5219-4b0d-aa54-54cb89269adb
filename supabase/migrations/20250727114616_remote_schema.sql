drop policy "Merchants can view mapping areas" on "public"."product_mapping_areas";

drop policy "Super admin can manage mapping areas" on "public"."product_mapping_areas";

create policy "Merchants can view mapping areas"
on "public"."product_mapping_areas"
as permissive
for select
to public
using ((EXISTS ( SELECT 1
   FROM (merchants m
     JOIN merchant_products mp ON ((m.id = mp.merchant_id)))
  WHERE ((mp.product_id = product_mapping_areas.product_id) AND (m.user_id = auth.uid())))));


create policy "Super admin can manage mapping areas"
on "public"."product_mapping_areas"
as permissive
for all
to public
using ((EXISTS ( SELECT 1
   FROM users
  WHERE ((users.id = auth.uid()) AND (users.role = 'super_admin'::user_role)))));



