-- Add SVG mapping support to products
-- This migration adds support for SVG-based area mapping for precise design element positioning

-- Check if products table exists, if not create a basic version
DO $$
BEGIN
    IF NOT EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'products') THEN
        CREATE TABLE public.products (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            name TEXT NOT NULL,
            description TEXT,
            slug TEXT NOT NULL UNIQUE,
            base_price DECIMAL(10,2) NOT NULL DEFAULT 0,
            category_id UUID,
            tags TEXT[] DEFAULT '{}',
            sku TEXT UNIQUE,
            weight DECIMAL(8,2),
            dimensions JSONB,
            materials TEXT[],
            care_instructions TEXT,
            is_active BOOLEAN DEFAULT true,
            featured BOOLEAN DEFAULT false,
            sort_order INTEGER DEFAULT 0,
            mockup_files JSONB,
            print_files JSONB,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
    END IF;
END $$;

-- Add canvas configuration to products table (safe add)
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_schema = 'public'
        AND table_name = 'products'
        AND column_name = 'canvas_config'
    ) THEN
        ALTER TABLE public.products ADD COLUMN canvas_config JSONB;
    END IF;
END $$;

-- Add mapping areas table for SVG-based area mapping
CREATE TABLE IF NOT EXISTS public.product_mapping_areas (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    product_id UUID NOT NULL REFERENCES public.products(id) ON DELETE CASCADE,
    area_id TEXT NOT NULL,           -- 'front', 'back', 'left-sleeve', etc.
    area_name TEXT NOT NULL,         -- 'Front Design Area', 'Back Design Area'
    view_type TEXT NOT NULL CHECK (view_type IN ('mockup', 'printfile')),
    svg_path TEXT NOT NULL,          -- SVG path defining the area boundary
    display_bounds JSONB NOT NULL,   -- {x, y, width, height} for display canvas
    print_bounds JSONB NOT NULL,     -- {x, y, width, height} for print file
    transform_matrix JSONB,          -- Optional transformation matrix
    sort_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(product_id, area_id, view_type)
);

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_product_mapping_areas_product_id ON public.product_mapping_areas(product_id);
CREATE INDEX IF NOT EXISTS idx_product_mapping_areas_view_type ON public.product_mapping_areas(view_type);
CREATE INDEX IF NOT EXISTS idx_product_mapping_areas_area_id ON public.product_mapping_areas(area_id);

-- Add RLS policies
ALTER TABLE public.product_mapping_areas ENABLE ROW LEVEL SECURITY;

-- Super admin can manage all mapping areas
DROP POLICY IF EXISTS "Super admin can manage mapping areas" ON public.product_mapping_areas;
CREATE POLICY "Super admin can manage mapping areas" ON public.product_mapping_areas
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.users
            WHERE users.id = auth.uid()
            AND users.role = 'super_admin'
        )
    );

-- Merchants can view mapping areas for their products
DROP POLICY IF EXISTS "Merchants can view mapping areas" ON public.product_mapping_areas;
CREATE POLICY "Merchants can view mapping areas" ON public.product_mapping_areas
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.merchants m
            JOIN public.merchant_products mp ON m.id = mp.merchant_id
            WHERE mp.product_id = product_mapping_areas.product_id
            AND m.user_id = auth.uid()
        )
    );

-- Public can view mapping areas for active products
DROP POLICY IF EXISTS "Public can view active mapping areas" ON public.product_mapping_areas;
CREATE POLICY "Public can view active mapping areas" ON public.product_mapping_areas
    FOR SELECT USING (
        is_active = true
        AND EXISTS (
            SELECT 1 FROM public.products p
            WHERE p.id = product_mapping_areas.product_id
            AND p.is_active = true
        )
    );

-- Add comments for documentation
COMMENT ON TABLE public.product_mapping_areas IS 'SVG-based mapping areas for precise design element positioning between mockup and print file views';
COMMENT ON COLUMN public.product_mapping_areas.area_id IS 'Unique identifier for the area (e.g., front, back, left-sleeve)';
COMMENT ON COLUMN public.product_mapping_areas.view_type IS 'Whether this area is for mockup or printfile view';
COMMENT ON COLUMN public.product_mapping_areas.svg_path IS 'SVG path definition for the clickable/designable area';
COMMENT ON COLUMN public.product_mapping_areas.display_bounds IS 'Bounding box coordinates for display canvas';
COMMENT ON COLUMN public.product_mapping_areas.print_bounds IS 'Bounding box coordinates for print file (300 DPI)';
COMMENT ON COLUMN public.product_mapping_areas.transform_matrix IS 'Optional transformation matrix for coordinate conversion';

-- Add comment to products table canvas_config column (safe)
DO $$
BEGIN
    IF EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_schema = 'public'
        AND table_name = 'products'
        AND column_name = 'canvas_config'
    ) THEN
        COMMENT ON COLUMN public.products.canvas_config IS 'Canvas configuration including print dimensions, DPI, and display scaling';
    END IF;
END $$;

-- Add trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

DROP TRIGGER IF EXISTS update_product_mapping_areas_updated_at ON public.product_mapping_areas;
CREATE TRIGGER update_product_mapping_areas_updated_at
    BEFORE UPDATE ON public.product_mapping_areas
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();