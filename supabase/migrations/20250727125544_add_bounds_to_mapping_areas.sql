-- Add bounds columns to product_mapping_areas for faster lookups and better UI
-- These store the bounding box of each mapping area

ALTER TABLE public.product_mapping_areas
ADD COLUMN IF NOT EXISTS bounds_x NUMERIC,
ADD COLUMN IF NOT EXISTS bounds_y NUMERIC,
ADD COLUMN IF NOT EXISTS bounds_width NUMERIC,
ADD COLUMN IF NOT EXISTS bounds_height NUMERIC;

-- Add comments to explain the new columns
COMMENT ON COLUMN public.product_mapping_areas.bounds_x IS 'X coordinate of the bounding box top-left corner';
COMMENT ON COLUMN public.product_mapping_areas.bounds_y IS 'Y coordinate of the bounding box top-left corner';
COMMENT ON COLUMN public.product_mapping_areas.bounds_width IS 'Width of the bounding box';
COMMENT ON COLUMN public.product_mapping_areas.bounds_height IS 'Height of the bounding box';

-- Create index for spatial queries
CREATE INDEX IF NOT EXISTS idx_product_mapping_areas_bounds
ON public.product_mapping_areas(product_id, view_type, bounds_x, bounds_y);