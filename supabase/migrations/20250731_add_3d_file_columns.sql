-- Add 3D file columns to products table for enhanced customizer support
-- This adds support for GLB files, texture files, and preview files

-- Add new columns to products table
ALTER TABLE public.products
ADD COLUMN IF NOT EXISTS glb_files JSONB DEFAULT '[]'::jsonb,
ADD COLUMN IF NOT EXISTS texture_files JSONB DEFAULT '[]'::jsonb,
ADD COLUMN IF NOT EXISTS preview_files JSONB DEFAULT '[]'::jsonb;

-- Add comments to explain the new columns
COMMENT ON COLUMN public.products.glb_files IS 'Array of GLB/GLTF files for 3D customizer - [{url: string, filename: string, file_type: string, file_size: number}]';
COMMENT ON COLUMN public.products.texture_files IS 'Array of texture files for 3D models - [{url: string, filename: string, file_type: string, file_size: number}]';
COMMENT ON COLUMN public.products.preview_files IS 'Array of preview images for 3D scenes - [{url: string, filename: string, file_type: string, file_size: number}]';

-- Create indexes for better performance when querying products with 3D files
CREATE INDEX IF NOT EXISTS idx_products_glb_files ON public.products USING GIN (glb_files);
CREATE INDEX IF NOT EXISTS idx_products_texture_files ON public.products USING GIN (texture_files);
CREATE INDEX IF NOT EXISTS idx_products_preview_files ON public.products USING GIN (preview_files);
