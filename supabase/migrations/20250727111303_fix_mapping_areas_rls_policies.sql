-- Fix RLS policies for product_mapping_areas to use existing is_super_admin() function

-- Drop existing policies
DROP POLICY IF EXISTS "Super admin can manage mapping areas" ON public.product_mapping_areas;
DROP POLICY IF EXISTS "Merchants can view mapping areas" ON public.product_mapping_areas;
DROP POLICY IF EXISTS "Public can view active mapping areas" ON public.product_mapping_areas;

-- Recreate policies with correct syntax
CREATE POLICY "Super admin can manage mapping areas" ON public.product_mapping_areas
    FOR ALL USING (is_super_admin());

CREATE POLICY "Merchants can view mapping areas" ON public.product_mapping_areas
    FOR SELECT USING (
        is_super_admin() OR
        EXISTS (
            SELECT 1 FROM public.merchants m
            JOIN public.merchant_products mp ON m.id = mp.merchant_id
            WHERE mp.product_id = product_mapping_areas.product_id
            AND m.user_id = auth.uid()
        )
    );

CREATE POLICY "Public can view active mapping areas" ON public.product_mapping_areas
    FOR SELECT USING (
        is_active = true
        AND EXISTS (
            SELECT 1 FROM public.products p
            WHERE p.id = product_mapping_areas.product_id
            AND p.is_active = true
        )
    );