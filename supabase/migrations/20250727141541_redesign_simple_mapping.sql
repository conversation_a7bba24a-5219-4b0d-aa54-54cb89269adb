-- Redesign mapping system for simplified visual interface
-- Clean up and optimize for the new simple mapping setup

-- Add unified canvas configuration to products table
ALTER TABLE public.products
ADD COLUMN IF NOT EXISTS canvas_width INTEGER DEFAULT 3000,
ADD COLUMN IF NOT EXISTS canvas_height INTEGER DEFAULT 4000,
ADD COLUMN IF NOT EXISTS canvas_dpi INTEGER DEFAULT 300;

-- Add comments
COMMENT ON COLUMN public.products.canvas_width IS 'Unified canvas width for both mockup and print file views';
COMMENT ON COLUMN public.products.canvas_height IS 'Unified canvas height for both mockup and print file views';
COMMENT ON COLUMN public.products.canvas_dpi IS 'Unified DPI for both mockup and print file views';

-- Simplify product_mapping_areas table - remove unnecessary columns
ALTER TABLE public.product_mapping_areas
DROP COLUMN IF EXISTS svg_element_id,
DROP COLUMN IF EXISTS svg_path_data;

-- Add connection tracking for linked areas
ALTER TABLE public.product_mapping_areas
ADD COLUMN IF NOT EXISTS connected_area_id TEXT,
ADD COLUMN IF NOT EXISTS is_connected BOOLEAN DEFAULT false;

-- Add comments for new columns
COMMENT ON COLUMN public.product_mapping_areas.connected_area_id IS 'ID of the corresponding area in the other view (mockup <-> printfile)';
COMMENT ON COLUMN public.product_mapping_areas.is_connected IS 'Whether this area is properly connected to its counterpart';

-- Create index for connection lookups
CREATE INDEX IF NOT EXISTS idx_product_mapping_areas_connections
ON public.product_mapping_areas(product_id, area_id, view_type);

-- Create a function to automatically link areas with matching area_ids
CREATE OR REPLACE FUNCTION link_mapping_areas()
RETURNS TRIGGER AS $$
BEGIN
    -- When a new area is inserted, try to find and link its counterpart
    UPDATE public.product_mapping_areas
    SET
        connected_area_id = NEW.area_id,
        is_connected = true
    WHERE
        product_id = NEW.product_id
        AND area_id = NEW.area_id
        AND view_type != NEW.view_type;

    -- Update the new area if a counterpart exists
    IF FOUND THEN
        NEW.connected_area_id := NEW.area_id;
        NEW.is_connected := true;
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for automatic area linking
DROP TRIGGER IF EXISTS trigger_link_mapping_areas ON public.product_mapping_areas;
CREATE TRIGGER trigger_link_mapping_areas
    BEFORE INSERT ON public.product_mapping_areas
    FOR EACH ROW
    EXECUTE FUNCTION link_mapping_areas();