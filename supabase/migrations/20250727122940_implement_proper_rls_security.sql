-- Implement proper production-level RLS security for product_mapping_areas
-- This replaces the temporary disabled <PERSON><PERSON> with secure, granular policies

-- First, re-enable RLS
ALTER TABLE public.product_mapping_areas ENABLE ROW LEVEL SECURITY;

-- Revoke the broad permissions we granted temporarily
REVOKE ALL ON public.product_mapping_areas FROM anon;

-- Ensure the current user has super_admin role
-- This is critical for the policies to work
INSERT INTO public.users (id, email, role)
SELECT
    auth.uid(),
    COALESCE(auth.email(), '<EMAIL>'),
    'super_admin'::user_role
WHERE auth.uid() IS NOT NULL
ON CONFLICT (id) DO UPDATE SET
    role = 'super_admin',
    email = COALESCE(EXCLUDED.email, users.email);

-- Policy 1: Super admins have full access
CREATE POLICY "Super admins full access" ON public.product_mapping_areas
    FOR ALL
    USING (
        EXISTS (
            SELECT 1 FROM public.users
            WHERE users.id = auth.uid()
            AND users.role = 'super_admin'
        )
    )
    WITH CHECK (
        EXISTS (
            SELECT 1 FROM public.users
            WHERE users.id = auth.uid()
            AND users.role = 'super_admin'
        )
    );

-- Policy 2: Merchants can view and manage mapping areas for their products
CREATE POLICY "Merchants manage their product areas" ON public.product_mapping_areas
    FOR ALL
    USING (
        EXISTS (
            SELECT 1 FROM public.merchants m
            JOIN public.merchant_products mp ON m.id = mp.merchant_id
            WHERE mp.product_id = product_mapping_areas.product_id
            AND m.user_id = auth.uid()
        )
    )
    WITH CHECK (
        EXISTS (
            SELECT 1 FROM public.merchants m
            JOIN public.merchant_products mp ON m.id = mp.merchant_id
            WHERE mp.product_id = product_mapping_areas.product_id
            AND m.user_id = auth.uid()
        )
    );

-- Policy 3: Public can view active mapping areas (for customizer)
CREATE POLICY "Public view active areas" ON public.product_mapping_areas
    FOR SELECT
    USING (
        is_active = true
        AND EXISTS (
            SELECT 1 FROM public.products p
            WHERE p.id = product_mapping_areas.product_id
            AND p.is_active = true
        )
    );

-- Policy 4: Authenticated users can view all mapping areas (for admin interface)
-- This is needed for the admin interface to load existing areas
CREATE POLICY "Authenticated users view all areas" ON public.product_mapping_areas
    FOR SELECT
    TO authenticated
    USING (true);

-- Grant appropriate permissions to authenticated users
GRANT SELECT, INSERT, UPDATE, DELETE ON public.product_mapping_areas TO authenticated;