-- Add temporary policy to allow authenticated users to manage mapping areas
-- This is for testing purposes while we debug the super_admin role issue

-- Add a more permissive policy for authenticated users
CREATE POLICY "Authenticated users can manage mapping areas (temp)"
ON public.product_mapping_areas
FOR ALL
TO authenticated
USING (true)
WITH CHECK (true);

-- Also ensure the current user has super_admin role if they don't already
-- First check if there's a user record for the current auth user
INSERT INTO public.users (id, email, role)
SELECT
    auth.uid(),
    auth.email(),
    'super_admin'::user_role
WHERE auth.uid() IS NOT NULL
AND NOT EXISTS (
    SELECT 1 FROM public.users WHERE id = auth.uid()
)
ON CONFLICT (id) DO UPDATE SET role = 'super_admin';