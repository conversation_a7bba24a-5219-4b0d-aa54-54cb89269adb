# Biypod Customizer Database Schema

This document describes the database schema for the Biypod Customizer application, a multi-tenant Shopify app for print-on-demand product customization.

## Overview

The database is designed to support three user roles:
- **Super Admin**: Full system access and management
- **Merchant**: Shopify store owners who install the app
- **Customer**: End users who customize products

## Core Tables

### Users & Authentication

#### `users`
Extends Supabase auth.users with role-based access control.
- `id` (UUID, PK): References auth.users(id)
- `email` (TEXT): User's email address
- `role` (ENUM): 'super_admin', 'merchant', 'customer'
- `created_at`, `updated_at` (TIMESTAMP)

#### `user_profiles`
Additional profile information for users.
- `id` (UUID, PK)
- `user_id` (UUID, FK): References users(id)
- `first_name`, `last_name` (TEXT)
- `avatar_url` (TEXT): Profile picture URL
- `phone`, `company` (TEXT)
- `created_at`, `updated_at` (TIMESTAMP)

#### `merchants`
Shopify merchant information and settings.
- `id` (UUID, PK)
- `user_id` (UUID, FK): References users(id)
- `shop_domain` (TEXT): Shopify shop domain
- `shop_name` (TEXT): Display name of the shop
- `access_token` (TEXT): Shopify API access token
- `subscription_tier` (ENUM): 'free', 'basic', 'pro', 'enterprise'
- `subscription_status` (ENUM): 'active', 'inactive', 'cancelled', 'past_due', 'trialing'
- `trial_ends_at` (TIMESTAMP): End of trial period
- `billing_address` (JSONB): Billing information
- `settings` (JSONB): Merchant preferences
- `webhook_url` (TEXT): Custom webhook endpoint
- `is_active` (BOOLEAN): Account status

### Products & Catalog

#### `product_categories`
Product categorization system.
- `id` (UUID, PK)
- `name` (TEXT): Category name
- `description` (TEXT): Category description
- `slug` (TEXT): URL-friendly identifier
- `image_url` (TEXT): Category image
- `sort_order` (INTEGER): Display order
- `is_active` (BOOLEAN): Visibility status

#### `products`
Master product catalog managed by super admin.
- `id` (UUID, PK)
- `name` (TEXT): Product name
- `description` (TEXT): Product description
- `slug` (TEXT): URL-friendly identifier
- `base_price` (DECIMAL): Base price before customizations
- `category_id` (UUID, FK): References product_categories(id)
- `tags` (TEXT[]): Product tags for filtering
- `sku` (TEXT): Stock keeping unit
- `weight` (DECIMAL): Product weight
- `dimensions` (JSONB): Physical dimensions
- `materials` (TEXT[]): Materials used
- `care_instructions` (TEXT): Care and maintenance info
- `is_active` (BOOLEAN): Availability status
- `featured` (BOOLEAN): Featured product flag
- `sort_order` (INTEGER): Display order

#### `product_images`
Product image gallery.
- `id` (UUID, PK)
- `product_id` (UUID, FK): References products(id)
- `url` (TEXT): Image URL
- `alt_text` (TEXT): Accessibility text
- `is_primary` (BOOLEAN): Main product image
- `sort_order` (INTEGER): Display order

#### `customization_options`
Available customization options for each product.
- `id` (UUID, PK)
- `product_id` (UUID, FK): References products(id)
- `type` (ENUM): 'text', 'image', 'color', 'size', 'font', 'position'
- `name` (TEXT): Option name
- `description` (TEXT): Option description
- `required` (BOOLEAN): Whether option is mandatory
- `options` (JSONB): Available choices/configurations
- `price_modifier` (DECIMAL): Additional cost for this option
- `max_length` (INTEGER): For text inputs
- `allowed_file_types` (TEXT[]): For image uploads
- `position_constraints` (JSONB): For positioning options
- `sort_order` (INTEGER): Display order
- `is_active` (BOOLEAN): Option availability

#### `merchant_products`
Products published by merchants to their stores.
- `id` (UUID, PK)
- `merchant_id` (UUID, FK): References merchants(id)
- `product_id` (UUID, FK): References products(id)
- `shopify_product_id` (TEXT): Shopify product ID
- `custom_price` (DECIMAL): Merchant's custom pricing
- `is_published` (BOOLEAN): Publication status
- `published_at` (TIMESTAMP): Publication date

### Orders & Fulfillment

#### `orders`
Customer orders for customized products.
- `id` (UUID, PK)
- `order_number` (TEXT): Unique order identifier
- `merchant_id` (UUID, FK): References merchants(id)
- `customer_email` (TEXT): Customer's email
- `customer_name`, `customer_phone` (TEXT): Customer info
- `total_amount`, `subtotal`, `tax_amount`, `shipping_amount`, `discount_amount` (DECIMAL)
- `status` (ENUM): 'pending', 'processing', 'shipped', 'delivered', 'cancelled', 'refunded'
- `payment_status` (ENUM): 'pending', 'paid', 'failed', 'refunded', 'partially_refunded'
- `payment_intent_id` (TEXT): Stripe payment intent
- `shipping_address`, `billing_address` (JSONB): Address information
- `tracking_number`, `tracking_url` (TEXT): Shipping tracking
- `notes` (TEXT): Order notes
- `shopify_order_id` (TEXT): Shopify order reference
- `fulfillment_service` (TEXT): Fulfillment provider

#### `order_items`
Individual items within an order.
- `id` (UUID, PK)
- `order_id` (UUID, FK): References orders(id)
- `product_id` (UUID, FK): References products(id)
- `design_id` (UUID, FK): References designs(id)
- `quantity` (INTEGER): Number of items
- `unit_price`, `total_price` (DECIMAL): Pricing
- `customization_data` (JSONB): Final customization details
- `production_files` (JSONB): Print-ready file URLs
- `sku` (TEXT): Item SKU

### Designs & Customizations

#### `designs`
Customer-created product designs.
- `id` (UUID, PK)
- `customer_email` (TEXT): Design creator
- `merchant_id` (UUID, FK): References merchants(id)
- `product_id` (UUID, FK): References products(id)
- `name` (TEXT): Design name
- `design_data` (JSONB): Complete design configuration
- `preview_url` (TEXT): Design preview image
- `thumbnail_url` (TEXT): Small preview image
- `is_saved` (BOOLEAN): Whether customer saved the design
- `is_public` (BOOLEAN): Public gallery visibility

### Support & Communication

#### `tickets`
Customer support ticket system.
- `id` (UUID, PK)
- `ticket_number` (TEXT): Unique ticket identifier
- `user_id` (UUID, FK): References users(id)
- `merchant_id` (UUID, FK): References merchants(id)
- `subject` (TEXT): Ticket subject
- `description` (TEXT): Initial description
- `status` (ENUM): 'open', 'in_progress', 'resolved', 'closed'
- `priority` (ENUM): 'low', 'medium', 'high', 'urgent'
- `assigned_to` (UUID, FK): References users(id)
- `tags` (TEXT[]): Ticket categorization

#### `ticket_messages`
Messages within support tickets.
- `id` (UUID, PK)
- `ticket_id` (UUID, FK): References tickets(id)
- `user_id` (UUID, FK): References users(id)
- `message` (TEXT): Message content
- `is_internal` (BOOLEAN): Internal staff note
- `attachments` (JSONB): File attachments

### System & Monitoring

#### `activity_logs`
System activity and audit trail.
- `id` (UUID, PK)
- `user_id` (UUID, FK): References users(id)
- `merchant_id` (UUID, FK): References merchants(id)
- `action` (TEXT): Action performed
- `resource_type` (TEXT): Type of resource affected
- `resource_id` (UUID): ID of affected resource
- `details` (JSONB): Additional context
- `ip_address` (INET): User's IP address
- `user_agent` (TEXT): Browser information

#### `webhooks`
Webhook configurations for merchants.
- `id` (UUID, PK)
- `merchant_id` (UUID, FK): References merchants(id)
- `event_type` (TEXT): Event that triggers webhook
- `url` (TEXT): Webhook endpoint URL
- `secret` (TEXT): Webhook signing secret
- `is_active` (BOOLEAN): Webhook status
- `last_triggered_at` (TIMESTAMP): Last execution time
- `failure_count` (INTEGER): Failed delivery attempts

## Security Features

### Row Level Security (RLS)
All tables have RLS enabled with policies that enforce:
- Super admins can access all data
- Merchants can only access their own data
- Customers can only access their own designs and orders
- Public data (products, categories) is readable by all

### Helper Functions
- `is_super_admin()`: Check if current user is super admin
- `is_merchant()`: Check if current user is merchant
- `get_user_merchant_id()`: Get merchant ID for current user

## Database Functions

### Utility Functions
- `generate_order_number()`: Creates unique order numbers
- `generate_ticket_number()`: Creates unique ticket numbers
- `calculate_order_totals()`: Recalculates order pricing
- `log_activity()`: Records system activities

### Statistics Functions
- `get_admin_stats()`: Dashboard statistics for super admin
- `get_merchant_stats()`: Dashboard statistics for merchants
- `search_products()`: Advanced product search with filters

## Triggers

### Automatic Updates
- `updated_at` triggers on all tables with timestamps
- Order number generation on order creation
- Ticket number generation on ticket creation
- Order total recalculation when items change
- Activity logging for important state changes

### User Management
- Automatic user profile creation on registration
- Activity logging for user actions

## Migration Files

1. `001_initial_schema.sql` - Core table structure and types
2. `002_rls_policies.sql` - Row Level Security policies
3. `003_functions_and_triggers.sql` - Database functions and triggers
4. `004_seed_data.sql` - Initial product catalog and sample data

## Usage Notes

- All UUIDs are generated using `uuid_generate_v4()`
- Timestamps use `TIMESTAMP WITH TIME ZONE` for proper timezone handling
- JSONB is used for flexible data storage (addresses, settings, design data)
- Proper foreign key constraints ensure data integrity
- Indexes are created on frequently queried columns for performance
