-- Function to generate unique order numbers
CREATE OR REPLACE FUNCTION generate_order_number()
RETURNS TEXT AS $$
DECLARE
    new_number TEXT;
    exists_check BOOLEAN;
BEGIN
    LOOP
        -- Generate order number: BO-YYYYMMDD-XXXX (BO = Biypod Order)
        new_number := 'BO-' || TO_CHAR(NOW(), 'YYYYMMDD') || '-' || 
                     LPAD(FLOOR(RANDOM() * 10000)::TEXT, 4, '0');
        
        -- Check if this number already exists
        SELECT EXISTS(SELECT 1 FROM public.orders WHERE order_number = new_number) INTO exists_check;
        
        -- If it doesn't exist, we can use it
        IF NOT exists_check THEN
            EXIT;
        END IF;
    END LOOP;
    
    RETURN new_number;
END;
$$ LANGUAGE plpgsql;

-- Function to generate unique ticket numbers
CREATE OR REPLACE FUNCTION generate_ticket_number()
RETURNS TEXT AS $$
DECLARE
    new_number TEXT;
    exists_check BOOLEAN;
BEGIN
    LOOP
        -- Generate ticket number: TK-YYYYMMDD-XXXX
        new_number := 'TK-' || TO_CHAR(NOW(), 'YYYYMMDD') || '-' || 
                     LPAD(FLOOR(RANDOM() * 10000)::TEXT, 4, '0');
        
        -- Check if this number already exists
        SELECT EXISTS(SELECT 1 FROM public.tickets WHERE ticket_number = new_number) INTO exists_check;
        
        -- If it doesn't exist, we can use it
        IF NOT exists_check THEN
            EXIT;
        END IF;
    END LOOP;
    
    RETURN new_number;
END;
$$ LANGUAGE plpgsql;

-- Trigger to auto-generate order numbers
CREATE OR REPLACE FUNCTION set_order_number()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.order_number IS NULL OR NEW.order_number = '' THEN
        NEW.order_number := generate_order_number();
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_set_order_number
    BEFORE INSERT ON public.orders
    FOR EACH ROW
    EXECUTE FUNCTION set_order_number();

-- Trigger to auto-generate ticket numbers
CREATE OR REPLACE FUNCTION set_ticket_number()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.ticket_number IS NULL OR NEW.ticket_number = '' THEN
        NEW.ticket_number := generate_ticket_number();
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_set_ticket_number
    BEFORE INSERT ON public.tickets
    FOR EACH ROW
    EXECUTE FUNCTION set_ticket_number();

-- Function to log activities
CREATE OR REPLACE FUNCTION log_activity(
    p_user_id UUID,
    p_merchant_id UUID,
    p_action TEXT,
    p_resource_type TEXT,
    p_resource_id UUID,
    p_details JSONB DEFAULT '{}'::jsonb
)
RETURNS UUID AS $$
DECLARE
    log_id UUID;
BEGIN
    INSERT INTO public.activity_logs (
        user_id,
        merchant_id,
        action,
        resource_type,
        resource_id,
        details
    ) VALUES (
        p_user_id,
        p_merchant_id,
        p_action,
        p_resource_type,
        p_resource_id,
        p_details
    ) RETURNING id INTO log_id;
    
    RETURN log_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger to log order status changes
CREATE OR REPLACE FUNCTION log_order_status_change()
RETURNS TRIGGER AS $$
BEGIN
    IF OLD.status IS DISTINCT FROM NEW.status THEN
        PERFORM log_activity(
            auth.uid(),
            NEW.merchant_id,
            'order_status_changed',
            'order',
            NEW.id,
            jsonb_build_object(
                'old_status', OLD.status,
                'new_status', NEW.status,
                'order_number', NEW.order_number
            )
        );
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_log_order_status_change
    AFTER UPDATE ON public.orders
    FOR EACH ROW
    EXECUTE FUNCTION log_order_status_change();

-- Function to calculate order totals
CREATE OR REPLACE FUNCTION calculate_order_totals(order_id UUID)
RETURNS VOID AS $$
DECLARE
    subtotal DECIMAL(10,2);
    tax_rate DECIMAL(5,4) := 0.0875; -- Default 8.75% tax rate
    tax_amount DECIMAL(10,2);
    shipping_amount DECIMAL(10,2) := 9.99; -- Default shipping
    total_amount DECIMAL(10,2);
BEGIN
    -- Calculate subtotal from order items
    SELECT COALESCE(SUM(total_price), 0) INTO subtotal
    FROM public.order_items
    WHERE order_items.order_id = calculate_order_totals.order_id;
    
    -- Calculate tax
    tax_amount := subtotal * tax_rate;
    
    -- Free shipping over $75
    IF subtotal >= 75 THEN
        shipping_amount := 0;
    END IF;
    
    -- Calculate total
    total_amount := subtotal + tax_amount + shipping_amount;
    
    -- Update order
    UPDATE public.orders SET
        subtotal = calculate_order_totals.subtotal,
        tax_amount = calculate_order_totals.tax_amount,
        shipping_amount = calculate_order_totals.shipping_amount,
        total_amount = calculate_order_totals.total_amount,
        updated_at = NOW()
    WHERE id = calculate_order_totals.order_id;
END;
$$ LANGUAGE plpgsql;

-- Trigger to recalculate order totals when items change
CREATE OR REPLACE FUNCTION recalculate_order_totals()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'DELETE' THEN
        PERFORM calculate_order_totals(OLD.order_id);
        RETURN OLD;
    ELSE
        PERFORM calculate_order_totals(NEW.order_id);
        RETURN NEW;
    END IF;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_recalculate_order_totals
    AFTER INSERT OR UPDATE OR DELETE ON public.order_items
    FOR EACH ROW
    EXECUTE FUNCTION recalculate_order_totals();

-- Function to handle new user registration
CREATE OR REPLACE FUNCTION handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.users (id, email, role)
    VALUES (NEW.id, NEW.email, 'customer');
    
    INSERT INTO public.user_profiles (user_id)
    VALUES (NEW.id);
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger for new user registration
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW
    EXECUTE FUNCTION handle_new_user();

-- Function to get merchant statistics
CREATE OR REPLACE FUNCTION get_merchant_stats(merchant_id UUID)
RETURNS JSONB AS $$
DECLARE
    stats JSONB;
BEGIN
    SELECT jsonb_build_object(
        'total_orders', COUNT(o.id),
        'total_revenue', COALESCE(SUM(o.total_amount), 0),
        'pending_orders', COUNT(o.id) FILTER (WHERE o.status = 'pending'),
        'processing_orders', COUNT(o.id) FILTER (WHERE o.status = 'processing'),
        'shipped_orders', COUNT(o.id) FILTER (WHERE o.status = 'shipped'),
        'total_designs', (
            SELECT COUNT(*) FROM public.designs d 
            WHERE d.merchant_id = get_merchant_stats.merchant_id
        ),
        'published_products', (
            SELECT COUNT(*) FROM public.merchant_products mp 
            WHERE mp.merchant_id = get_merchant_stats.merchant_id AND mp.is_published = true
        )
    ) INTO stats
    FROM public.orders o
    WHERE o.merchant_id = get_merchant_stats.merchant_id;
    
    RETURN stats;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get super admin dashboard stats
CREATE OR REPLACE FUNCTION get_admin_stats()
RETURNS JSONB AS $$
DECLARE
    stats JSONB;
BEGIN
    SELECT jsonb_build_object(
        'total_merchants', (SELECT COUNT(*) FROM public.merchants WHERE is_active = true),
        'total_orders', (SELECT COUNT(*) FROM public.orders),
        'total_revenue', (SELECT COALESCE(SUM(total_amount), 0) FROM public.orders WHERE payment_status = 'paid'),
        'total_products', (SELECT COUNT(*) FROM public.products WHERE is_active = true),
        'total_designs', (SELECT COUNT(*) FROM public.designs),
        'open_tickets', (SELECT COUNT(*) FROM public.tickets WHERE status IN ('open', 'in_progress')),
        'monthly_revenue', (
            SELECT COALESCE(SUM(total_amount), 0) 
            FROM public.orders 
            WHERE payment_status = 'paid' 
            AND created_at >= DATE_TRUNC('month', NOW())
        ),
        'monthly_orders', (
            SELECT COUNT(*) 
            FROM public.orders 
            WHERE created_at >= DATE_TRUNC('month', NOW())
        )
    ) INTO stats;
    
    RETURN stats;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to search products
CREATE OR REPLACE FUNCTION search_products(
    search_term TEXT DEFAULT '',
    category_id UUID DEFAULT NULL,
    limit_count INTEGER DEFAULT 20,
    offset_count INTEGER DEFAULT 0
)
RETURNS TABLE (
    id UUID,
    name TEXT,
    description TEXT,
    slug TEXT,
    base_price DECIMAL(10,2),
    category_name TEXT,
    primary_image_url TEXT,
    tags TEXT[]
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        p.id,
        p.name,
        p.description,
        p.slug,
        p.base_price,
        pc.name as category_name,
        pi.url as primary_image_url,
        p.tags
    FROM public.products p
    LEFT JOIN public.product_categories pc ON pc.id = p.category_id
    LEFT JOIN public.product_images pi ON pi.product_id = p.id AND pi.is_primary = true
    WHERE p.is_active = true
    AND (search_term = '' OR p.name ILIKE '%' || search_term || '%' OR p.description ILIKE '%' || search_term || '%')
    AND (category_id IS NULL OR p.category_id = search_products.category_id)
    ORDER BY p.featured DESC, p.sort_order ASC, p.name ASC
    LIMIT limit_count
    OFFSET offset_count;
END;
$$ LANGUAGE plpgsql;
