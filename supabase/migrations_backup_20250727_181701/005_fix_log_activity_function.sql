-- Fix log_activity function to handle NULL values and empty strings properly

-- Drop the existing function
DROP FUNCTION IF EXISTS log_activity(UUI<PERSON>, UUID, TEXT, TEXT, UUID, JSONB);

-- Recreate with proper NULL handling
CREATE OR REPLACE FUNCTION log_activity(
    p_user_id TEXT DEFAULT NULL,
    p_merchant_id TEXT DEFAULT NULL,
    p_action TEXT,
    p_resource_type TEXT,
    p_resource_id TEXT DEFAULT NULL,
    p_details JSONB DEFAULT '{}'::jsonb
)
RETURNS UUID AS $$
DECLARE
    log_id UUID;
    final_user_id UUID;
    final_merchant_id UUID;
    final_resource_id UUID;
BEGIN
    -- Convert empty strings to NULL and validate UUIDs
    IF p_user_id IS NOT NULL AND p_user_id != '' THEN
        BEGIN
            final_user_id := p_user_id::UUID;
        EXCEPTION WHEN invalid_text_representation THEN
            final_user_id := NULL;
        END;
    ELSE
        final_user_id := NULL;
    END IF;
    
    IF p_merchant_id IS NOT NULL AND p_merchant_id != '' THEN
        BEGIN
            final_merchant_id := p_merchant_id::UUID;
        EXCEPTION WHEN invalid_text_representation THEN
            final_merchant_id := NULL;
        END;
    ELSE
        final_merchant_id := NULL;
    END IF;
    
    IF p_resource_id IS NOT NULL AND p_resource_id != '' THEN
        BEGIN
            final_resource_id := p_resource_id::UUID;
        EXCEPTION WHEN invalid_text_representation THEN
            final_resource_id := NULL;
        END;
    ELSE
        final_resource_id := NULL;
    END IF;
    
    -- Insert the activity log
    INSERT INTO public.activity_logs (
        user_id,
        merchant_id,
        action,
        resource_type,
        resource_id,
        details
    ) VALUES (
        final_user_id,
        final_merchant_id,
        p_action,
        p_resource_type,
        final_resource_id,
        p_details
    ) RETURNING id INTO log_id;
    
    RETURN log_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
