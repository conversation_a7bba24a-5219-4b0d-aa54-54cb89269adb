-- Add missing INSERT policy for users table
-- This allows service role to create users during Shopify app installation

-- Allow service role to insert users (for Shopify merchant creation)
CREATE POLICY "Service role can insert users" ON public.users
    FOR INSERT WITH CHECK (true);

-- Allow service role to insert user profiles (for Shopify merchant creation)  
CREATE POLICY "Service role can insert user profiles" ON public.user_profiles
    FOR INSERT WITH CHECK (true);

-- Allow service role to insert merchants (for Shopify merchant creation)
CREATE POLICY "Service role can insert merchants" ON public.merchants
    FOR INSERT WITH CHECK (true);

-- Allow service role to update merchants (for Shopify app updates)
CREATE POLICY "Service role can update merchants" ON public.merchants
    FOR UPDATE USING (true);
