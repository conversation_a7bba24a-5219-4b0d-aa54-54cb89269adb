-- Update existing order_items table to match our new schema
-- First, add the new columns we need
ALTER TABLE public.order_items 
ADD COLUMN IF NOT EXISTS merchant_product_id UUID REFERENCES public.merchant_products(id) ON DELETE SET NULL,
ADD COLUMN IF NOT EXISTS product_variant_id UUID,
ADD COLUMN IF NOT EXISTS shopify_line_item_id TEXT,
ADD COLUMN IF NOT EXISTS product_name TEXT,
ADD COLUMN IF NOT EXISTS variant_title TEXT,
ADD COLUMN IF NOT EXISTS price DECIMAL(10,2) DEFAULT 0,
ADD COLUMN IF NOT EXISTS fulfillment_status TEXT DEFAULT 'unfulfilled',
ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();

-- Update existing columns to match our schema
ALTER TABLE public.order_items 
ALTER COLUMN quantity SET DEFAULT 1,
ALTER COLUMN total_price SET DEFAULT 0;

-- Rename product_id to legacy_product_id to avoid conflicts
ALTER TABLE public.order_items 
RENAME COLUMN product_id TO legacy_product_id;

-- Rename unit_price to price if it exists
DO $$ 
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'order_items' AND column_name = 'unit_price') THEN
        ALTER TABLE public.order_items RENAME COLUMN unit_price TO legacy_unit_price;
    END IF;
END $$;

-- Make product_name NOT NULL with a default for existing rows
UPDATE public.order_items SET product_name = 'Unknown Product' WHERE product_name IS NULL;
ALTER TABLE public.order_items ALTER COLUMN product_name SET NOT NULL;

-- Create product_variants table
CREATE TABLE IF NOT EXISTS public.product_variants (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    merchant_product_id UUID NOT NULL REFERENCES public.merchant_products(id) ON DELETE CASCADE,
    shopify_variant_id TEXT,
    title TEXT NOT NULL,
    price DECIMAL(10,2) NOT NULL DEFAULT 0,
    compare_at_price DECIMAL(10,2),
    sku TEXT,
    barcode TEXT,
    inventory_quantity INTEGER DEFAULT 0,
    inventory_policy TEXT DEFAULT 'deny',
    fulfillment_service TEXT DEFAULT 'manual',
    inventory_management TEXT,
    option1 TEXT,
    option2 TEXT,
    option3 TEXT,
    weight DECIMAL(8,2),
    weight_unit TEXT DEFAULT 'kg',
    requires_shipping BOOLEAN DEFAULT true,
    taxable BOOLEAN DEFAULT true,
    position INTEGER DEFAULT 1,
    image_url TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add foreign key constraint for product_variant_id after creating the table
ALTER TABLE public.order_items 
ADD CONSTRAINT fk_order_items_product_variant_id 
FOREIGN KEY (product_variant_id) REFERENCES public.product_variants(id) ON DELETE SET NULL;

-- Create indexes for order_items (new columns)
CREATE INDEX IF NOT EXISTS idx_order_items_merchant_product_id ON public.order_items(merchant_product_id);
CREATE INDEX IF NOT EXISTS idx_order_items_product_variant_id ON public.order_items(product_variant_id);
CREATE INDEX IF NOT EXISTS idx_order_items_shopify_line_item_id ON public.order_items(shopify_line_item_id);

-- Create indexes for product_variants
CREATE INDEX IF NOT EXISTS idx_product_variants_merchant_product_id ON public.product_variants(merchant_product_id);
CREATE INDEX IF NOT EXISTS idx_product_variants_shopify_variant_id ON public.product_variants(shopify_variant_id);
CREATE INDEX IF NOT EXISTS idx_product_variants_sku ON public.product_variants(sku);
CREATE INDEX IF NOT EXISTS idx_product_variants_active ON public.product_variants(is_active);

-- Add RLS policies for product_variants
ALTER TABLE public.product_variants ENABLE ROW LEVEL SECURITY;

-- Policy: Users can view variants of products they have access to
CREATE POLICY "Users can view product variants" ON public.product_variants
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.merchant_products mp
            JOIN public.merchants m ON mp.merchant_id = m.id
            WHERE mp.id = merchant_product_id
            AND (
                m.user_id = auth.uid()
                OR EXISTS (
                    SELECT 1 FROM public.users u 
                    WHERE u.id = auth.uid() 
                    AND u.role = 'super_admin'
                )
            )
        )
    );

-- Policy: Merchants can manage their product variants
CREATE POLICY "Merchants can manage their product variants" ON public.product_variants
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.merchant_products mp
            JOIN public.merchants m ON mp.merchant_id = m.id
            WHERE mp.id = merchant_product_id
            AND m.user_id = auth.uid()
        )
    );

-- Policy: Super admins can manage all variants
CREATE POLICY "Super admins can manage all product variants" ON public.product_variants
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.users u 
            WHERE u.id = auth.uid() 
            AND u.role = 'super_admin'
        )
    );

-- Add updated_at trigger for product_variants
CREATE TRIGGER update_product_variants_updated_at
    BEFORE UPDATE ON public.product_variants
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Add updated_at trigger for order_items if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'update_order_items_updated_at') THEN
        CREATE TRIGGER update_order_items_updated_at
            BEFORE UPDATE ON public.order_items
            FOR EACH ROW
            EXECUTE FUNCTION update_updated_at_column();
    END IF;
END $$;
