-- Enable Row Level Security on all tables
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.merchants ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.product_categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.products ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.product_images ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.customization_options ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.merchant_products ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.designs ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.orders ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.order_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.tickets ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.ticket_messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.activity_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.webhooks ENABLE ROW LEVEL SECURITY;

-- Helper function to check if user is super admin
CREATE OR REPLACE FUNCTION is_super_admin()
RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM public.users 
        WHERE id = auth.uid() AND role = 'super_admin'
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Helper function to check if user is merchant
CREATE OR REPLACE FUNCTION is_merchant()
RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM public.users 
        WHERE id = auth.uid() AND role = 'merchant'
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Helper function to get user's merchant ID
CREATE OR REPLACE FUNCTION get_user_merchant_id()
RETURNS UUID AS $$
BEGIN
    RETURN (
        SELECT m.id FROM public.merchants m
        JOIN public.users u ON u.id = m.user_id
        WHERE u.id = auth.uid()
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Users table policies
CREATE POLICY "Super admins can view all users" ON public.users
    FOR SELECT USING (is_super_admin());

CREATE POLICY "Users can view their own record" ON public.users
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update their own record" ON public.users
    FOR UPDATE USING (auth.uid() = id);

-- User profiles policies
CREATE POLICY "Super admins can view all profiles" ON public.user_profiles
    FOR SELECT USING (is_super_admin());

CREATE POLICY "Users can view their own profile" ON public.user_profiles
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own profile" ON public.user_profiles
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own profile" ON public.user_profiles
    FOR UPDATE USING (auth.uid() = user_id);

-- Merchants table policies
CREATE POLICY "Super admins can view all merchants" ON public.merchants
    FOR ALL USING (is_super_admin());

CREATE POLICY "Merchants can view their own record" ON public.merchants
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Merchants can update their own record" ON public.merchants
    FOR UPDATE USING (auth.uid() = user_id);

-- Product categories policies (public read, admin write)
CREATE POLICY "Anyone can view active categories" ON public.product_categories
    FOR SELECT USING (is_active = true);

CREATE POLICY "Super admins can manage categories" ON public.product_categories
    FOR ALL USING (is_super_admin());

-- Products policies (public read, admin write)
CREATE POLICY "Anyone can view active products" ON public.products
    FOR SELECT USING (is_active = true);

CREATE POLICY "Super admins can manage products" ON public.products
    FOR ALL USING (is_super_admin());

-- Product images policies
CREATE POLICY "Anyone can view product images" ON public.product_images
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.products p 
            WHERE p.id = product_id AND p.is_active = true
        )
    );

CREATE POLICY "Super admins can manage product images" ON public.product_images
    FOR ALL USING (is_super_admin());

-- Customization options policies
CREATE POLICY "Anyone can view customization options" ON public.customization_options
    FOR SELECT USING (
        is_active = true AND EXISTS (
            SELECT 1 FROM public.products p 
            WHERE p.id = product_id AND p.is_active = true
        )
    );

CREATE POLICY "Super admins can manage customization options" ON public.customization_options
    FOR ALL USING (is_super_admin());

-- Merchant products policies
CREATE POLICY "Super admins can view all merchant products" ON public.merchant_products
    FOR SELECT USING (is_super_admin());

CREATE POLICY "Merchants can view their own products" ON public.merchant_products
    FOR SELECT USING (merchant_id = get_user_merchant_id());

CREATE POLICY "Merchants can manage their own products" ON public.merchant_products
    FOR ALL USING (merchant_id = get_user_merchant_id());

CREATE POLICY "Anyone can view published merchant products" ON public.merchant_products
    FOR SELECT USING (is_published = true);

-- Designs policies
CREATE POLICY "Super admins can view all designs" ON public.designs
    FOR SELECT USING (is_super_admin());

CREATE POLICY "Merchants can view designs for their products" ON public.designs
    FOR SELECT USING (merchant_id = get_user_merchant_id());

CREATE POLICY "Customers can view their own designs" ON public.designs
    FOR SELECT USING (customer_email = auth.jwt() ->> 'email');

CREATE POLICY "Customers can create designs" ON public.designs
    FOR INSERT WITH CHECK (customer_email = auth.jwt() ->> 'email');

CREATE POLICY "Customers can update their own designs" ON public.designs
    FOR UPDATE USING (customer_email = auth.jwt() ->> 'email');

CREATE POLICY "Anyone can view public designs" ON public.designs
    FOR SELECT USING (is_public = true);

-- Orders policies
CREATE POLICY "Super admins can view all orders" ON public.orders
    FOR ALL USING (is_super_admin());

CREATE POLICY "Merchants can view their own orders" ON public.orders
    FOR SELECT USING (merchant_id = get_user_merchant_id());

CREATE POLICY "Merchants can update their own orders" ON public.orders
    FOR UPDATE USING (merchant_id = get_user_merchant_id());

CREATE POLICY "Customers can view their own orders" ON public.orders
    FOR SELECT USING (customer_email = auth.jwt() ->> 'email');

-- Order items policies
CREATE POLICY "Super admins can view all order items" ON public.order_items
    FOR SELECT USING (is_super_admin());

CREATE POLICY "Merchants can view their order items" ON public.order_items
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.orders o 
            WHERE o.id = order_id AND o.merchant_id = get_user_merchant_id()
        )
    );

CREATE POLICY "Customers can view their order items" ON public.order_items
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.orders o 
            WHERE o.id = order_id AND o.customer_email = auth.jwt() ->> 'email'
        )
    );

-- Tickets policies
CREATE POLICY "Super admins can view all tickets" ON public.tickets
    FOR ALL USING (is_super_admin());

CREATE POLICY "Users can view their own tickets" ON public.tickets
    FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Users can create tickets" ON public.tickets
    FOR INSERT WITH CHECK (user_id = auth.uid());

CREATE POLICY "Users can update their own tickets" ON public.tickets
    FOR UPDATE USING (user_id = auth.uid());

CREATE POLICY "Merchants can view tickets for their account" ON public.tickets
    FOR SELECT USING (merchant_id = get_user_merchant_id());

-- Ticket messages policies
CREATE POLICY "Super admins can view all ticket messages" ON public.ticket_messages
    FOR ALL USING (is_super_admin());

CREATE POLICY "Users can view messages for their tickets" ON public.ticket_messages
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.tickets t 
            WHERE t.id = ticket_id AND t.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can create messages for their tickets" ON public.ticket_messages
    FOR INSERT WITH CHECK (
        user_id = auth.uid() AND EXISTS (
            SELECT 1 FROM public.tickets t 
            WHERE t.id = ticket_id AND t.user_id = auth.uid()
        )
    );

CREATE POLICY "Merchants can view messages for their tickets" ON public.ticket_messages
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.tickets t 
            WHERE t.id = ticket_id AND t.merchant_id = get_user_merchant_id()
        )
    );

-- Activity logs policies
CREATE POLICY "Super admins can view all activity logs" ON public.activity_logs
    FOR SELECT USING (is_super_admin());

CREATE POLICY "Users can view their own activity logs" ON public.activity_logs
    FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Merchants can view logs for their account" ON public.activity_logs
    FOR SELECT USING (merchant_id = get_user_merchant_id());

-- Webhooks policies
CREATE POLICY "Super admins can view all webhooks" ON public.webhooks
    FOR SELECT USING (is_super_admin());

CREATE POLICY "Merchants can manage their own webhooks" ON public.webhooks
    FOR ALL USING (merchant_id = get_user_merchant_id());

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO anon, authenticated;
GRANT ALL ON ALL TABLES IN SCHEMA public TO anon, authenticated;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO anon, authenticated;
