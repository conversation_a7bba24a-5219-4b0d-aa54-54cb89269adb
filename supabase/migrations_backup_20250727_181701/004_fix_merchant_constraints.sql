-- Fix merchant table to allow multiple shops per user
-- Remove the unique constraint on user_id to allow one user to have multiple merchant accounts

-- Drop the unique constraint on user_id
ALTER TABLE public.merchants DROP CONSTRAINT IF EXISTS merchants_user_id_key;

-- Keep the unique constraint on shop_domain (each shop can only be installed once)
-- This constraint should already exist from the initial schema

-- Add a comment to clarify the design
COMMENT ON TABLE public.merchants IS 'Each merchant record represents one Shopify store installation. A user can have multiple merchant records for different stores.';
COMMENT ON COLUMN public.merchants.user_id IS 'References the user who installed this store. One user can install multiple stores.';
COMMENT ON COLUMN public.merchants.shop_domain IS 'Unique Shopify store domain. Each store can only be installed once.';
