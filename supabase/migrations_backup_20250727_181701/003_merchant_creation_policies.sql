-- Add special policies for merchant creation during Shopify app installation
-- This allows the system to create merchant records without requiring authentication

-- Create a function to check if the request is from our application
CREATE OR REPLACE FUNCTION is_system_request()
RETURNS BOOLEAN AS $$
BEGIN
    -- Check if the request has the service role key
    -- This is a simplified check - in production you might want more sophisticated validation
    RETURN current_setting('request.jwt.claims', true)::json->>'role' = 'service_role'
           OR current_setting('request.jwt.claims', true)::json->>'iss' = 'supabase';
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Allow system to create merchants without authentication
CREATE POLICY "System can create merchants" ON public.merchants
    FOR INSERT WITH CHECK (is_system_request());

-- Allow system to create users without authentication (for merchant creation)
CREATE POLICY "System can create users" ON public.users
    FOR INSERT WITH CHECK (is_system_request());

-- Allow system to create user profiles without authentication
CREATE POLICY "System can create user profiles" ON public.user_profiles
    FOR INSERT WITH CHECK (is_system_request());

-- Allow system to read merchants by shop domain (for checking existence)
CREATE POLICY "System can read merchants by shop domain" ON public.merchants
    FOR SELECT USING (is_system_request());

-- Allow system to read users for merchant creation
CREATE POLICY "System can read users for merchant creation" ON public.users
    FOR SELECT USING (is_system_request());

-- Create a function to safely create a merchant with user
CREATE OR REPLACE FUNCTION create_merchant_with_user(
    p_shop_domain TEXT,
    p_shop_name TEXT,
    p_access_token TEXT DEFAULT NULL,
    p_email TEXT DEFAULT NULL
)
RETURNS JSON AS $$
DECLARE
    v_user_id UUID;
    v_merchant_id UUID;
    v_result JSON;
BEGIN
    -- Generate a unique email if not provided
    IF p_email IS NULL THEN
        p_email := p_shop_domain || '@merchant.biypod.com';
    END IF;

    -- Check if merchant already exists
    SELECT id INTO v_merchant_id 
    FROM public.merchants 
    WHERE shop_domain = p_shop_domain;
    
    IF v_merchant_id IS NOT NULL THEN
        -- Return existing merchant
        SELECT json_build_object(
            'success', true,
            'message', 'Merchant already exists',
            'merchant', row_to_json(m.*)
        ) INTO v_result
        FROM public.merchants m
        WHERE m.id = v_merchant_id;
        
        RETURN v_result;
    END IF;

    -- Create user first
    INSERT INTO public.users (id, email, role)
    VALUES (gen_random_uuid(), p_email, 'merchant')
    RETURNING id INTO v_user_id;

    -- Create user profile
    INSERT INTO public.user_profiles (user_id, first_name, last_name, company)
    VALUES (v_user_id, 'Merchant', 'User', p_shop_name);

    -- Create merchant
    INSERT INTO public.merchants (
        user_id,
        shop_domain,
        shop_name,
        access_token,
        subscription_tier,
        subscription_status,
        settings
    ) VALUES (
        v_user_id,
        p_shop_domain,
        p_shop_name,
        COALESCE(p_access_token, 'pending-' || extract(epoch from now())::text),
        'free',
        'active',
        '{"customizer_enabled": true, "auto_fulfill": false, "theme_settings": {"primary_color": "#3B82F6", "secondary_color": "#10B981"}}'::jsonb
    ) RETURNING id INTO v_merchant_id;

    -- Return the created merchant with user info
    SELECT json_build_object(
        'success', true,
        'message', 'Merchant created successfully',
        'merchant', json_build_object(
            'id', m.id,
            'user_id', m.user_id,
            'shop_domain', m.shop_domain,
            'shop_name', m.shop_name,
            'access_token', m.access_token,
            'subscription_tier', m.subscription_tier,
            'subscription_status', m.subscription_status,
            'settings', m.settings,
            'created_at', m.created_at,
            'updated_at', m.updated_at,
            'user', json_build_object(
                'id', u.id,
                'email', u.email,
                'role', u.role,
                'user_profiles', COALESCE(
                    json_agg(
                        json_build_object(
                            'id', up.id,
                            'user_id', up.user_id,
                            'first_name', up.first_name,
                            'last_name', up.last_name,
                            'company', up.company,
                            'created_at', up.created_at,
                            'updated_at', up.updated_at
                        )
                    ) FILTER (WHERE up.id IS NOT NULL),
                    '[]'::json
                )
            )
        )
    ) INTO v_result
    FROM public.merchants m
    JOIN public.users u ON u.id = m.user_id
    LEFT JOIN public.user_profiles up ON up.user_id = u.id
    WHERE m.id = v_merchant_id
    GROUP BY m.id, m.user_id, m.shop_domain, m.shop_name, m.access_token, 
             m.subscription_tier, m.subscription_status, m.settings, 
             m.created_at, m.updated_at, u.id, u.email, u.role;

    RETURN v_result;
EXCEPTION
    WHEN OTHERS THEN
        RETURN json_build_object(
            'success', false,
            'error', 'Failed to create merchant',
            'details', SQLERRM
        );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission on the function
GRANT EXECUTE ON FUNCTION create_merchant_with_user TO anon, authenticated;
GRANT EXECUTE ON FUNCTION is_system_request TO anon, authenticated;
