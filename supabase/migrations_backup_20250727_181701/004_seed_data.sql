-- Insert product categories
INSERT INTO public.product_categories (id, name, description, slug, image_url, sort_order) VALUES
    ('550e8400-e29b-41d4-a716-446655440001', 'T-Shirts', 'Comfortable and customizable t-shirts for all occasions', 't-shirts', '/images/categories/t-shirts.jpg', 1),
    ('550e8400-e29b-41d4-a716-446655440002', 'Hoodies', 'Cozy hoodies perfect for custom designs', 'hoodies', '/images/categories/hoodies.jpg', 2),
    ('550e8400-e29b-41d4-a716-446655440003', 'Mugs', 'High-quality ceramic mugs for personalized gifts', 'mugs', '/images/categories/mugs.jpg', 3),
    ('550e8400-e29b-41d4-a716-446655440004', 'Phone Cases', 'Protective phone cases with custom designs', 'phone-cases', '/images/categories/phone-cases.jpg', 4),
    ('550e8400-e29b-41d4-a716-446655440005', 'Posters', 'Premium quality posters for wall art', 'posters', '/images/categories/posters.jpg', 5),
    ('550e8400-e29b-41d4-a716-446655440006', 'Stickers', 'Durable vinyl stickers for any surface', 'stickers', '/images/categories/stickers.jpg', 6);

-- Insert sample products
INSERT INTO public.products (id, name, description, slug, base_price, category_id, tags, sku, weight, dimensions, materials, care_instructions, featured) VALUES
    ('650e8400-e29b-41d4-a716-446655440001', 'Classic Cotton T-Shirt', 'Premium 100% cotton t-shirt perfect for custom designs. Soft, comfortable, and durable.', 'classic-cotton-tshirt', 19.99, '550e8400-e29b-41d4-a716-446655440001', ARRAY['cotton', 'basic', 'unisex'], 'TSH-001', 0.2, '{"width": 20, "height": 28, "unit": "inches"}'::jsonb, ARRAY['100% Cotton'], 'Machine wash cold, tumble dry low', true),
    
    ('650e8400-e29b-41d4-a716-446655440002', 'Premium Hoodie', 'Cozy fleece hoodie with kangaroo pocket. Perfect for custom prints and embroidery.', 'premium-hoodie', 39.99, '550e8400-e29b-41d4-a716-446655440002', ARRAY['fleece', 'hoodie', 'warm'], 'HOD-001', 0.8, '{"width": 24, "height": 32, "unit": "inches"}'::jsonb, ARRAY['80% Cotton', '20% Polyester'], 'Machine wash cold, hang dry recommended', true),
    
    ('650e8400-e29b-41d4-a716-446655440003', 'Ceramic Coffee Mug', 'High-quality 11oz ceramic mug with smooth finish. Dishwasher and microwave safe.', 'ceramic-coffee-mug', 12.99, '550e8400-e29b-41d4-a716-446655440003', ARRAY['ceramic', 'dishwasher-safe', 'microwave-safe'], 'MUG-001', 0.5, '{"diameter": 3.2, "height": 3.7, "unit": "inches"}'::jsonb, ARRAY['Ceramic'], 'Dishwasher safe, microwave safe', false),
    
    ('650e8400-e29b-41d4-a716-446655440004', 'iPhone 15 Case', 'Durable TPU case for iPhone 15. Precise cutouts and wireless charging compatible.', 'iphone-15-case', 24.99, '550e8400-e29b-41d4-a716-446655440004', ARRAY['iphone', 'tpu', 'wireless-charging'], 'PHN-001', 0.05, '{"width": 2.8, "height": 5.8, "depth": 0.3, "unit": "inches"}'::jsonb, ARRAY['TPU'], 'Clean with damp cloth', false),
    
    ('650e8400-e29b-41d4-a716-446655440005', 'Premium Poster', 'High-quality matte finish poster. Available in multiple sizes.', 'premium-poster', 15.99, '550e8400-e29b-41d4-a716-446655440005', ARRAY['poster', 'matte', 'wall-art'], 'PST-001', 0.1, '{"width": 18, "height": 24, "unit": "inches"}'::jsonb, ARRAY['Premium Paper'], 'Handle with care, avoid moisture', true),
    
    ('650e8400-e29b-41d4-a716-446655440006', 'Vinyl Sticker Pack', 'Waterproof vinyl stickers. Perfect for laptops, water bottles, and more.', 'vinyl-sticker-pack', 8.99, '550e8400-e29b-41d4-a716-446655440006', ARRAY['vinyl', 'waterproof', 'pack'], 'STK-001', 0.02, '{"width": 3, "height": 3, "unit": "inches"}'::jsonb, ARRAY['Vinyl'], 'Clean surface before application', false);

-- Insert product images
INSERT INTO public.product_images (product_id, url, alt_text, is_primary, sort_order) VALUES
    ('650e8400-e29b-41d4-a716-446655440001', '/images/products/tshirt-white-front.jpg', 'Classic Cotton T-Shirt - White Front View', true, 1),
    ('650e8400-e29b-41d4-a716-446655440001', '/images/products/tshirt-white-back.jpg', 'Classic Cotton T-Shirt - White Back View', false, 2),
    ('650e8400-e29b-41d4-a716-446655440001', '/images/products/tshirt-black-front.jpg', 'Classic Cotton T-Shirt - Black Front View', false, 3),
    
    ('650e8400-e29b-41d4-a716-446655440002', '/images/products/hoodie-gray-front.jpg', 'Premium Hoodie - Gray Front View', true, 1),
    ('650e8400-e29b-41d4-a716-446655440002', '/images/products/hoodie-gray-back.jpg', 'Premium Hoodie - Gray Back View', false, 2),
    
    ('650e8400-e29b-41d4-a716-446655440003', '/images/products/mug-white.jpg', 'Ceramic Coffee Mug - White', true, 1),
    ('650e8400-e29b-41d4-a716-446655440003', '/images/products/mug-black.jpg', 'Ceramic Coffee Mug - Black', false, 2),
    
    ('650e8400-e29b-41d4-a716-446655440004', '/images/products/iphone-case-clear.jpg', 'iPhone 15 Case - Clear', true, 1),
    ('650e8400-e29b-41d4-a716-446655440004', '/images/products/iphone-case-black.jpg', 'iPhone 15 Case - Black', false, 2),
    
    ('650e8400-e29b-41d4-a716-446655440005', '/images/products/poster-mockup.jpg', 'Premium Poster - Mockup', true, 1),
    
    ('650e8400-e29b-41d4-a716-446655440006', '/images/products/sticker-pack.jpg', 'Vinyl Sticker Pack', true, 1);

-- Insert customization options for T-Shirt
INSERT INTO public.customization_options (product_id, type, name, description, required, options, price_modifier, max_length, sort_order) VALUES
    ('650e8400-e29b-41d4-a716-446655440001', 'text', 'Front Text', 'Add custom text to the front of the t-shirt', false, '[{"label": "Font", "options": ["Arial", "Helvetica", "Times New Roman", "Comic Sans MS"]}, {"label": "Size", "options": ["Small", "Medium", "Large", "Extra Large"]}]'::jsonb, 0, 50, 1),
    ('650e8400-e29b-41d4-a716-446655440001', 'text', 'Back Text', 'Add custom text to the back of the t-shirt', false, '[{"label": "Font", "options": ["Arial", "Helvetica", "Times New Roman", "Comic Sans MS"]}, {"label": "Size", "options": ["Small", "Medium", "Large", "Extra Large"]}]'::jsonb, 2.00, 50, 2),
    ('650e8400-e29b-41d4-a716-446655440001', 'color', 'Text Color', 'Choose the color for your text', false, '[{"name": "Black", "hex": "#000000"}, {"name": "White", "hex": "#FFFFFF"}, {"name": "Red", "hex": "#FF0000"}, {"name": "Blue", "hex": "#0000FF"}, {"name": "Green", "hex": "#00FF00"}]'::jsonb, 0, null, 3),
    ('650e8400-e29b-41d4-a716-446655440001', 'size', 'T-Shirt Size', 'Select your t-shirt size', true, '[{"size": "XS", "label": "Extra Small"}, {"size": "S", "label": "Small"}, {"size": "M", "label": "Medium"}, {"size": "L", "label": "Large"}, {"size": "XL", "label": "Extra Large"}, {"size": "XXL", "label": "2X Large", "price_modifier": 3.00}]'::jsonb, 0, null, 4),
    ('650e8400-e29b-41d4-a716-446655440001', 'image', 'Custom Image', 'Upload your own image design', false, '[{"max_size": "10MB", "formats": ["PNG", "JPG", "JPEG", "SVG"]}]'::jsonb, 5.00, null, 5);

-- Insert customization options for Hoodie
INSERT INTO public.customization_options (product_id, type, name, description, required, options, price_modifier, max_length, sort_order) VALUES
    ('650e8400-e29b-41d4-a716-446655440002', 'text', 'Front Text', 'Add custom text to the front of the hoodie', false, '[{"label": "Font", "options": ["Arial", "Helvetica", "Times New Roman", "Comic Sans MS"]}, {"label": "Size", "options": ["Small", "Medium", "Large", "Extra Large"]}]'::jsonb, 0, 50, 1),
    ('650e8400-e29b-41d4-a716-446655440002', 'size', 'Hoodie Size', 'Select your hoodie size', true, '[{"size": "S", "label": "Small"}, {"size": "M", "label": "Medium"}, {"size": "L", "label": "Large"}, {"size": "XL", "label": "Extra Large"}, {"size": "XXL", "label": "2X Large", "price_modifier": 5.00}]'::jsonb, 0, null, 2),
    ('650e8400-e29b-41d4-a716-446655440002', 'color', 'Hoodie Color', 'Choose the base color of your hoodie', true, '[{"name": "Gray", "hex": "#808080"}, {"name": "Black", "hex": "#000000"}, {"name": "Navy", "hex": "#000080"}, {"name": "Maroon", "hex": "#800000"}]'::jsonb, 0, null, 3);

-- Insert customization options for Mug
INSERT INTO public.customization_options (product_id, type, name, description, required, options, price_modifier, max_length, sort_order) VALUES
    ('650e8400-e29b-41d4-a716-446655440003', 'text', 'Custom Text', 'Add personalized text to your mug', false, '[{"label": "Font", "options": ["Arial", "Helvetica", "Times New Roman", "Script"]}, {"label": "Position", "options": ["Center", "Left", "Right"]}]'::jsonb, 0, 30, 1),
    ('650e8400-e29b-41d4-a716-446655440003', 'image', 'Custom Image', 'Upload your own image for the mug', false, '[{"max_size": "5MB", "formats": ["PNG", "JPG", "JPEG"]}]'::jsonb, 3.00, null, 2),
    ('650e8400-e29b-41d4-a716-446655440003', 'color', 'Mug Color', 'Choose the base color of your mug', true, '[{"name": "White", "hex": "#FFFFFF"}, {"name": "Black", "hex": "#000000"}, {"name": "Blue", "hex": "#0000FF"}]'::jsonb, 0, null, 3);

-- Insert customization options for Phone Case
INSERT INTO public.customization_options (product_id, type, name, description, required, options, price_modifier, max_length, sort_order) VALUES
    ('650e8400-e29b-41d4-a716-446655440004', 'image', 'Case Design', 'Upload your custom design for the phone case', false, '[{"max_size": "10MB", "formats": ["PNG", "JPG", "JPEG"]}]'::jsonb, 0, null, 1),
    ('650e8400-e29b-41d4-a716-446655440004', 'text', 'Custom Text', 'Add text to your phone case', false, '[{"label": "Font", "options": ["Arial", "Helvetica", "Impact"]}, {"label": "Position", "options": ["Top", "Center", "Bottom"]}]'::jsonb, 2.00, 25, 2);

-- Insert customization options for Poster
INSERT INTO public.customization_options (product_id, type, name, description, required, options, price_modifier, max_length, sort_order) VALUES
    ('650e8400-e29b-41d4-a716-446655440005', 'image', 'Poster Image', 'Upload your image for the poster', true, '[{"max_size": "50MB", "formats": ["PNG", "JPG", "JPEG", "PDF"], "min_resolution": "300dpi"}]'::jsonb, 0, null, 1),
    ('650e8400-e29b-41d4-a716-446655440005', 'size', 'Poster Size', 'Select your poster size', true, '[{"size": "12x18", "label": "12\" x 18\"", "price_modifier": 0}, {"size": "18x24", "label": "18\" x 24\"", "price_modifier": 5.00}, {"size": "24x36", "label": "24\" x 36\"", "price_modifier": 15.00}]'::jsonb, 0, null, 2);

-- Insert customization options for Stickers
INSERT INTO public.customization_options (product_id, type, name, description, required, options, price_modifier, max_length, sort_order) VALUES
    ('650e8400-e29b-41d4-a716-446655440006', 'image', 'Sticker Design', 'Upload your design for the stickers', true, '[{"max_size": "10MB", "formats": ["PNG", "JPG", "JPEG", "SVG"]}]'::jsonb, 0, null, 1),
    ('650e8400-e29b-41d4-a716-446655440006', 'size', 'Sticker Size', 'Choose the size of your stickers', true, '[{"size": "2x2", "label": "2\" x 2\"", "price_modifier": 0}, {"size": "3x3", "label": "3\" x 3\"", "price_modifier": 2.00}, {"size": "4x4", "label": "4\" x 4\"", "price_modifier": 4.00}]'::jsonb, 0, null, 2),
    ('650e8400-e29b-41d4-a716-446655440006', 'text', 'Quantity', 'How many stickers do you want?', true, '[{"qty": 5, "label": "5 stickers", "price_modifier": 0}, {"qty": 10, "label": "10 stickers", "price_modifier": 5.00}, {"qty": 25, "label": "25 stickers", "price_modifier": 15.00}]'::jsonb, 0, null, 3);
