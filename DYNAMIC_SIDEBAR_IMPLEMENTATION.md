# Dynamic Sidebar Implementation

## Overview

This implementation adds dynamic sidebar content that updates based on the currently active view type in the mini preview canvas. The sidebar provides contextual controls that match what's displayed in the mini preview, creating a cohesive user experience.

## Key Features

### 1. **Bidirectional Synchronization**
- When main canvas shows 3D model view → sidebar displays 3D model switching controls
- When main canvas shows print files view → sidebar displays print file switching controls
- Real-time updates when view switching occurs

### 2. **Contextual Controls**
- **3D Model View**: Thumbnails and navigation for different GLB mockup perspectives (both in sidebar and mini preview)
- **Print Files View**: Thumbnails and navigation for multiple print files with slider functionality (both in sidebar and mini preview)

### 3. **Consistent UI/UX**
- Glass morphism styling matching existing design system
- Responsive layout adapting to content and screen size
- Smooth transitions and hover effects

## Implementation Details

### Components Created

#### 1. `VerticalPrintFileSelector` (`src/components/customizer/vertical-print-file-selector.tsx`)
- Displays print files in a vertical layout similar to the existing GLB selector
- Features:
  - Thumbnail previews with fallback to document icons
  - File name display with extension removal
  - Active file indicator with animation
  - Responsive sizing based on file count
  - Error handling for failed image loads

#### 2. `DynamicSidebar` (`src/components/customizer/dynamic-sidebar.tsx`)
- Main component that switches between 3D and print file selectors
- Features:
  - View-based conditional rendering
  - Fallback messages when no files are available
  - Proper prop forwarding to child components

#### 3. Updated `DualCanvasLayout` (`src/components/customizer/dual-canvas-layout.tsx`)
- Replaced hardcoded GLB selector with dynamic sidebar
- Features:
  - Conditional rendering based on available content (GLB files OR print files)
  - Proper view state synchronization using `viewState.miniCanvasView`

### View State Logic

The sidebar responds to `viewState.mainCanvasView` which determines what the main canvas shows:

```typescript
// When main canvas shows '3d' → sidebar shows 3D controls
// When main canvas shows 'printfile' → sidebar shows print file controls
<DynamicSidebar currentView={viewState.mainCanvasView} />
```

This creates the desired behavior where the sidebar provides controls for whatever is currently displayed in the main canvas.

### Styling Approach

- **Glass Morphism**: Consistent with existing design using `bg-white/10 backdrop-blur-md`
- **Color Coding**: 
  - 3D models: Blue/purple gradient for active state
  - Print files: Green/blue gradient for active state
- **Responsive Design**: Adapts button sizes and text based on content count
- **Animations**: Smooth transitions, hover effects, and pulse animations for active states

## Testing

Comprehensive test suite (`src/tests/dynamic-sidebar.test.tsx`) covers:
- ✅ Correct component rendering based on view type
- ✅ User interaction handling (clicks, selections)
- ✅ Fallback behavior when no files are available
- ✅ Dynamic view switching functionality
- ✅ Proper callback execution

## Usage Example

```tsx
<DynamicSidebar
  currentView={viewState.miniCanvasView}
  glbFiles={glbFiles}
  selectedGLBIndex={selectedGLBIndex}
  onGLBSelect={onGLBSelect}
  getGLBUrl={getGLBUrl}
  printFiles={printFiles}
  selectedPrintFileIndex={selectedPrintFileIndex}
  onPrintFileSelect={onPrintFileSelect}
  getFileUrl={getFileUrl}
/>
```

## New Feature: Mini Preview Navigation

### GLB Navigation in Mini Preview
Added comprehensive 3D model navigation controls to the mini preview when it displays 3D models:

- **Navigation Arrows**: Left/right arrows for quick model switching
- **Thumbnail Grid**: Visual thumbnails with 3D cube icons for each GLB file
- **Indicator Dots**: Small dots showing current selection and allowing direct navigation
- **Visual Feedback**: Purple color scheme for active GLB selection (vs blue for print files)

### Updated MiniPreviewCanvas
- Added `onGLBSelect` handler to the interface
- Implemented `navigateGLB` function for keyboard/arrow navigation
- Added GLB thumbnail slider similar to print file slider
- Consistent styling and interaction patterns

## Benefits

1. **Enhanced UX**: Users get contextual controls that match what they're viewing
2. **Consistent Interface**: All three components (main canvas, mini preview, sidebar) stay synchronized
3. **Efficient Navigation**: Easy switching between different models and print files in both sidebar AND mini preview
4. **Responsive Design**: Works well on different screen sizes
5. **Maintainable Code**: Clean separation of concerns with reusable components
6. **Complete Navigation**: Both 3D models and print files now have full navigation controls in mini preview

## Future Enhancements

- Add keyboard navigation support
- Implement drag-and-drop reordering
- Add preview tooltips on hover
- Support for additional file types
- Batch selection capabilities
