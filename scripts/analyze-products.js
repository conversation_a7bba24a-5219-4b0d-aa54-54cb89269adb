#!/usr/bin/env node

/**
 * Product Analysis Script
 * Analyzes PopCustoms GLB files and generates product catalog
 */

const path = require('path');
const fs = require('fs');

// Import our analysis tools (we'll need to compile TypeScript first)
async function analyzeProducts() {
  console.log('🚀 Starting PopCustoms Product Analysis');
  console.log('=====================================');

  // Path to PopCustoms assets
  const basePath = path.join(__dirname, '..', 'Competitors', 'popcustoms.com (2)', '3ddesigner.popcustoms.com');

  console.log(`📁 Base path: ${basePath}`);

  // Check if directory exists
  if (!fs.existsSync(basePath)) {
    console.error('❌ PopCustoms directory not found!');
    console.error('Expected path:', basePath);
    return;
  }

  // List available products
  try {
    const entries = fs.readdirSync(basePath, { withFileTypes: true });
    const products = entries
      .filter(entry => entry.isDirectory())
      .map(entry => entry.name)
      .filter(name => !name.startsWith('.') && name !== 'ext');

    console.log(`📦 Found ${products.length} products:`);
    products.forEach((product, index) => {
      console.log(`  ${index + 1}. ${product}`);
    });

    // Analyze first product (YS_C11) as test
    if (products.length > 0) {
      const testProduct = products[0];
      console.log(`\n🔍 Analyzing test product: ${testProduct}`);

      const productPath = path.join(basePath, testProduct);
      const visualModelsPath = path.join(productPath, 'VisualModels');

      if (fs.existsSync(visualModelsPath)) {
        const glbFiles = fs.readdirSync(visualModelsPath)
          .filter(file => file.endsWith('.glb'));

        console.log(`📄 GLB files found:`);
        glbFiles.forEach(file => {
          const filePath = path.join(visualModelsPath, file);
          const stats = fs.statSync(filePath);
          console.log(`  - ${file} (${(stats.size / 1024).toFixed(1)} KB)`);
        });

        // Check for assets
        const assetsPath = path.join(visualModelsPath, 'Assets', testProduct);
        if (fs.existsSync(assetsPath)) {
          console.log(`\n🎨 Assets found:`);

          const texturePath = path.join(assetsPath, 'Texture');
          if (fs.existsSync(texturePath)) {
            const textures = fs.readdirSync(texturePath);
            console.log(`  Textures (${textures.length}):`);
            textures.forEach(texture => {
              const stats = fs.statSync(path.join(texturePath, texture));
              console.log(`    - ${texture} (${(stats.size / 1024).toFixed(1)} KB)`);
            });
          }

          const previewPath = path.join(assetsPath, 'Previews');
          if (fs.existsSync(previewPath)) {
            const previews = fs.readdirSync(previewPath);
            console.log(`  Previews (${previews.length}):`);
            previews.forEach(preview => {
              const stats = fs.statSync(path.join(previewPath, preview));
              console.log(`    - ${preview} (${(stats.size / 1024).toFixed(1)} KB)`);
            });
          }
        }
      }
    }

    console.log('\n✅ Basic analysis complete!');
    console.log('\n📋 Next Steps:');
    console.log('1. Run: npm run build (to compile TypeScript)');
    console.log('2. Run: node scripts/run-glb-analysis.js (to run full GLB analysis)');
    console.log('3. Check output in: analysis-results/');

  } catch (error) {
    console.error('❌ Error during analysis:', error);
  }
}

// Run the analysis
analyzeProducts().catch(console.error);