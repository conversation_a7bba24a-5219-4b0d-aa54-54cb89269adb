const { createClient } = require('@supabase/supabase-js')
const fs = require('fs')
const path = require('path')

// Load environment variables
require('dotenv').config({ path: '.env.local' })

async function apply3DColumnsMigration() {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
  const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

  if (!supabaseUrl || !supabaseServiceKey) {
    console.error('Missing Supabase environment variables')
    process.exit(1)
  }

  const supabase = createClient(supabaseUrl, supabaseServiceKey, {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  })

  try {
    console.log('Applying 3D file columns migration...')
    
    // Apply the migration SQL directly
    const migrationSQL = `
      -- Add 3D file columns to products table for enhanced customizer support
      ALTER TABLE public.products
      ADD COLUMN IF NOT EXISTS glb_files JSONB DEFAULT '[]'::jsonb,
      ADD COLUMN IF NOT EXISTS texture_files JSONB DEFAULT '[]'::jsonb,
      ADD COLUMN IF NOT EXISTS preview_files JSONB DEFAULT '[]'::jsonb;

      -- Add comments to explain the new columns
      COMMENT ON COLUMN public.products.glb_files IS 'Array of GLB/GLTF files for 3D customizer - [{url: string, filename: string, file_type: string, file_size: number}]';
      COMMENT ON COLUMN public.products.texture_files IS 'Array of texture files for 3D models - [{url: string, filename: string, file_type: string, file_size: number}]';
      COMMENT ON COLUMN public.products.preview_files IS 'Array of preview images for 3D scenes - [{url: string, filename: string, file_type: string, file_size: number}]';

      -- Create indexes for better performance when querying products with 3D files
      CREATE INDEX IF NOT EXISTS idx_products_glb_files ON public.products USING GIN (glb_files);
      CREATE INDEX IF NOT EXISTS idx_products_texture_files ON public.products USING GIN (texture_files);
      CREATE INDEX IF NOT EXISTS idx_products_preview_files ON public.products USING GIN (preview_files);
    `

    // Split the SQL into individual statements and execute them
    const statements = migrationSQL
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'))

    for (const statement of statements) {
      if (statement.trim()) {
        console.log('Executing:', statement.substring(0, 100) + '...')
        
        // Use raw SQL execution
        const { error } = await supabase.rpc('exec', { sql: statement + ';' })
        
        if (error) {
          console.error('Error executing statement:', error)
          console.error('Statement:', statement)
          // Continue with other statements
        } else {
          console.log('✓ Statement executed successfully')
        }
      }
    }

    console.log('✅ 3D columns migration completed!')

    // Verify the columns were added
    const { data, error } = await supabase
      .from('products')
      .select('id, glb_files, texture_files, preview_files')
      .limit(1)

    if (error) {
      console.error('❌ Error verifying migration:', error)
    } else {
      console.log('✅ Migration verified - columns are accessible')
    }

  } catch (error) {
    console.error('❌ Error applying migration:', error)
    process.exit(1)
  }
}

apply3DColumnsMigration()
