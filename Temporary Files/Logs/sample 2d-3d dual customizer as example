<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>2D and 3D Dual Editable Editor</title>
  <style>
    html, body {
      margin: 0;
      height: 100%;
      overflow: hidden;
      display: flex;
    }
    #editor {
      width: 30%;
      display: flex;
      flex-direction: column;
      background: #fafafa;
      border-right: 1px solid #ccc;
    }
    #controls {
      padding: 10px;
      background: #eee;
    }
    #fabric-wrapper {
      flex: 1;
      position: relative;
    }
    #fabric-canvas {
      width: 100%;
      height: 100%;
      cursor: text;
    }
    #three-container {
      flex: 1;
      position: relative;
    }
    canvas {
      display: block;
    }
  </style>
</head>
<body>
  <div id="editor">
    <div id="controls">
      <button onclick="addText()">Add Text</button>
      <input type="file" id="upload" accept="image/*">
    </div>
    <div id="fabric-wrapper">
      <canvas id="fabric-canvas" width="1024" height="1024"></canvas>
    </div>
  </div>
  <div id="three-container"></div>

  <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/90/three.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/fabric.js/2.2.1/fabric.min.js"></script>

  <script>
    // Fabric.js setup
    const canvasEl = document.getElementById('fabric-canvas');
    const fabricCanvas = new fabric.Canvas('fabric-canvas', {
      backgroundColor: 'white',
      selection: true
    });
    fabricCanvas.calcOffset();

    const canvasTexture = new THREE.CanvasTexture(canvasEl);
    fabricCanvas.on('after:render', () => canvasTexture.needsUpdate = true);

    function addText() {
      const text = new fabric.IText('New Text', {
        left: 100, top: 100, fontSize: 28, fill: '#007bff'
      });
      fabricCanvas.add(text);
      fabricCanvas.setActiveObject(text);
    }
    document.getElementById('upload').addEventListener('change', e => {
      const reader = new FileReader();
      reader.onload = ev => fabric.Image.fromURL(ev.target.result, img => {
        img.set({ left: 150, top: 150, scaleX: 0.4, scaleY: 0.4 });
        fabricCanvas.add(img);
        fabricCanvas.setActiveObject(img);
      });
      reader.readAsDataURL(e.target.files[0]);
    });

    // Three.js setup
    const renderer = new THREE.WebGLRenderer({ antialias: true });
    const threeContainer = document.getElementById('three-container');
    threeContainer.appendChild(renderer.domElement);
    const scene = new THREE.Scene();
    const camera = new THREE.PerspectiveCamera(60, 1, 1, 1000);
    camera.position.set(0, 0, 10);
    scene.add(new THREE.AmbientLight(0xffffff, 1));
    const dirLight = new THREE.DirectionalLight(0xffffff, 0.5);
    dirLight.position.set(5, 5, 5);
    scene.add(dirLight);

    // Create rippled sphere geometry
    const sphereGeo = new THREE.SphereGeometry(4.5, 80, 80);
    sphereGeo.vertices.forEach(v => {
      const amp = 0.25, freq = 4;
      const spherical = new THREE.Spherical().setFromVector3(v);
      const offset = Math.sin(spherical.theta * freq) * amp + Math.cos(spherical.phi * freq) * amp;
      v.normalize().multiplyScalar(4.5 + offset);
    });
    sphereGeo.computeFaceNormals();
    sphereGeo.computeVertexNormals();
    const material = new THREE.MeshStandardMaterial({ map: canvasTexture, metalness: 0.3, roughness: 0.4, side: THREE.DoubleSide });
    const sphere = new THREE.Mesh(sphereGeo, material);
    scene.add(sphere);

    // Raycaster and dragging logic
    const raycaster = new THREE.Raycaster();
    const mouse = new THREE.Vector2();
    let is3dDragging = false;
    let lastPt = null;

    function getFabricCoords(event) {
      const rect3 = renderer.domElement.getBoundingClientRect();
      mouse.x = ((event.clientX - rect3.left) / rect3.width) * 2 - 1;
      mouse.y = -((event.clientY - rect3.top) / rect3.height) * 2 + 1;
      raycaster.setFromCamera(mouse, camera);
      const ints = raycaster.intersectObject(sphere);
      if (!ints.length || !ints[0].uv) return null;
      const uv = ints[0].uv;
      const rect2 = fabricCanvas.upperCanvasEl.getBoundingClientRect();
      const x = uv.x * rect2.width + rect2.left;
      const y = (1 - uv.y) * rect2.height + rect2.top;
      return { x, y };
    }

    renderer.domElement.addEventListener('pointerdown', ev => {
      const pt = getFabricCoords(ev);
      if (!pt) return;
      const hitEvt = new MouseEvent('mousedown', { clientX: pt.x, clientY: pt.y, bubbles: true, cancelable: true });
      const target = fabricCanvas.findTarget(hitEvt, false);
      if (target) {
        fabricCanvas.upperCanvasEl.dispatchEvent(hitEvt);
        is3dDragging = true;
        lastPt = pt;
      }
    });

    renderer.domElement.addEventListener('pointermove', ev => {
      if (!is3dDragging) return;
      const pt = getFabricCoords(ev);
      if (!pt) return;
      lastPt = pt;
      const moveEvt = new MouseEvent('mousemove', { clientX: pt.x, clientY: pt.y, bubbles: true, cancelable: true });
      fabricCanvas.upperCanvasEl.dispatchEvent(moveEvt);
    });

    renderer.domElement.addEventListener('pointerup', ev => {
      if (!is3dDragging) return;
      const pt = getFabricCoords(ev) || lastPt;
      const upEvt = new MouseEvent('mouseup', { clientX: pt.x, clientY: pt.y, bubbles: true, cancelable: true });
      fabricCanvas.upperCanvasEl.dispatchEvent(upEvt);
      is3dDragging = false;
      lastPt = null;
    });

    // Resize and render loops
    function resize() {
      const w3 = threeContainer.clientWidth, h3 = window.innerHeight;
      renderer.setSize(w3, h3);
      camera.aspect = w3 / h3;
      camera.updateProjectionMatrix();
      fabricCanvas.calcOffset();
    }
    function animate() {
      requestAnimationFrame(animate);
      renderer.render(scene, camera);
    }
    window.addEventListener('resize', resize);
    resize();
    animate();
  </script>
</body>
</html>
