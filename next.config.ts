import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  // Webpack configuration removed - no longer needed without Konva
  async headers() {
    return [
      {
        // Apply headers to the Shopify embedded app page
        source: '/shopify',
        headers: [
          {
            key: 'X-Frame-Options',
            value: 'ALLOWALL'
          },
          {
            key: 'Content-Security-Policy',
            value: "frame-ancestors 'self' https://*.shopify.com https://admin.shopify.com"
          }
        ]
      },
      {
        // Apply headers to the installation endpoint for iframe embedding
        source: '/api/auth/shopify/install',
        headers: [
          {
            key: 'X-Frame-Options',
            value: 'ALLOWALL'
          },
          {
            key: 'Content-Security-Policy',
            value: "frame-ancestors 'self' https://*.shopify.com https://admin.shopify.com"
          }
        ]
      }
    ]
  }
};

export default nextConfig;
