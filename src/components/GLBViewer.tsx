'use client';

import React, { useRef, useEffect, useState } from 'react';
import * as THREE from 'three';
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader.js';
import { DRACOLoader } from 'three/examples/jsm/loaders/DRACOLoader.js';
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js';
import UVExtractor, { DesignAreaUV } from '@/lib/uv-extractor';
import { FinalTextureEngine } from '@/lib/final-texture-engine';

interface GLBViewerProps {
  glbPath: string;
  width?: number;
  height?: number;
  showWireframe?: boolean;
  highlightDesignAreas?: boolean;
  onMeshClick?: (meshName: string) => void;
  onDesignAreasExtracted?: (designAreas: DesignAreaUV[]) => void;
  onTextureEngineReady?: (textureEngine: FinalTextureEngine) => void;
}

interface MeshInfo {
  name: string;
  vertices: number;
  faces: number;
  hasUV: boolean;
  material: string;
}

export default function GLBViewer({
  glbPath,
  width = 800,
  height = 600,
  showWireframe = false,
  highlightDesignAreas = true,
  onMeshClick,
  onDesignAreasExtracted,
  onTextureEngineReady
}: GLBViewerProps) {
  const mountRef = useRef<HTMLDivElement>(null);
  const sceneRef = useRef<THREE.Scene | null>(null);
  const rendererRef = useRef<THREE.WebGLRenderer | null>(null);
  const cameraRef = useRef<THREE.OrthographicCamera | null>(null);
  const controlsRef = useRef<OrbitControls | null>(null);
  const textureEngineRef = useRef<FinalTextureEngine | null>(null);
  const [meshes, setMeshes] = useState<MeshInfo[]>([]);
  const [designAreas, setDesignAreas] = useState<DesignAreaUV[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showModelInfo, setShowModelInfo] = useState(true);

  useEffect(() => {
    if (!mountRef.current) return;

    // Initialize Three.js scene
    const scene = new THREE.Scene();
    scene.background = new THREE.Color(0xf0f0f0);
    sceneRef.current = scene;

    // Initialize ORTHOGRAPHIC camera to match Blender's Front Orthographic view exactly
    const frustumSize = 4; // Very small number = very zoomed in (very big model)
    const aspect = width / height;
    const camera = new THREE.OrthographicCamera(
      frustumSize * aspect / -2, // left
      frustumSize * aspect / 2,  // right
      frustumSize / 2,           // top
      frustumSize / -2,          // bottom
      0.1,                       // near
      1000                       // far
    );
    // Position camera for Front view (looking along negative Z axis toward positive Z)
    camera.position.set(0, 0, -10); // Position on negative Z axis (Front view)
    camera.up.set(0, 1, 0); // Y-up orientation
    camera.lookAt(0, 0, 0); // Look toward origin
    cameraRef.current = camera;

    // Initialize renderer
    const renderer = new THREE.WebGLRenderer({ antialias: true });
    renderer.setSize(width, height);
    renderer.shadowMap.enabled = true;
    renderer.shadowMap.type = THREE.PCFSoftShadowMap;
    rendererRef.current = renderer;

    // Add renderer to DOM
    mountRef.current.appendChild(renderer.domElement);

    // Initialize controls - DISABLED for fixed stationary view
    const controls = new OrbitControls(camera, renderer.domElement);
    controls.enabled = false; // Disable all controls for fixed view
    controlsRef.current = controls;

    // Add lighting
    const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
    scene.add(ambientLight);

    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
    directionalLight.position.set(10, 10, 5);
    directionalLight.castShadow = true;
    scene.add(directionalLight);

    // Add grid helper
    const gridHelper = new THREE.GridHelper(10, 10);
    scene.add(gridHelper);

    // Initialize final texture engine
    textureEngineRef.current = new FinalTextureEngine(renderer);

    // Notify parent component that texture engine is ready
    if (onTextureEngineReady) {
      onTextureEngineReady(textureEngineRef.current);
    }

    // Load GLB file
    loadGLB();

    // Animation loop - no controls update needed for fixed view
    const animate = () => {
      requestAnimationFrame(animate);
      renderer.render(scene, camera);
    };
    animate();

    // Cleanup
    return () => {
      if (mountRef.current && renderer.domElement) {
        mountRef.current.removeChild(renderer.domElement);
      }
      renderer.dispose();
      controls.dispose();
    };
  }, [glbPath, width, height]);

  const loadGLB = async () => {
    if (!sceneRef.current) return;

    setLoading(true);
    setError(null);

    try {
      const loader = new GLTFLoader();

      // Setup Draco loader with correct decoder files
      const dracoLoader = new DRACOLoader();
      dracoLoader.setDecoderPath('/draco/');
      loader.setDRACOLoader(dracoLoader);

      // Try to load GLB with explicit fetch first to handle CORS/MIME type issues
      let gltf;
      try {
        if (glbPath.startsWith('http')) {
          const response = await fetch(glbPath);
          if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
          }
          const arrayBuffer = await response.arrayBuffer();

          gltf = await new Promise<any>((resolve, reject) => {
            loader.parse(
              arrayBuffer,
              '',
              resolve,
              reject
            );
          });
        } else {
          gltf = await new Promise<any>((resolve, reject) => {
            loader.load(
              glbPath,
              resolve,
              (progress) => {
                console.log(`Loading progress: ${(progress.loaded / progress.total * 100).toFixed(1)}%`);
              },
              reject
            );
          });
        }
      } catch (error) {
        console.error('GLBViewer: Failed to load GLB:', error);
        throw error;
      }

      // Clear previous models
      const objectsToRemove = sceneRef.current.children.filter(child =>
        child.userData.isModel
      );
      objectsToRemove.forEach(obj => sceneRef.current!.remove(obj));

      // Add new model
      gltf.scene.userData.isModel = true;
      sceneRef.current.add(gltf.scene);

      // Extract UV mappings from design areas
      const extractedDesignAreas = UVExtractor.extractFromScene(gltf.scene);
      setDesignAreas(extractedDesignAreas);

      // Notify parent component about extracted design areas
      if (onDesignAreasExtracted) {
        onDesignAreasExtracted(extractedDesignAreas);
      }

      // Analyze meshes
      const meshInfos: MeshInfo[] = [];
      gltf.scene.traverse((object: THREE.Object3D) => {
        if (object instanceof THREE.Mesh) {
          const mesh = object as THREE.Mesh;
          const geometry = mesh.geometry;
          const material = mesh.material;

          // Collect mesh information
          const meshInfo: MeshInfo = {
            name: mesh.name || 'Unnamed',
            vertices: geometry.attributes.position?.count || 0,
            faces: geometry.index ? geometry.index.count / 3 : 0,
            hasUV: !!geometry.attributes.uv,
            material: Array.isArray(material) ? material[0].type : material.type
          };
          meshInfos.push(meshInfo);

          // Store original material for all meshes
          if (!mesh.userData.originalMaterial) {
            mesh.userData.originalMaterial = mesh.material;
          }

          // Hide mask pieces
          if (isMaskPiece(mesh.name)) {
            mesh.visible = false;
            console.log(`🎭 Hidden mask piece: ${mesh.name}`);
          }

          // Highlight design areas
          if (highlightDesignAreas && isDesignArea(mesh.name)) {
            highlightMesh(mesh);
          }

          // Add click handler
          if (onMeshClick) {
            mesh.userData.clickHandler = () => onMeshClick(mesh.name);
          }
        }
      });

      setMeshes(meshInfos);

      // Center and scale model
      const box = new THREE.Box3().setFromObject(gltf.scene);
      const center = box.getCenter(new THREE.Vector3());
      const size = box.getSize(new THREE.Vector3());
      const maxDim = Math.max(size.x, size.y, size.z);
      const scale = 4 / maxDim;

      gltf.scene.scale.setScalar(scale);
      gltf.scene.position.copy(center).multiplyScalar(-scale);

      // Add the model to the scene
      sceneRef.current?.add(gltf.scene);

      setLoading(false);

    } catch (err) {
      console.error('Error loading GLB:', err);
      setError(err instanceof Error ? err.message : 'Failed to load GLB file');
      setLoading(false);
    }
  };

  const isDesignArea = (name: string): boolean => {
    const designAreaPatterns = [
      'front', 'back', 'left', 'right', 'sleeve', 'collar', 'chest', 'pocket'
    ];
    const lowerName = name.toLowerCase();
    return designAreaPatterns.some(pattern => lowerName.includes(pattern));
  };

  const isMaskPiece = (name: string): boolean => {
    const maskPatterns = [
      'mask', 'frame', 'border', 'outline', 'guide', 'helper', 'viewport', 'camera'
    ];
    const lowerName = name.toLowerCase();
    return maskPatterns.some(pattern => lowerName.includes(pattern));
  };

  const highlightMesh = (mesh: THREE.Mesh) => {
    // Store original material for restoration
    if (!mesh.userData.originalMaterial) {
      mesh.userData.originalMaterial = mesh.material;
    }

    // Create wireframe overlay for design areas (only if no texture is applied)
    const material = Array.isArray(mesh.material) ? mesh.material[0] : mesh.material;
    if (!(material as any).map) {
      const wireframeGeometry = mesh.geometry.clone();
      const wireframeMaterial = new THREE.WireframeGeometry(wireframeGeometry);
      const wireframe = new THREE.LineSegments(
        wireframeMaterial,
        new THREE.LineBasicMaterial({ color: 0x00ff00, linewidth: 2 })
      );

      // Store wireframe reference for later removal
      mesh.userData.wireframe = wireframe;
      mesh.add(wireframe);
    }
  };

  return (
    <div className="glb-viewer">
      <div ref={mountRef} className="viewer-container" />

      {loading && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-100 bg-opacity-75">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Loading GLB model...</p>
          </div>
        </div>
      )}

      {error && (
        <div className="absolute inset-0 flex items-center justify-center bg-red-100 bg-opacity-75">
          <div className="text-center text-red-600">
            <p className="font-semibold">Error loading model</p>
            <p className="text-sm">{error}</p>
          </div>
        </div>
      )}

      {/* Toggle Button for Model Info */}
      <button
        onClick={() => setShowModelInfo(!showModelInfo)}
        className="absolute top-4 left-4 bg-white p-2 rounded-lg shadow-lg text-black hover:bg-gray-100 z-10"
        title={showModelInfo ? 'Hide Model Info' : 'Show Model Info'}
      >
        {showModelInfo ? '📊 Hide Info' : '📊 Show Info'}
      </button>

      {/* Mesh Information Panel - Moved to bottom left */}
      {meshes.length > 0 && showModelInfo && (
        <div className="absolute bottom-4 left-4 bg-white p-4 rounded-lg shadow-lg max-w-xs">
          <h3 className="font-semibold mb-2 text-black">Model Information</h3>
          <div className="text-sm space-y-1 text-black">
            <p><strong>Total Meshes:</strong> {meshes.length}</p>
            <p><strong>Total Vertices:</strong> {meshes.reduce((sum, m) => sum + m.vertices, 0).toLocaleString()}</p>
            <p><strong>Total Faces:</strong> {meshes.reduce((sum, m) => sum + m.faces, 0).toLocaleString()}</p>
          </div>

          <div className="mt-3">
            <h4 className="font-medium mb-1 text-black">Design Areas ({designAreas.length}):</h4>
            <div className="max-h-32 overflow-y-auto">
              {designAreas.map((area, index) => (
                <div key={index} className="text-xs p-1 bg-green-50 rounded mb-1">
                  <div className="font-medium text-green-800">{area.areaName}</div>
                  <div className="text-green-700">
                    UV: {area.mapping.uvCoordinates.length / 2} coords •
                    Texture: {area.mapping.textureSize.width}x{area.mapping.textureSize.height}
                  </div>
                  <div className="text-green-600 text-xs">
                    Bounds: [{area.mapping.boundingBox.min[0].toFixed(2)}, {area.mapping.boundingBox.min[1].toFixed(2)}]
                    to [{area.mapping.boundingBox.max[0].toFixed(2)}, {area.mapping.boundingBox.max[1].toFixed(2)}]
                  </div>
                </div>
              ))}
            </div>
          </div>

          <div className="mt-3">
            <h4 className="font-medium mb-1 text-black">Other Meshes:</h4>
            <div className="max-h-24 overflow-y-auto">
              {meshes.filter(m => !isDesignArea(m.name)).map((mesh, index) => (
                <div key={index} className="text-xs p-1 bg-gray-50 rounded mb-1">
                  <div className="font-medium text-black">{mesh.name}</div>
                  <div className="text-black">
                    {mesh.vertices} vertices • UV: {mesh.hasUV ? '✓' : '✗'}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      <style jsx>{`
        .glb-viewer {
          position: relative;
          width: ${width}px;
          height: ${height}px;
          border: 1px solid #e5e7eb;
          border-radius: 8px;
          overflow: hidden;
        }
        .viewer-container {
          width: 100%;
          height: 100%;
        }
      `}</style>
    </div>
  );
}