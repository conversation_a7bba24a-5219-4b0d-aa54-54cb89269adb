'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/components/providers/auth-provider'
import type { Database } from '@/lib/database.types'

interface ProtectedRouteProps {
  children: React.ReactNode
  requiredRole?: Database['public']['Enums']['user_role']
  fallbackPath?: string
  loadingComponent?: React.ReactNode
}

export function ProtectedRoute({ 
  children, 
  requiredRole, 
  fallbackPath = '/auth/signin',
  loadingComponent 
}: ProtectedRouteProps) {
  const { user, userProfile, isLoading } = useAuth()
  const router = useRouter()

  useEffect(() => {
    if (!isLoading) {
      // If no user, redirect to sign in
      if (!user) {
        router.push(fallbackPath)
        return
      }

      // If user exists but no profile yet, wait a bit more
      if (!userProfile) {
        return
      }

      // If specific role is required, check it
      if (requiredRole && userProfile.role !== requiredRole) {
        // Redirect based on user's actual role
        switch (userProfile.role) {
          case 'super_admin':
            router.push('/admin')
            break
          case 'merchant':
            router.push('/dashboard')
            break
          case 'customer':
            router.push('/')
            break
          default:
            router.push(fallbackPath)
        }
      }
    }
  }, [user, userProfile, isLoading, requiredRole, router, fallbackPath])

  // Show loading state
  if (isLoading || (user && !userProfile)) {
    return loadingComponent || (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  // If no user, don't render children (will redirect)
  if (!user) {
    return null
  }

  // If role is required and doesn't match, don't render children (will redirect)
  if (requiredRole && userProfile?.role !== requiredRole) {
    return null
  }

  // All checks passed, render children
  return <>{children}</>
}

// Convenience components for specific roles
export function SuperAdminRoute({ children, ...props }: Omit<ProtectedRouteProps, 'requiredRole'>) {
  return (
    <ProtectedRoute requiredRole="super_admin" {...props}>
      {children}
    </ProtectedRoute>
  )
}

export function MerchantRoute({ children, ...props }: Omit<ProtectedRouteProps, 'requiredRole'>) {
  return (
    <ProtectedRoute requiredRole="merchant" {...props}>
      {children}
    </ProtectedRoute>
  )
}

export function CustomerRoute({ children, ...props }: Omit<ProtectedRouteProps, 'requiredRole'>) {
  return (
    <ProtectedRoute requiredRole="customer" {...props}>
      {children}
    </ProtectedRoute>
  )
}

// Component that requires any authenticated user
export function AuthenticatedRoute({ children, ...props }: Omit<ProtectedRouteProps, 'requiredRole'>) {
  return (
    <ProtectedRoute {...props}>
      {children}
    </ProtectedRoute>
  )
}
