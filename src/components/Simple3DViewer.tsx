'use client';

import React, { useRef, useEffect, useState, useCallback } from 'react';
import * as THREE from 'three';
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader.js';
import { DRACOLoader } from 'three/examples/jsm/loaders/DRACOLoader.js';
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js';
import UVExtractor, { DesignAreaUV } from '@/lib/uv-extractor';
import { FinalTextureEngine } from '@/lib/final-texture-engine';
import { ThreeDInteractionManager, InteractionManagerConfig, InteractionCallbacks } from '@/lib/3d-interaction-manager';
import { DesignElement } from '@/lib/fabric-threejs-bridge';
import { ThreeDPerformanceOptimizer, PerformanceConfig } from '@/lib/3d-performance-optimizer';

interface Simple3DViewerProps {
  glbUrl: string;
  width?: number;
  height?: number;
  responsive?: boolean;
  showWireframe?: boolean;
  meshVisibility?: Record<string, boolean>;
  onMeshesLoaded?: (meshNames: string[]) => void;
  onDesignAreasExtracted?: (designAreas: DesignAreaUV[]) => void;
  onTextureEngineReady?: (textureEngine: FinalTextureEngine) => void;
  onMeshClick?: (meshName: string) => void;
  // New props for inline editing
  editingTextId?: string | null;
  designElements?: DesignElement[];
  onUpdateElement?: (id: string, updates: any) => void;
  onFinishTextEdit?: () => void;
  // New props for 3D interaction
  selectedElement?: string | null;
  onSelectElement?: (id: string | null) => void;
  onStartTextEdit?: (id: string) => void;
  // New prop for selected design area highlighting
  selectedDesignArea?: string;
  // New props for 3D interaction system
  enable3DInteraction?: boolean;
  interactionConfig?: Partial<InteractionManagerConfig>;
  onInteractionReady?: (manager: ThreeDInteractionManager) => void;
  onCanvasUpdate?: (canvas: HTMLCanvasElement) => void;
  // Performance optimization props
  enablePerformanceOptimization?: boolean;
  performanceConfig?: Partial<PerformanceConfig>;
}

export default function Simple3DViewer({
  glbUrl,
  width = 800,
  height = 800,
  responsive = false,
  showWireframe = true,
  meshVisibility = {},
  onMeshesLoaded,
  onDesignAreasExtracted,
  onTextureEngineReady,
  onMeshClick,
  editingTextId,
  designElements = [],
  onUpdateElement,
  onFinishTextEdit,
  selectedElement,
  onSelectElement,
  onStartTextEdit,
  selectedDesignArea,
  enable3DInteraction = true,
  interactionConfig = {},
  onInteractionReady,
  onCanvasUpdate,
  enablePerformanceOptimization = true,
  performanceConfig = {}
}: Simple3DViewerProps) {
  const mountRef = useRef<HTMLDivElement>(null);
  const [error, setError] = useState<string | null>(null);
  const [dimensions, setDimensions] = useState({ width, height });
  const textureEngineRef = useRef<FinalTextureEngine | null>(null);
  const interactionManagerRef = useRef<ThreeDInteractionManager | null>(null);
  const fabricCanvasRef = useRef<HTMLCanvasElement | null>(null);
  const performanceOptimizerRef = useRef<ThreeDPerformanceOptimizer | null>(null);

  // New state for 3D inline editing
  const sceneRef = useRef<THREE.Scene | null>(null);
  const cameraRef = useRef<THREE.OrthographicCamera | null>(null);
  const rendererRef = useRef<THREE.WebGLRenderer | null>(null);
  const [textEditorPosition, setTextEditorPosition] = useState<{x: number, y: number, width: number, height: number} | null>(null);

  // 3D interaction state
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState<{x: number, y: number} | null>(null);
  const raycasterRef = useRef<THREE.Raycaster>(new THREE.Raycaster());
  const mouseRef = useRef<THREE.Vector2>(new THREE.Vector2());
  const sceneObjectRef = useRef<THREE.Group | null>(null);

  // Function to project 3D world coordinates to 2D screen coordinates
  const project3DToScreen = (worldPosition: THREE.Vector3): {x: number, y: number} | null => {
    if (!cameraRef.current || !rendererRef.current || !mountRef.current) return null;

    const camera = cameraRef.current;
    const renderer = rendererRef.current;
    const canvas = renderer.domElement;
    const rect = canvas.getBoundingClientRect();

    // Project 3D point to normalized device coordinates (-1 to 1)
    const vector = worldPosition.clone();
    vector.project(camera);

    // Convert to screen coordinates
    const x = (vector.x * 0.5 + 0.5) * rect.width + rect.left;
    const y = (-vector.y * 0.5 + 0.5) * rect.height + rect.top;

    return { x, y };
  };

  // Update text editor position when editing text
  useEffect(() => {
    if (!editingTextId || !designElements.length) {
      setTextEditorPosition(null);
      return;
    }

    const editingElement = designElements.find(el => el.id === editingTextId);
    if (!editingElement || editingElement.type !== 'text') {
      setTextEditorPosition(null);
      return;
    }

    // For now, position at center of 3D viewer - we'll improve this to use actual mesh position
    const centerPosition = new THREE.Vector3(0, 0, 0);
    const screenPos = project3DToScreen(centerPosition);

    if (screenPos) {
      setTextEditorPosition({
        x: screenPos.x - 100, // Center the editor
        y: screenPos.y - 25,
        width: Math.max(200, editingElement.width || 200),
        height: Math.max(50, editingElement.height || 50)
      });
    }
  }, [editingTextId, designElements]);

  // Sync design elements with interaction manager
  useEffect(() => {
    if (!interactionManagerRef.current || !designElements.length) return;

    const manager = interactionManagerRef.current;

    // Clear existing elements
    manager.clear();

    // Add all design elements to the interaction manager
    designElements.forEach(element => {
      manager.addElement(element);
    });

    console.log(`🎮 Synced ${designElements.length} design elements with interaction manager`);
  }, [designElements]);

  // Sync selected element with interaction manager
  useEffect(() => {
    if (!interactionManagerRef.current) return;

    interactionManagerRef.current.selectElement(selectedElement || null);
  }, [selectedElement]);



  // Mouse event handlers for 3D interaction
  const handleMouseDown = useCallback((event: MouseEvent) => {
    if (!rendererRef.current || !cameraRef.current || !sceneRef.current) return;

    const canvas = rendererRef.current.domElement;
    const rect = canvas.getBoundingClientRect();

    // Calculate mouse position in normalized device coordinates (-1 to +1)
    mouseRef.current.x = ((event.clientX - rect.left) / rect.width) * 2 - 1;
    mouseRef.current.y = -((event.clientY - rect.top) / rect.height) * 2 + 1;

    // Set up raycaster
    raycasterRef.current.setFromCamera(mouseRef.current, cameraRef.current);

    // Check for intersections with design areas (meshes) - exclude masks
    const meshes: THREE.Mesh[] = [];
    const designAreaMeshes: THREE.Mesh[] = [];

    sceneRef.current.traverse((object) => {
      if (object instanceof THREE.Mesh) {
        // Skip mask meshes and highlight meshes entirely for clicking
        if (object.name.toLowerCase().includes('mask') ||
            object.name.toLowerCase().includes('highlight')) {
          return;
        }

        meshes.push(object);

        // Prioritize known design areas
        const designAreaNames = ['front', 'back', 'collar', 'sleeve', 'left_sleeve', 'right_sleeve'];
        if (designAreaNames.some(name => object.name.toLowerCase().includes(name))) {
          designAreaMeshes.push(object);
        }
      }
    });

    // First try to intersect with design area meshes only
    let intersects = raycasterRef.current.intersectObjects(designAreaMeshes);

    // If no design area hit, try all non-mask meshes
    if (intersects.length === 0) {
      intersects = raycasterRef.current.intersectObjects(meshes);
    }

    console.log('🎨 Available meshes for clicking:', meshes.map(m => m.name));
    console.log('🎨 Design area meshes:', designAreaMeshes.map(m => m.name));

    if (intersects.length > 0) {
      // Find the first intersected mesh with a valid name
      let validMesh = null;
      let validMeshName = '';

      for (const intersect of intersects) {
        const mesh = intersect.object as THREE.Mesh;
        if (mesh.name && mesh.name.trim() !== '') {
          validMesh = mesh;
          validMeshName = mesh.name;
          break;
        }
      }

      if (!validMesh || !validMeshName) {
        console.log('🎨 No valid named mesh found in intersects:', intersects.map(i => (i.object as THREE.Mesh).name));
        return;
      }

      console.log('🎨 Clicked on mesh:', validMeshName);
      console.log('🎨 Available design elements:', designElements.map(el => ({ id: el.id, type: el.type, meshName: el.data?.meshName })));

      // First, notify parent about mesh click for design area selection
      if (onMeshClick) {
        console.log('🎨 Notifying parent about mesh click:', validMeshName);
        onMeshClick(validMeshName);
      }

      // Then check if we clicked on a design area that has text elements
      // Try multiple matching strategies
      const elementsOnThisMesh = designElements.filter(el => {
        if (el.type !== 'text') return false;
        const elementMeshName = el.data?.meshName;
        if (!elementMeshName) return false;

        // Direct match
        if (elementMeshName === validMeshName) return true;

        // Try with/without underscores
        if (elementMeshName === validMeshName.replace('_', '')) return true;
        if (elementMeshName.replace('_', '') === validMeshName) return true;

        // Try case insensitive
        if (elementMeshName.toLowerCase() === validMeshName.toLowerCase()) return true;

        return false;
      });

      console.log('🎨 Elements on this mesh:', elementsOnThisMesh);

      if (elementsOnThisMesh.length > 0) {
        // Select the first text element on this mesh
        const elementToSelect = elementsOnThisMesh[0];
        console.log('🎨 Selecting element:', elementToSelect.id);
        if (onSelectElement) {
          onSelectElement(elementToSelect.id);
        }

        // Start dragging
        setIsDragging(true);
        setDragStart({ x: event.clientX, y: event.clientY });
      } else {
        // If no text elements on this mesh, deselect
        if (onSelectElement) {
          onSelectElement(null);
        }
      }
    } else {
      // Clicked on empty space, deselect
      if (onSelectElement) {
        onSelectElement(null);
      }
    }
  }, [designElements, onSelectElement, onMeshClick]);

  const handleMouseMove = useCallback((event: MouseEvent) => {
    if (!isDragging || !dragStart || !selectedElement || !onUpdateElement) return;

    const deltaX = event.clientX - dragStart.x;
    const deltaY = event.clientY - dragStart.y;

    // Update the selected element's position
    // For now, we'll use simple screen-space movement
    // In a more advanced implementation, we'd project this back to 3D surface coordinates
    const selectedEl = designElements.find(el => el.id === selectedElement);
    if (selectedEl) {
      onUpdateElement(selectedElement, {
        x: selectedEl.x + deltaX * 0.5, // Scale down the movement
        y: selectedEl.y + deltaY * 0.5
      });
    }

    setDragStart({ x: event.clientX, y: event.clientY });
  }, [isDragging, dragStart, selectedElement, onUpdateElement, designElements]);

  const handleMouseUp = useCallback(() => {
    setIsDragging(false);
    setDragStart(null);
  }, []);

  const handleDoubleClick = useCallback((event: MouseEvent) => {
    if (!rendererRef.current || !cameraRef.current || !sceneRef.current) return;

    const canvas = rendererRef.current.domElement;
    const rect = canvas.getBoundingClientRect();

    // Calculate mouse position in normalized device coordinates (-1 to +1)
    mouseRef.current.x = ((event.clientX - rect.left) / rect.width) * 2 - 1;
    mouseRef.current.y = -((event.clientY - rect.top) / rect.height) * 2 + 1;

    // Set up raycaster
    raycasterRef.current.setFromCamera(mouseRef.current, cameraRef.current);

    // Check for intersections with design areas (meshes) - exclude masks
    const meshes: THREE.Mesh[] = [];
    const designAreaMeshes: THREE.Mesh[] = [];

    sceneRef.current.traverse((object) => {
      if (object instanceof THREE.Mesh) {
        // Skip mask meshes and highlight meshes entirely for clicking
        if (object.name.toLowerCase().includes('mask') ||
            object.name.toLowerCase().includes('highlight')) {
          return;
        }

        meshes.push(object);

        // Prioritize known design areas
        const designAreaNames = ['front', 'back', 'collar', 'sleeve', 'left_sleeve', 'right_sleeve'];
        if (designAreaNames.some(name => object.name.toLowerCase().includes(name))) {
          designAreaMeshes.push(object);
        }
      }
    });

    // First try to intersect with design area meshes only
    let intersects = raycasterRef.current.intersectObjects(designAreaMeshes);

    // If no design area hit, try all non-mask meshes
    if (intersects.length === 0) {
      intersects = raycasterRef.current.intersectObjects(meshes);
    }

    if (intersects.length > 0) {
      // Find the first intersected mesh with a valid name
      let validMesh = null;
      let validMeshName = '';

      for (const intersect of intersects) {
        const mesh = intersect.object as THREE.Mesh;
        if (mesh.name && mesh.name.trim() !== '') {
          validMesh = mesh;
          validMeshName = mesh.name;
          break;
        }
      }

      if (!validMesh || !validMeshName) {
        console.log('🎨 No valid named mesh found in double-click intersects:', intersects.map(i => (i.object as THREE.Mesh).name));
        return;
      }

      console.log('🎨 Double-clicked on mesh:', validMeshName);
      console.log('🎨 Selected element:', selectedElement);

      // Check if we double-clicked on a design area that has text elements
      // Try multiple matching strategies
      const elementsOnThisMesh = designElements.filter(el => {
        if (el.type !== 'text') return false;
        const elementMeshName = el.data?.meshName;
        if (!elementMeshName) return false;

        // Direct match
        if (elementMeshName === validMeshName) return true;

        // Try with/without underscores
        if (elementMeshName === validMeshName.replace('_', '')) return true;
        if (elementMeshName.replace('_', '') === validMeshName) return true;

        // Try case insensitive
        if (elementMeshName.toLowerCase() === validMeshName.toLowerCase()) return true;

        return false;
      });

      console.log('🎨 Elements on this mesh for editing:', elementsOnThisMesh);

      if (elementsOnThisMesh.length > 0 && onStartTextEdit) {
        // Find the selected element or use the first one
        const elementToEdit = selectedElement ?
          elementsOnThisMesh.find(el => el.id === selectedElement) || elementsOnThisMesh[0] :
          elementsOnThisMesh[0];

        console.log('🎨 Starting text edit for element:', elementToEdit.id);
        onStartTextEdit(elementToEdit.id);
      }
    }
  }, [designElements, onStartTextEdit]);

  // Add mouse event listeners (only if 3D interaction is disabled)
  useEffect(() => {
    if (!rendererRef.current || enable3DInteraction) return;

    const canvas = rendererRef.current.domElement;
    canvas.addEventListener('mousedown', handleMouseDown);
    canvas.addEventListener('dblclick', handleDoubleClick);
    window.addEventListener('mousemove', handleMouseMove);
    window.addEventListener('mouseup', handleMouseUp);

    return () => {
      canvas.removeEventListener('mousedown', handleMouseDown);
      canvas.removeEventListener('dblclick', handleDoubleClick);
      window.removeEventListener('mousemove', handleMouseMove);
      window.removeEventListener('mouseup', handleMouseUp);
    };
  }, [handleMouseDown, handleDoubleClick, handleMouseMove, handleMouseUp, enable3DInteraction]);

  // Handle responsive sizing
  useEffect(() => {
    if (!responsive) return;

    const updateDimensions = () => {
      if (typeof window === 'undefined') return;

      const viewportWidth = window.innerWidth;
      const viewportHeight = window.innerHeight;

      // Calculate available space considering header, tools, and bottom controls
      const reservedHeight = 220; // Space for header, tools, and bottom controls
      const availableHeight = viewportHeight - reservedHeight;

      let newSize: number;

      if (viewportWidth >= 1920) {
        newSize = Math.min(700, viewportWidth - 400, availableHeight);
      } else if (viewportWidth >= 1440) {
        newSize = Math.min(650, viewportWidth - 300, availableHeight);
      } else if (viewportWidth >= 1024) {
        newSize = Math.min(550, viewportWidth - 200, availableHeight);
      } else if (viewportWidth >= 768) {
        newSize = Math.min(450, viewportWidth - 100, availableHeight);
      } else {
        newSize = Math.min(350, viewportWidth - 40, availableHeight);
      }

      // Ensure minimum size but respect viewport constraints
      newSize = Math.max(250, Math.min(newSize, availableHeight));

      setDimensions({ width: newSize, height: newSize });
    };

    updateDimensions();

    let resizeTimeout: NodeJS.Timeout;
    const handleResize = () => {
      clearTimeout(resizeTimeout);
      resizeTimeout = setTimeout(updateDimensions, 150);
    };

    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
      clearTimeout(resizeTimeout);
    };
  }, [responsive]);

  // Update mesh highlighting when selectedDesignArea changes
  useEffect(() => {
    if (!sceneObjectRef.current) return;

    console.log('🎨 Updating mesh highlighting for:', selectedDesignArea);

    // Update highlighting for all meshes
    sceneObjectRef.current.traverse((object) => {
      if (object instanceof THREE.Mesh) {
        const mesh = object as THREE.Mesh;

        // Remove existing highlight
        const existingHighlight = mesh.getObjectByName('highlight');
        if (existingHighlight) {
          mesh.remove(existingHighlight);
        }

        // Check if this mesh should be highlighted
        const shouldHighlight = selectedDesignArea && (
          mesh.name === selectedDesignArea ||
          mesh.name.toLowerCase().includes(selectedDesignArea.toLowerCase()) ||
          selectedDesignArea.toLowerCase().includes(mesh.name.toLowerCase())
        );

        if (shouldHighlight) {
          // Create glowing outline effect
          const highlightGeometry = new THREE.WireframeGeometry(mesh.geometry);
          const highlightMaterial = new THREE.LineBasicMaterial({
            color: 0x00ffff, // Cyan color for selection
            linewidth: 3,
            transparent: true,
            opacity: 0.8
          });
          const highlight = new THREE.LineSegments(highlightGeometry, highlightMaterial);
          highlight.name = 'highlight';

          // Make highlight non-interactive for raycasting
          highlight.raycast = () => {};

          // Add pulsing animation
          const animate = () => {
            if (highlight.parent) {
              highlightMaterial.opacity = 0.5 + 0.3 * Math.sin(Date.now() * 0.005);
              requestAnimationFrame(animate);
            }
          };
          animate();

          mesh.add(highlight);
          console.log('🎨 Added highlight to mesh:', mesh.name);
        }
      }
    });
  }, [selectedDesignArea]);

  useEffect(() => {
    if (!mountRef.current || !glbUrl) return;



    // Clear any existing content
    while (mountRef.current.firstChild) {
      mountRef.current.removeChild(mountRef.current.firstChild);
    }

    // Create scene
    const scene = new THREE.Scene();
    scene.background = new THREE.Color(0xf0f0f0);
    sceneRef.current = scene; // Store reference for projection

    // Create ORTHOGRAPHIC camera to match GLBViewer settings exactly
    const frustumSize = 4; // Same as GLBViewer
    const aspect = dimensions.width / dimensions.height;
    const camera = new THREE.OrthographicCamera(
      frustumSize * aspect / -2, // left
      frustumSize * aspect / 2,  // right
      frustumSize / 2,           // top
      frustumSize / -2,          // bottom
      0.1,                       // near
      1000                       // far
    );
    // Position camera for Front view (fixed to positive Z)
    camera.position.set(0, 0, 10); // Position on positive Z axis (Front view)
    camera.up.set(0, 1, 0); // Y-up orientation
    camera.lookAt(0, 0, 0); // Look toward origin
    cameraRef.current = camera; // Store reference for projection

    // Create renderer
    const renderer = new THREE.WebGLRenderer({ antialias: true });
    renderer.setSize(dimensions.width, dimensions.height);
    mountRef.current.appendChild(renderer.domElement);
    rendererRef.current = renderer; // Store reference for projection

    // Add controls - DISABLED for fixed stationary view (same as GLBViewer)
    const controls = new OrbitControls(camera, renderer.domElement);
    controls.enabled = false; // Disable all controls for fixed view

    // Add lights
    const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
    scene.add(ambientLight);

    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
    directionalLight.position.set(10, 10, 5);
    scene.add(directionalLight);

    // Initialize texture engine for design application
    textureEngineRef.current = new FinalTextureEngine(renderer);

    // Notify parent that texture engine is ready
    if (onTextureEngineReady && textureEngineRef.current) {
      onTextureEngineReady(textureEngineRef.current);
    }

    // Initialize 3D interaction system if enabled
    if (enable3DInteraction) {
      // Create hidden Fabric.js canvas for the interaction system
      const fabricCanvas = document.createElement('canvas');
      fabricCanvas.width = 512;
      fabricCanvas.height = 512;
      fabricCanvas.style.display = 'none';
      fabricCanvas.style.position = 'absolute';
      fabricCanvas.style.left = '-9999px';
      fabricCanvas.style.top = '-9999px';
      fabricCanvas.style.pointerEvents = 'none';
      fabricCanvas.style.zIndex = '-1';
      document.body.appendChild(fabricCanvas);
      fabricCanvasRef.current = fabricCanvas;

      // Initialize interaction manager
      interactionManagerRef.current = new ThreeDInteractionManager(
        scene,
        camera,
        renderer.domElement,
        fabricCanvas
      );

      // Fix Fabric.js container positioning to prevent UI blocking
      setTimeout(() => {
        const fabricContainer = fabricCanvas.parentElement;
        if (fabricContainer && fabricContainer.classList.contains('canvas-container')) {
          fabricContainer.style.position = 'absolute';
          fabricContainer.style.left = '-9999px';
          fabricContainer.style.top = '-9999px';
          fabricContainer.style.pointerEvents = 'none';
          fabricContainer.style.zIndex = '-1';
          fabricContainer.style.visibility = 'hidden';
          console.log('🎨 Fixed Fabric.js container positioning to prevent UI blocking');
        }
      }, 100);

      // Configure interaction manager
      if (interactionConfig) {
        interactionManagerRef.current.updateConfig(interactionConfig);
      }

      // Set up callbacks
      const callbacks: InteractionCallbacks = {
        onElementSelect: onSelectElement,
        onElementUpdate: onUpdateElement,
        onCanvasUpdate: onCanvasUpdate,
        onDesignAreaSelect: (areaName) => {
          if (onMeshClick) {
            onMeshClick(areaName);
          }
        }
      };
      interactionManagerRef.current.setCallbacks(callbacks);

      // Notify parent that interaction manager is ready
      if (onInteractionReady) {
        onInteractionReady(interactionManagerRef.current);
      }

      console.log('🎮 3D Interaction system initialized');
    }

    // Initialize performance optimizer if enabled
    if (enablePerformanceOptimization) {
      performanceOptimizerRef.current = new ThreeDPerformanceOptimizer(
        renderer,
        scene,
        camera
      );

      // Configure performance optimizer
      if (performanceConfig) {
        performanceOptimizerRef.current.updateConfig(performanceConfig);
      }

      console.log('⚡ Performance optimization system initialized');
    }



    // Animation loop - no controls update needed for fixed view
    const animate = () => {
      requestAnimationFrame(animate);
      renderer.render(scene, camera);
    };
    animate();

    // Now try to load the GLB
    const loader = new GLTFLoader();
    const dracoLoader = new DRACOLoader();
    dracoLoader.setDecoderPath('/draco/');
    loader.setDRACOLoader(dracoLoader);

    loader.load(
      glbUrl,
      (gltf) => {


        // Add the model to scene
        scene.add(gltf.scene);

        // Store reference for highlighting updates
        sceneObjectRef.current = gltf.scene;

        // Extract design areas from the loaded model
        const extractedDesignAreas: DesignAreaUV[] = [];
        gltf.scene.traverse((object) => {
          if (object instanceof THREE.Mesh) {
            const mesh = object as THREE.Mesh;
            const geometry = mesh.geometry;

            // Check if this is a design area (same logic as UVExtractor)
            const isDesignArea = (name: string): boolean => {
              const designAreaPatterns = [
                'front', 'back', 'left', 'right', 'sleeve', 'collar', 'chest', 'pocket'
              ];
              const lowerName = name.toLowerCase();
              return designAreaPatterns.some(pattern => lowerName.includes(pattern));
            };

            if (geometry.attributes.uv && isDesignArea(mesh.name)) {
              const uvMapping = UVExtractor.extractUVMapping(mesh);
              if (uvMapping) {
                extractedDesignAreas.push({
                  areaName: mesh.name,
                  mapping: uvMapping,
                  mesh,
                  material: Array.isArray(mesh.material) ? mesh.material[0] : mesh.material,
                  isDesignArea: true
                });
              }
            }
          }
        });

        // Notify parent about extracted design areas
        if (onDesignAreasExtracted && extractedDesignAreas.length > 0) {
          onDesignAreasExtracted(extractedDesignAreas);
        }

        // Register design areas with interaction manager
        if (interactionManagerRef.current && extractedDesignAreas.length > 0) {
          extractedDesignAreas.forEach(designArea => {
            interactionManagerRef.current!.registerDesignArea(designArea);
          });
          console.log(`🎮 Registered ${extractedDesignAreas.length} design areas with interaction manager`);
        }



        // Process meshes
        const meshNames: string[] = [];
        gltf.scene.traverse((object) => {
          if (object instanceof THREE.Mesh) {
            const mesh = object as THREE.Mesh;

            // Collect mesh names for debug panel
            if (mesh.name && !mesh.name.toLowerCase().includes('mask')) {
              meshNames.push(mesh.name);
            }

            // Hide mask pieces
            if (mesh.name.toLowerCase().includes('mask')) {
              mesh.visible = false;
            } else {
              // Apply mesh visibility from debug panel
              const isVisible = meshVisibility[mesh.name] !== false; // Default to visible
              mesh.visible = isVisible;

              // Add click handler for mesh selection
              if (onMeshClick) {
                mesh.userData.clickHandler = () => onMeshClick(mesh.name);
              }

              // Add green wireframe to visible meshes only if showWireframe is true
              if (showWireframe && isVisible) {
                const wireframeGeometry = new THREE.WireframeGeometry(mesh.geometry);
                const wireframeMaterial = new THREE.LineBasicMaterial({ color: 0x00ff00 });
                const wireframe = new THREE.LineSegments(wireframeGeometry, wireframeMaterial);
                mesh.add(wireframe);
              }


            }
          }
        });

        // Report available meshes to parent component
        if (onMeshesLoaded && meshNames.length > 0) {
          onMeshesLoaded(meshNames);
        }

        // Center and scale model (same as GLBViewer)
        const box = new THREE.Box3().setFromObject(gltf.scene);
        const center = box.getCenter(new THREE.Vector3());
        const size = box.getSize(new THREE.Vector3());
        const maxDim = Math.max(size.x, size.y, size.z);
        const scale = 4 / maxDim; // Same scale factor as GLBViewer

        gltf.scene.scale.setScalar(scale);
        gltf.scene.position.copy(center).multiplyScalar(-scale);


        setError(null);
      },
      () => {
        // Progress tracking removed - no longer needed
      },
      (error) => {
        setError(error instanceof Error ? error.message : 'Failed to load 3D model');

      }
    );

    // Cleanup
    return () => {
      // Dispose interaction manager
      if (interactionManagerRef.current) {
        interactionManagerRef.current.dispose();
        interactionManagerRef.current = null;
      }

      // Dispose performance optimizer
      if (performanceOptimizerRef.current) {
        performanceOptimizerRef.current.dispose();
        performanceOptimizerRef.current = null;
      }

      // Remove fabric canvas
      if (fabricCanvasRef.current && document.body.contains(fabricCanvasRef.current)) {
        document.body.removeChild(fabricCanvasRef.current);
        fabricCanvasRef.current = null;
      }

      // Dispose Three.js resources
      if (mountRef.current && renderer.domElement && mountRef.current.contains(renderer.domElement)) {
        mountRef.current.removeChild(renderer.domElement);
      }
      renderer.dispose();
    };
  }, [glbUrl, dimensions.width, dimensions.height, showWireframe, meshVisibility]);

  return (
    <div style={{ width: dimensions.width, height: dimensions.height }} className="relative">
      <div
        ref={mountRef}
        style={{ width: dimensions.width, height: dimensions.height }}
        className="rounded-lg overflow-hidden shadow-sm"
      />



      {/* Inline Text Editor - positioned over 3D model */}
      {editingTextId && textEditorPosition && onUpdateElement && onFinishTextEdit && (
        <textarea
          autoFocus
          value={designElements.find(el => el.id === editingTextId)?.data?.text || ''}
          onChange={(e) => {
            const element = designElements.find(el => el.id === editingTextId);
            if (element) {
              onUpdateElement(editingTextId, {
                data: { ...element.data, text: e.target.value }
              });
            }
          }}
          onKeyDown={(e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
              e.preventDefault();
              onFinishTextEdit();
            } else if (e.key === 'Escape') {
              e.preventDefault();
              onFinishTextEdit();
            }
          }}
          onBlur={onFinishTextEdit}
          style={{
            position: 'fixed',
            left: textEditorPosition.x,
            top: textEditorPosition.y,
            width: textEditorPosition.width,
            height: textEditorPosition.height,
            fontSize: designElements.find(el => el.id === editingTextId)?.data?.fontSize || 24,
            fontFamily: designElements.find(el => el.id === editingTextId)?.data?.fontFamily || 'Arial',
            color: designElements.find(el => el.id === editingTextId)?.data?.color || '#000000',
            backgroundColor: 'rgba(255, 255, 255, 0.95)',
            border: '2px solid #4285f4',
            borderRadius: '4px',
            padding: '8px',
            resize: 'none',
            outline: 'none',
            zIndex: 1000,
            overflow: 'hidden',
            textAlign: 'center',
            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)'
          }}
          placeholder="Enter text..."
        />
      )}

      {error && (
        <div className="absolute inset-0 flex items-center justify-center bg-red-50 bg-opacity-90 rounded-lg">
          <div className="text-center text-red-600">
            <p className="font-medium">Failed to load 3D model</p>
            <p className="text-sm mt-1">{error}</p>
          </div>
        </div>
      )}
    </div>
  );
}
