'use client'

import { useState, useCallback, useEffect } from 'react'

export type ViewType = '3d' | 'printfile'

export interface CanvasViewState {
  mainCanvasView: ViewType
  miniCanvasView: ViewType
  selectedGLBIndex: number
  selectedPrintFileIndex: number
  isSwapping: boolean
}

export interface CanvasViewManagerProps {
  initialMainView?: ViewType
  initialGLBIndex?: number
  initialPrintFileIndex?: number
  onViewChange?: (state: CanvasViewState) => void
  onSwapComplete?: () => void
}

export interface CanvasViewManagerReturn {
  // Current state
  state: CanvasViewState
  
  // Actions
  setMainView: (view: ViewType) => void
  setMiniView: (view: ViewType) => void
  swapViews: () => void
  setSelectedGLB: (index: number) => void
  setSelectedPrintFile: (index: number) => void
  
  // Computed values
  isMainCanvas3D: boolean
  isMiniCanvas3D: boolean
  shouldShow3DInMain: boolean
  shouldShowPrintFileInMain: boolean
}

export function useCanvasViewManager({
  initialMainView = '3d',
  initialGLBIndex = 0,
  initialPrintFileIndex = 0,
  onViewChange,
  onSwapComplete
}: CanvasViewManagerProps = {}): CanvasViewManagerReturn {
  
  const [state, setState] = useState<CanvasViewState>({
    mainCanvasView: initialMainView,
    miniCanvasView: initialMainView === '3d' ? 'printfile' : '3d',
    selectedGLBIndex: initialGLBIndex,
    selectedPrintFileIndex: initialPrintFileIndex,
    isSwapping: false
  })

  // Notify parent of state changes
  useEffect(() => {
    if (onViewChange) {
      onViewChange(state)
    }
  }, [state, onViewChange])

  const setMainView = useCallback((view: ViewType) => {
    setState(prev => ({
      ...prev,
      mainCanvasView: view,
      miniCanvasView: view === '3d' ? 'printfile' : '3d'
    }))
  }, [])

  const setMiniView = useCallback((view: ViewType) => {
    setState(prev => ({
      ...prev,
      miniCanvasView: view,
      mainCanvasView: view === '3d' ? 'printfile' : '3d'
    }))
  }, [])

  const swapViews = useCallback(async () => {
    setState(prev => ({ ...prev, isSwapping: true }))
    
    // Small delay for animation
    await new Promise(resolve => setTimeout(resolve, 150))
    
    setState(prev => ({
      ...prev,
      mainCanvasView: prev.miniCanvasView,
      miniCanvasView: prev.mainCanvasView,
      isSwapping: false
    }))

    // Notify completion
    if (onSwapComplete) {
      setTimeout(onSwapComplete, 100)
    }
  }, [onSwapComplete])

  const setSelectedGLB = useCallback((index: number) => {
    setState(prev => ({
      ...prev,
      selectedGLBIndex: index
    }))
  }, [])

  const setSelectedPrintFile = useCallback((index: number) => {
    setState(prev => ({
      ...prev,
      selectedPrintFileIndex: index
    }))
  }, [])

  // Computed values
  const isMainCanvas3D = state.mainCanvasView === '3d'
  const isMiniCanvas3D = state.miniCanvasView === '3d'
  const shouldShow3DInMain = state.mainCanvasView === '3d'
  const shouldShowPrintFileInMain = state.mainCanvasView === 'printfile'

  return {
    state,
    setMainView,
    setMiniView,
    swapViews,
    setSelectedGLB,
    setSelectedPrintFile,
    isMainCanvas3D,
    isMiniCanvas3D,
    shouldShow3DInMain,
    shouldShowPrintFileInMain
  }
}

// Hook for managing view transitions and animations
export function useViewTransitions() {
  const [isTransitioning, setIsTransitioning] = useState(false)
  const [transitionType, setTransitionType] = useState<'swap' | 'change' | null>(null)

  const startTransition = useCallback((type: 'swap' | 'change') => {
    setIsTransitioning(true)
    setTransitionType(type)
  }, [])

  const endTransition = useCallback(() => {
    setIsTransitioning(false)
    setTransitionType(null)
  }, [])

  return {
    isTransitioning,
    transitionType,
    startTransition,
    endTransition
  }
}

// Utility function to get opposite view
export function getOppositeView(view: ViewType): ViewType {
  return view === '3d' ? 'printfile' : '3d'
}

// Utility function to validate view state
export function validateViewState(state: CanvasViewState): boolean {
  // Main and mini views should always be opposite
  return state.mainCanvasView !== state.miniCanvasView
}

export default useCanvasViewManager
