'use client'

import React, { useState } from 'react'
import { XMarkIcon, EyeIcon, EyeSlashIcon } from '@heroicons/react/24/outline'

interface DesignAreaUV {
  areaName: string
  mesh: any
  mapping: any
}

interface PrintFile {
  url: string
  filename: string
}

interface DebugMappingScreenProps {
  isVisible: boolean
  onClose: () => void
  designAreas: DesignAreaUV[]
  printFiles: PrintFile[]
  getFileUrl: (bucket: string, path: string) => string
  selectedDesignArea?: DesignAreaUV
}

export default function DebugMappingScreen({
  isVisible,
  onClose,
  designAreas,
  printFiles,
  getFileUrl,
  selectedDesignArea
}: DebugMappingScreenProps) {
  const [showDetails, setShowDetails] = useState(false)

  if (!isVisible) return null

  // Create mapping between mesh names and print files
  const createMapping = () => {
    const mappings: Array<{
      meshName: string
      printFile: PrintFile | null
      confidence: 'exact' | 'partial' | 'none'
      reason: string
      debugInfo?: string
    }> = []

    designAreas.forEach(area => {
      const meshName = area.areaName.toLowerCase()

      // Try to find matching print file
      let matchedFile: PrintFile | null = null
      let confidence: 'exact' | 'partial' | 'none' = 'none'
      let reason = 'No matching print file found'
      let debugInfo = ''

      // Debug: Log what we're working with
      const fileAnalysis = printFiles.map(file => {
        // Extract just the filename from the full path
        const fullPath = file.url || file.filename || ''
        const pathParts = fullPath.split('/')
        const actualFileName = pathParts[pathParts.length - 1] || ''
        const fileNameWithoutExt = actualFileName.toLowerCase().replace(/\.(png|jpg|jpeg)$/i, '')

        return {
          originalUrl: file.url,
          originalFilename: file.filename,
          extractedFileName: actualFileName,
          fileNameWithoutExt,
          meshName
        }
      })

      debugInfo = JSON.stringify(fileAnalysis, null, 2)

      // Try multiple matching strategies
      for (const file of printFiles) {
        // Strategy 1: Check the filename property
        if (file.filename) {
          const fileName = file.filename.toLowerCase().replace(/\.(png|jpg|jpeg)$/i, '')
          if (fileName === meshName) {
            matchedFile = file
            confidence = 'exact'
            reason = 'Exact filename match'
            break
          }
        }

        // Strategy 2: Check the URL path for filename
        if (file.url) {
          const urlParts = file.url.split('/')
          const urlFileName = urlParts[urlParts.length - 1] || ''
          const urlFileNameWithoutExt = urlFileName.toLowerCase().replace(/\.(png|jpg|jpeg)$/i, '')

          if (urlFileNameWithoutExt === meshName) {
            matchedFile = file
            confidence = 'exact'
            reason = 'Exact URL filename match'
            break
          }
        }

        // Strategy 3: Partial matching with both filename and URL
        const searchTargets = [
          file.filename?.toLowerCase() || '',
          file.url?.toLowerCase() || ''
        ]

        for (const target of searchTargets) {
          if (target.includes(meshName) || meshName.includes(target.replace(/\.(png|jpg|jpeg)$/i, ''))) {
            if (!matchedFile) { // Only set if we haven't found an exact match
              matchedFile = file
              confidence = 'partial'
              reason = `Partial match in ${target.includes(meshName) ? 'path' : 'name'}`
            }
          }
        }
      }

      mappings.push({
        meshName: area.areaName,
        printFile: matchedFile,
        confidence,
        reason,
        debugInfo: showDetails ? debugInfo : undefined
      })
    })

    return mappings
  }

  const mappings = createMapping()

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div>
            <h2 className="text-xl font-semibold text-gray-900">Debug: Mesh to Print File Mapping</h2>
            <p className="text-sm text-gray-600 mt-1">
              Visualizing how 3D meshes map to print files
            </p>
          </div>
          <div className="flex items-center space-x-2">
            <button
              onClick={() => setShowDetails(!showDetails)}
              className="flex items-center space-x-1 px-3 py-1 text-sm bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"
            >
              {showDetails ? <EyeSlashIcon className="w-4 h-4" /> : <EyeIcon className="w-4 h-4" />}
              <span>{showDetails ? 'Hide' : 'Show'} Details</span>
            </button>
            <button
              onClick={onClose}
              className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
            >
              <XMarkIcon className="w-5 h-5" />
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
          {/* Summary Stats */}
          <div className="grid grid-cols-3 gap-4 mb-6">
            <div className="bg-blue-50 p-4 rounded-lg">
              <div className="text-2xl font-bold text-blue-600">{designAreas.length}</div>
              <div className="text-sm text-blue-800">Design Areas (Meshes)</div>
            </div>
            <div className="bg-green-50 p-4 rounded-lg">
              <div className="text-2xl font-bold text-green-600">{printFiles.length}</div>
              <div className="text-sm text-green-800">Print Files</div>
            </div>
            <div className="bg-purple-50 p-4 rounded-lg">
              <div className="text-2xl font-bold text-purple-600">
                {mappings.filter(m => m.confidence !== 'none').length}
              </div>
              <div className="text-sm text-purple-800">Successful Mappings</div>
            </div>
          </div>

          {/* Debug Information */}
          {showDetails && (
            <div className="bg-gray-50 border border-gray-200 rounded-lg p-4 mb-6">
              <h3 className="font-medium text-gray-800 mb-2">Debug Information</h3>
              <div className="text-sm text-gray-600 space-y-1">
                <div><strong>Print Files Raw Data:</strong></div>
                <pre className="bg-white p-2 rounded text-xs overflow-x-auto">
                  {JSON.stringify(printFiles, null, 2)}
                </pre>
                <div><strong>Design Areas:</strong></div>
                <pre className="bg-white p-2 rounded text-xs overflow-x-auto">
                  {JSON.stringify(designAreas.map(area => ({
                    areaName: area.areaName,
                    hasMapping: !!area.mapping,
                    hasMesh: !!area.mesh
                  })), null, 2)}
                </pre>
              </div>
            </div>
          )}

          {/* Current Selection */}
          {selectedDesignArea && (
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
              <h3 className="font-medium text-yellow-800 mb-2">Currently Selected Design Area</h3>
              <div className="text-sm text-yellow-700">
                <strong>Mesh Name:</strong> {selectedDesignArea.areaName}
              </div>
              {showDetails && (
                <div className="mt-2 text-xs text-yellow-600">
                  <div><strong>UV Bounds:</strong> {JSON.stringify(selectedDesignArea.mapping?.boundingBox)}</div>
                  <div><strong>Texture Size:</strong> {selectedDesignArea.mapping?.textureSize?.width}x{selectedDesignArea.mapping?.textureSize?.height}</div>
                </div>
              )}
            </div>
          )}

          {/* Mapping Table */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-gray-900">Mesh to Print File Mappings</h3>
            
            {mappings.map((mapping, index) => (
              <div
                key={index}
                className={`border rounded-lg p-4 ${
                  mapping.confidence === 'exact' ? 'border-green-200 bg-green-50' :
                  mapping.confidence === 'partial' ? 'border-yellow-200 bg-yellow-50' :
                  'border-red-200 bg-red-50'
                }`}
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3">
                      <div className="font-medium text-gray-900">
                        {mapping.meshName}
                      </div>
                      <div className="text-2xl text-gray-400">→</div>
                      <div className={`font-medium ${
                        mapping.printFile ? 'text-gray-900' : 'text-gray-400'
                      }`}>
                        {mapping.printFile ? mapping.printFile.filename : 'No match found'}
                      </div>
                    </div>
                    
                    <div className="mt-2 flex items-center space-x-4">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        mapping.confidence === 'exact' ? 'bg-green-100 text-green-800' :
                        mapping.confidence === 'partial' ? 'bg-yellow-100 text-yellow-800' :
                        'bg-red-100 text-red-800'
                      }`}>
                        {mapping.confidence === 'exact' ? '✓ Exact Match' :
                         mapping.confidence === 'partial' ? '~ Partial Match' :
                         '✗ No Match'}
                      </span>
                      <span className="text-sm text-gray-600">{mapping.reason}</span>
                    </div>

                    {showDetails && (
                      <div className="mt-3 text-xs text-gray-500">
                        {mapping.printFile && (
                          <>
                            <div><strong>Print File URL:</strong> {mapping.printFile.url}</div>
                            <div><strong>Print File Filename:</strong> {mapping.printFile.filename}</div>
                            <div><strong>Full Path:</strong> {getFileUrl('product-files', mapping.printFile.url)}</div>
                          </>
                        )}
                        {mapping.debugInfo && (
                          <details className="mt-2">
                            <summary className="cursor-pointer text-blue-600">Show File Analysis</summary>
                            <pre className="mt-1 bg-gray-100 p-2 rounded text-xs overflow-x-auto">
                              {mapping.debugInfo}
                            </pre>
                          </details>
                        )}
                      </div>
                    )}
                  </div>

                  {/* Preview Images */}
                  {mapping.printFile && (
                    <div className="ml-4 flex-shrink-0">
                      <img
                        src={getFileUrl('product-files', mapping.printFile.url)}
                        alt={mapping.printFile.filename}
                        className="w-16 h-16 object-cover rounded border"
                        onError={(e) => {
                          const target = e.target as HTMLImageElement
                          target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjY0IiBoZWlnaHQ9IjY0IiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0yMCAyMEg0NFY0NEgyMFYyMFoiIHN0cm9rZT0iIzlDQTNBRiIgc3Ryb2tlLXdpZHRoPSIyIiBmaWxsPSJub25lIi8+CjxwYXRoIGQ9Ik0yOCAyOEwzNiAzNkwyOCA0NCIgc3Ryb2tlPSIjOUNBM0FGIiBzdHJva2Utd2lkdGg9IjIiIGZpbGw9Im5vbmUiLz4KPC9zdmc+'
                        }}
                      />
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>

          {/* Recommendations */}
          <div className="mt-6 bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h3 className="font-medium text-blue-800 mb-2">Recommendations</h3>
            <ul className="text-sm text-blue-700 space-y-1">
              <li>• Ensure print file names match mesh names (e.g., "front.png" for "front" mesh)</li>
              <li>• Use consistent naming: underscores or hyphens, but not mixed</li>
              <li>• Center point of print file should align with center point of mesh</li>
              <li>• All positioning should be relative to these center points</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  )
}
