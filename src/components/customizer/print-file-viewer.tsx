'use client'

import React, { useState, useRef, useCallback, useEffect } from 'react'
import { DesignElement } from './unified-customizer-interface'

interface Product {
  id: string
  name: string
  description?: string | null
  print_files?: any
  mockup_files?: any
}

interface PrintFileViewerProps {
  product: Product
  designElements: DesignElement[]
  getFileUrl: (bucket: string, path: string) => string
  viewMode: 'mockup' | 'printfile'
  onViewModeSwitch: () => void
}

export default function PrintFileViewer({
  product,
  designElements,
  getFileUrl,
  viewMode,
  onViewModeSwitch
}: PrintFileViewerProps) {
  // Set default position to center-right side, below header
  const [isMounted, setIsMounted] = useState(false)
  const [position, setPosition] = useState({
    x: 800, // Default fallback position
    y: 180 // Below the header (header is ~80px + margin)
  })
  const [isDragging, setIsDragging] = useState(false)
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 })
  const [isMinimized, setIsMinimized] = useState(false)
  const viewerRef = useRef<HTMLDivElement>(null)
  const dragOffsetRef = useRef({ x: 0, y: 0 })

  // Set proper initial position after component mounts
  useEffect(() => {
    setIsMounted(true)

    const updatePosition = () => {
      if (typeof window !== 'undefined') {
        const isMobile = window.innerWidth <= 768
        // Even smaller dimensions for mobile
        const mobileWidth = isMinimized ? 120 : 160 // Further reduced for mobile
        const mobileHeight = isMinimized ? 28 : 220 // Increased height to fit switch button
        const desktopWidth = isMinimized ? 200 : 280
        const desktopHeight = isMinimized ? 40 : 320
        const previewWidth = isMobile ? mobileWidth : desktopWidth
        const previewHeight = isMobile ? mobileHeight : desktopHeight

        setPosition({
          x: isMobile ? window.innerWidth - (mobileWidth + 27) : window.innerWidth - 300, // Even closer to right edge on mobile
          y: isMobile ? (window.innerHeight * 0.83) - (previewHeight / 2) : 180 // Lower position on mobile (65% down)
        })
      }
    }

    updatePosition()

    // Update position on resize
    if (typeof window !== 'undefined') {
      window.addEventListener('resize', updatePosition)
      return () => window.removeEventListener('resize', updatePosition)
    }
  }, [])

  // Handle drag start
  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    if (e.target === e.currentTarget || (e.target as HTMLElement).classList.contains('drag-handle')) {
      e.preventDefault() // Prevent text selection
      e.stopPropagation() // Stop event bubbling

      // Calculate the exact offset from mouse to element's top-left corner
      const offset = {
        x: e.clientX - position.x,
        y: e.clientY - position.y
      }

      dragOffsetRef.current = offset
      setDragStart(offset)
      setIsDragging(true)

      // Prevent text selection during drag
      document.body.style.userSelect = 'none'
      document.body.style.webkitUserSelect = 'none'
      document.body.classList.add('dragging')
    }
  }, [position])

  // Handle touch start for mobile
  const handleTouchStart = useCallback((e: React.TouchEvent) => {
    if (e.target === e.currentTarget || (e.target as HTMLElement).classList.contains('drag-handle')) {
      e.preventDefault() // Prevent scrolling
      e.stopPropagation() // Stop event bubbling

      const touch = e.touches[0]
      // Calculate the exact offset from touch to element's top-left corner
      const offset = {
        x: touch.clientX - position.x,
        y: touch.clientY - position.y
      }

      dragOffsetRef.current = offset
      setDragStart(offset)
      setIsDragging(true)

      // Prevent text selection during drag
      document.body.style.userSelect = 'none'
      document.body.style.webkitUserSelect = 'none'
      document.body.classList.add('dragging')
    }
  }, [position])

  // Handle drag
  const handleMouseMove = useCallback((e: MouseEvent) => {
    if (!isDragging) return

    e.preventDefault() // Prevent any default behavior

    // Use the ref for most up-to-date offset
    const offset = dragOffsetRef.current
    const newX = e.clientX - offset.x
    const newY = e.clientY - offset.y

    // Define constraints with responsive sizing
    const headerHeight = 80 // Header height
    const minY = headerHeight + 20 // Minimum Y position (below header + margin)
    const isMobile = window.innerWidth <= 768
    const previewWidth = isMobile ? (isMinimized ? 120 : 160) : (isMinimized ? 200 : 280)
    const previewHeight = isMobile ? (isMinimized ? 28 : 180) : (isMinimized ? 40 : 320)
    const maxX = window.innerWidth - previewWidth // Maximum X (width of mini preview)
    const maxY = window.innerHeight - previewHeight // Maximum Y (height of mini preview)

    // Direct position update for instant response
    setPosition({
      x: Math.max(20, Math.min(newX, maxX)), // Left margin of 20px, right constrained
      y: Math.max(minY, Math.min(newY, maxY)) // Below header, above bottom
    })
  }, [isDragging, isMinimized])

  // Handle touch move for mobile
  const handleTouchMove = useCallback((e: TouchEvent) => {
    if (!isDragging) return

    e.preventDefault() // Prevent scrolling

    const touch = e.touches[0]
    // Use the ref for most up-to-date offset
    const offset = dragOffsetRef.current
    const newX = touch.clientX - offset.x
    const newY = touch.clientY - offset.y

    // Define constraints with responsive sizing
    const headerHeight = 80 // Header height
    const minY = headerHeight + 20 // Minimum Y position (below header + margin)
    const isMobile = window.innerWidth <= 768
    const previewWidth = isMobile ? (isMinimized ? 120 : 160) : (isMinimized ? 200 : 280)
    const previewHeight = isMobile ? (isMinimized ? 28 : 180) : (isMinimized ? 40 : 320)
    const maxX = window.innerWidth - previewWidth // Maximum X (width of mini preview)
    const maxY = window.innerHeight - previewHeight // Maximum Y (height of mini preview)

    // Direct position update for instant response
    setPosition({
      x: Math.max(20, Math.min(newX, maxX)), // Left margin of 20px, right constrained
      y: Math.max(minY, Math.min(newY, maxY)) // Below header, above bottom
    })
  }, [isDragging, isMinimized])

  // Handle drag end
  const handleMouseUp = useCallback(() => {
    setIsDragging(false)

    // Restore text selection
    document.body.style.userSelect = ''
    document.body.style.webkitUserSelect = ''
    document.body.classList.remove('dragging')
  }, [])

  const handleTouchEnd = useCallback(() => {
    setIsDragging(false)

    // Restore text selection
    document.body.style.userSelect = ''
    document.body.style.webkitUserSelect = ''
    document.body.classList.remove('dragging')
  }, [])

  // Add global event listeners for dragging
  useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove)
      document.addEventListener('mouseup', handleMouseUp)
      document.addEventListener('touchmove', handleTouchMove, { passive: false })
      document.addEventListener('touchend', handleTouchEnd)
      return () => {
        document.removeEventListener('mousemove', handleMouseMove)
        document.removeEventListener('mouseup', handleMouseUp)
        document.removeEventListener('touchmove', handleTouchMove)
        document.removeEventListener('touchend', handleTouchEnd)
      }
    }
  }, [isDragging, handleMouseMove, handleMouseUp, handleTouchMove, handleTouchEnd])

  // Handle window resize to keep mini preview in bounds
  useEffect(() => {
    const handleResize = () => {
      if (typeof window !== 'undefined') {
        const headerHeight = 80
        const minY = headerHeight + 20
        const isMobile = window.innerWidth <= 768
        const previewWidth = isMobile ? (isMinimized ? 120 : 160) : (isMinimized ? 200 : 280)
        const previewHeight = isMobile ? (isMinimized ? 28 : 180) : (isMinimized ? 40 : 320)
        const maxX = window.innerWidth - previewWidth
        const maxY = window.innerHeight - previewHeight

        // On mobile, reset to default centered position; on desktop, keep within bounds
        if (isMobile) {
          setPosition({
            x: window.innerWidth - (previewWidth + 2), // Even closer to right edge
            y: (window.innerHeight * 0.65) - (previewHeight / 2) // Lower position (65% down)
          })
        } else {
          setPosition(prev => ({
            x: Math.max(20, Math.min(prev.x, maxX)),
            y: Math.max(minY, Math.min(prev.y, maxY))
          }))
        }
      }
    }

    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [isMinimized])

  // Get first print file
  const printFiles = Array.isArray(product.print_files) ? product.print_files : []
  const printFile = printFiles[0]
  if (!printFile || !isMounted) return null

  const printFileUrl = getFileUrl('product-files', printFile.url)

  // Responsive sizing for mobile vs desktop
  const isMobile = typeof window !== 'undefined' && window.innerWidth <= 768
  const mobileWidth = isMinimized ? 120 : 160  // Even smaller for mobile
  const mobileHeight = isMinimized ? 28 : 220  // Increased height to fit switch button
  const desktopWidth = isMinimized ? 200 : 280
  const desktopHeight = isMinimized ? 40 : 320

  const previewWidth = isMobile ? mobileWidth : desktopWidth
  const previewHeight = isMobile ? mobileHeight : desktopHeight

  return (
    <div
      ref={viewerRef}
      className={`fixed z-40 backdrop-blur-3xl border border-white/60 rounded-2xl shadow-2xl overflow-hidden ${isDragging ? '' : 'transition-all duration-300'}`}
      style={{
        left: position.x,
        top: position.y,
        width: `${previewWidth}px`,
        height: `${previewHeight}px`,
        background: 'linear-gradient(135deg, rgba(255, 255, 255, 0.4), rgba(255, 255, 255, 0.2))',
        backdropFilter: 'blur(50px) saturate(180%)',
        boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(255, 255, 255, 0.6) inset, 0 0 0 2px rgba(255, 255, 255, 0.2) inset',
        userSelect: isDragging ? 'none' : 'auto',
        WebkitUserSelect: isDragging ? 'none' : 'auto',
        transform: isDragging ? 'none' : undefined, // Disable any transforms during drag
        transition: isDragging ? 'none' : undefined // Disable transitions during drag
      }}
    >
      {/* Header */}
      <div
        className="drag-handle flex items-center justify-between border-b border-white/40 cursor-move backdrop-blur-xl select-none p-1.5 md:p-3"
        onMouseDown={handleMouseDown}
        onTouchStart={handleTouchStart}
        style={{
          background: 'linear-gradient(90deg, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.15))',
          backdropFilter: 'blur(20px) saturate(180%)',
          userSelect: 'none',
          WebkitUserSelect: 'none'
        }}
      >
        <div className={`flex items-center drag-handle ${window.innerWidth <= 768 ? 'space-x-1' : 'space-x-2'}`}>
          <div className={`flex items-center drag-handle ${window.innerWidth <= 768 ? 'space-x-0.5' : 'space-x-1'}`}>
            <div className={`bg-gray-400 rounded-full drag-handle ${window.innerWidth <= 768 ? 'w-0.5 h-0.5' : 'w-1 h-1'}`}></div>
            <div className={`bg-gray-400 rounded-full drag-handle ${window.innerWidth <= 768 ? 'w-0.5 h-0.5' : 'w-1 h-1'}`}></div>
            <div className={`bg-gray-400 rounded-full drag-handle ${window.innerWidth <= 768 ? 'w-0.5 h-0.5' : 'w-1 h-1'}`}></div>
            <div className={`bg-gray-400 rounded-full drag-handle ${window.innerWidth <= 768 ? 'w-0.5 h-0.5' : 'w-1 h-1'}`}></div>
          </div>
          <div className={`bg-blue-500 rounded-full drag-handle ${window.innerWidth <= 768 ? 'w-1.5 h-1.5' : 'w-2 h-2'}`}></div>
          <span className={`font-medium text-gray-700 drag-handle ${window.innerWidth <= 768 ? 'text-xs' : 'text-sm'}`}>
            {viewMode === 'mockup' ? 'Print File' : 'Mockup'}
          </span>
        </div>
        
        <div className="flex items-center space-x-1">
          {/* Minimize Button */}
          <button
            onClick={() => setIsMinimized(!isMinimized)}
            className={`rounded-md hover:bg-white/20 flex items-center justify-center text-gray-500 hover:text-gray-700 transition-colors ${window.innerWidth <= 768 ? 'w-4 h-4' : 'w-6 h-6'}`}
            title={isMinimized ? "Expand" : "Minimize"}
          >
            {isMinimized ? (
              <svg className={window.innerWidth <= 768 ? "w-2 h-2" : "w-3 h-3"} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 15l7-7 7 7" />
              </svg>
            ) : (
              <svg className={window.innerWidth <= 768 ? "w-2 h-2" : "w-3 h-3"} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </svg>
            )}
          </button>
        </div>
      </div>

      {/* Content */}
      {!isMinimized && (
        <div className={`${isMobile ? 'p-2' : 'p-4'}`}>
          {/* Mini Preview Canvas - Shows opposite of main canvas */}
          <div
            className={`relative w-full rounded-xl overflow-hidden flex items-center justify-center ${isMobile ? 'h-32' : 'h-48'}`}
            style={{
              background: 'linear-gradient(135deg, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.15))',
              backdropFilter: 'blur(20px) saturate(180%)',
              border: '1px solid rgba(255, 255, 255, 0.4)'
            }}
          >
            {/* Conditional Background - Show opposite of main canvas */}
            {viewMode === 'mockup' ? (
              // Main canvas shows mockup, so mini shows print file
              <img
                src={printFileUrl}
                alt={printFile.filename}
                className="max-w-full max-h-full object-contain"
                style={{
                  position: 'absolute',
                  top: '50%',
                  left: '50%',
                  transform: 'translate(-50%, -50%)'
                }}
                draggable={false}
                onError={(e) => {
                  console.error('Print file failed to load:', printFileUrl)
                }}
              />
            ) : (
              // Main canvas shows print file, so mini shows mockup
              product.mockup_files && product.mockup_files.length > 0 ? (
                <img
                  src={getFileUrl('product-mockups', product.mockup_files[0].url)}
                  alt="Product Mockup"
                  className="max-w-full max-h-full object-contain"
                  style={{
                    position: 'absolute',
                    top: '50%',
                    left: '50%',
                    transform: 'translate(-50%, -50%)'
                  }}
                  draggable={false}
                  onError={(e) => {
                    console.error('Mockup file failed to load:', getFileUrl('product-mockups', product.mockup_files[0].url))
                    console.error('Mockup file data:', product.mockup_files[0])
                  }}
                />
              ) : (
                // Fallback if no mockup files
                <div className="absolute inset-0 flex items-center justify-center text-gray-500 text-sm">
                  No mockup available
                </div>
              )
            )}

            {/* Design Elements Overlay (scaled down) */}
            <div className="absolute inset-0">
              {designElements.map((element) => {
                const scale = isMobile ? 0.4 : 0.6 // Smaller scale for mobile mini view
                const scaledStyle = {
                  position: 'absolute' as const,
                  left: element.x * scale,
                  top: element.y * scale,
                  width: element.width * scale,
                  height: element.height * scale,
                  transform: `rotate(${element.rotation}deg) scale(${scale})`,
                  transformOrigin: 'top left',
                  zIndex: element.zIndex,
                  pointerEvents: 'none' as const
                }

                switch (element.type) {
                  case 'text':
                    return (
                      <div
                        key={element.id}
                        style={{
                          ...scaledStyle,
                          fontSize: element.data.fontSize * scale,
                          color: element.data.color,
                          fontFamily: element.data.fontFamily,
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          userSelect: 'none'
                        }}
                      >
                        {element.data.text}
                      </div>
                    )

                  case 'image':
                    return (
                      <img
                        key={element.id}
                        src={element.data.src}
                        alt={element.data.filename}
                        style={{
                          ...scaledStyle,
                          objectFit: 'contain'
                        }}
                        draggable={false}
                      />
                    )

                  case 'shape':
                    const shapeStyle = {
                      ...scaledStyle,
                      backgroundColor: element.data.color,
                      border: `${element.data.borderWidth * scale}px solid ${element.data.borderColor}`
                    }

                    if (element.data.shape === 'circle') {
                      return (
                        <div
                          key={element.id}
                          style={{
                            ...shapeStyle,
                            borderRadius: '50%'
                          }}
                        />
                      )
                    }

                    return (
                      <div
                        key={element.id}
                        style={shapeStyle}
                      />
                    )

                  default:
                    return null
                }
              })}
            </div>

            {/* Overlay Effect */}
            <div className="absolute inset-0 bg-gradient-to-br from-blue-500/10 via-transparent to-purple-500/10 pointer-events-none" />
          </div>

          {/* Full-Width Switch Button */}
          <button
            onClick={onViewModeSwitch}
            className={`w-full mt-3 px-3 backdrop-blur-xl border border-green-400/60 rounded-lg shadow-lg hover:shadow-xl transition-all duration-200 flex items-center justify-center space-x-2 text-gray-700 hover:text-gray-900 hover:scale-[1.02] ${isMobile ? 'py-1.5' : 'py-2'}`}
            style={{
              background: 'linear-gradient(135deg, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.15))',
              backdropFilter: 'blur(20px) saturate(180%)',
              boxShadow: '0 4px 16px rgba(0, 0, 0, 0.1), 0 0 0 1px rgba(255, 255, 255, 0.3) inset, 0 0 8px rgba(34, 197, 94, 0.4), 0 0 16px rgba(34, 197, 94, 0.2)',
              borderColor: 'rgba(34, 197, 94, 0.6)'
            }}
          >
            <svg className={isMobile ? "w-3.5 h-3.5" : "w-4 h-4"} fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m0-4l4-4" />
            </svg>
            <span className={`font-medium ${isMobile ? 'text-xs' : 'text-sm'}`}>
              Switch View
            </span>
          </button>

        </div>
      )}
    </div>
  )
}
