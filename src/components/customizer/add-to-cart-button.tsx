'use client'

interface AddToCartButtonProps {
  onAddToCart: () => void
  isLoading?: boolean
  disabled?: boolean
}

export default function AddToCartButton({ 
  onAddToCart, 
  isLoading = false, 
  disabled = false 
}: AddToCartButtonProps) {
  return (
    <div className="relative inline-block">
      <button
        onClick={onAddToCart}
        disabled={disabled || isLoading}
        className={`
          group relative px-2 md:px-6 py-1.5 md:py-3 text-white font-semibold rounded-lg md:rounded-xl shadow-2xl backdrop-blur-xl border border-white/30
          transition-all duration-300 transform hover:scale-105 hover:shadow-2xl
          disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none
          text-xs md:text-sm
          ${isLoading ? 'animate-pulse' : ''}
        `}
        style={{
          background: 'linear-gradient(135deg, rgba(99, 102, 241, 0.8), rgba(139, 92, 246, 0.8))',
          backdropFilter: 'blur(20px)',
          boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(255, 255, 255, 0.2) inset'
        }}
      >
        {/* Background Effects */}
        <div
          className="absolute inset-0 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-xl"
          style={{
            background: 'linear-gradient(135deg, rgba(99, 102, 241, 0.6), rgba(139, 92, 246, 0.6))'
          }}
        />
        
        {/* Button Content */}
        <div className="relative flex items-center space-x-1 md:space-x-2">
          {isLoading ? (
            <>
              <div className="w-3 h-3 md:w-4 md:h-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
              <span className="hidden sm:inline">Adding to Cart...</span>
              <span className="sm:hidden">Adding...</span>
            </>
          ) : (
            <>
              <svg className="w-3 h-3 md:w-4 md:h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-1.1 5M7 13l-1.1 5m0 0h9.1M16 18a2 2 0 11-4 0 2 2 0 014 0zm-8 0a2 2 0 11-4 0 2 2 0 014 0z" />
              </svg>
              <span className="hidden sm:inline">Add to Cart</span>
              <span className="sm:hidden">Add</span>
            </>
          )}
        </div>

        {/* Shine Effect */}
        <div className="absolute inset-0 rounded-2xl overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-700" />
        </div>
      </button>

      {/* Floating Action Hint */}
      {disabled && (
        <div
          className="absolute -top-12 left-1/2 transform -translate-x-1/2 px-3 py-1 text-white text-xs rounded-lg whitespace-nowrap backdrop-blur-xl border border-white/20"
          style={{
            background: 'linear-gradient(135deg, rgba(0, 0, 0, 0.8), rgba(0, 0, 0, 0.6))',
            backdropFilter: 'blur(20px)'
          }}
        >
          Add some design elements first
          <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-black/80" />
        </div>
      )}
    </div>
  )
}
