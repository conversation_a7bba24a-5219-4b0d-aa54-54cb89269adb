'use client'

import { useState, useRef, useCallback, useEffect } from 'react'
import { createSupabaseBrowserClient } from '@/lib/supabase'
import { validateProductSetup, ValidationResult } from '@/lib/product-validation'
import dynamic from 'next/dynamic'
import CustomizerHeader from './customizer-header'
import ToolsPanel from './tools-panel'
import ColorPicker from './color-picker'
import SetupIncompleteModal from './setup-incomplete-modal'
import AddToCartButton from './add-to-cart-button'
import LayerPanel from './layer-panel'
import { DesignAreaUV } from '@/lib/uv-extractor'
import { FinalTextureEngine } from '@/lib/final-texture-engine'
import { ThreeDInteractionManager } from '@/lib/3d-interaction-manager'
import DualCanvasLayout, { useDualCanvasResponsive } from './dual-canvas-layout'
import { useCanvasViewManager } from './canvas-view-manager'
import { useDesignElements, useCustomizerUI, use3DState, useCanvasState } from '@/hooks/use-customizer-state'
import { InitializationState } from '@/components/providers/customizer-provider'
import DebugMappingScreen from './debug-mapping-screen'

// Dynamically import components to avoid SSR issues
// DesignCanvas removed - deprecated component replaced by 3D interaction system

// ShapeCreator removed - using 3D interaction system instead

const Simple3DViewer = dynamic(() => import('@/components/Simple3DViewer'), {
  ssr: false,
  loading: () => (
    <div className="flex items-center justify-center h-96">
      <div className="text-gray-500">Loading 3D viewer...</div>
    </div>
  )
})



interface Product {
  id: string
  name: string
  description: string | null
  base_price: number
  print_files?: any[]
  mockup_files?: any[]
  glb_files?: any[]
  product_images?: any[]
}

export interface DesignElement {
  id: string
  type: 'text' | 'image' | 'shape'
  x: number
  y: number
  width: number
  height: number
  rotation: number
  zIndex: number
  data: any
}

interface UnifiedCustomizerInterfaceProps {
  product: Product
  isIframe?: boolean
  shop?: string
  variant?: string
}

export default function UnifiedCustomizerInterface({
  product,
  isIframe = false,
  shop,
  variant
}: UnifiedCustomizerInterfaceProps) {
  // Dual-canvas view management
  const canvasViewManager = useCanvasViewManager({
    initialMainView: '3d',
    initialGLBIndex: 0,
    initialPrintFileIndex: 0
  })

  // Responsive layout configuration
  const responsiveConfig = useDualCanvasResponsive()

  // Centralized state hooks
  const {
    designElements,
    selectedElementId,
    editingTextId,
    addElement,
    updateElement,
    deleteElement,
    setSelectedElement,
    setEditingText
  } = useDesignElements()

  const {
    selectedColor,
    showSetupModal,
    setColor,
    setLoading,
    setSetupModal
  } = useCustomizerUI()

  const {
    designAreas,
    selectedDesignArea,
    textureEngine,
    interactionManager,
    showWireframe,
    meshVisibility,
    enable3DInteraction,
    initializationState,
    setDesignAreas,
    setSelectedDesignArea,
    setTextureEngine,
    setInteractionManager,
    setAvailableMeshes,
    setInitializationState
  } = use3DState()

  const {
    shouldShow3DInMain
  } = useCanvasState()

  // Legacy state (to be migrated)
  const [validationResult, setValidationResult] = useState<ValidationResult | null>(null)

  // Debug state
  const [showDebugMapping, setShowDebugMapping] = useState(false)

  // Auto-switch debouncing
  const autoSwitchTimeoutRef = useRef<NodeJS.Timeout | null>(null)

  // Use ref to store current design areas to avoid stale closure issues
  const designAreasRef = useRef<DesignAreaUV[]>([])

  // Mesh to print file mapping function (extracted from debug screen logic)
  const findMatchingPrintFile = useCallback((meshName: string) => {
    const printFiles = product.print_files || []
    if (!printFiles.length) return null

    const meshNameLower = meshName.toLowerCase()

    // Strategy 1: Exact filename match
    for (const file of printFiles) {
      if (file.filename) {
        const fileName = file.filename.toLowerCase().replace(/\.(png|jpg|jpeg)$/i, '')
        if (fileName === meshNameLower) {
          return file
        }
      }
    }

    // Strategy 2: Exact URL filename match
    for (const file of printFiles) {
      if (file.url) {
        const urlParts = file.url.split('/')
        const urlFileName = urlParts[urlParts.length - 1] || ''
        const urlFileNameWithoutExt = urlFileName.toLowerCase().replace(/\.(png|jpg|jpeg)$/i, '')

        if (urlFileNameWithoutExt === meshNameLower) {
          return file
        }
      }
    }

    // Strategy 3: Partial matching (fallback)
    for (const file of printFiles) {
      const searchTargets = [
        file.filename?.toLowerCase() || '',
        file.url?.toLowerCase() || ''
      ]

      for (const target of searchTargets) {
        if (target.includes(meshNameLower) || meshNameLower.includes(target.replace(/\.(png|jpg|jpeg)$/i, ''))) {
          return file
        }
      }
    }

    return null // No match found
  }, [product.print_files])

  // Auto-switch print file based on mesh selection with debouncing and animation
  const autoSwitchPrintFile = useCallback((meshName: string) => {
    // Clear any existing timeout
    if (autoSwitchTimeoutRef.current) {
      clearTimeout(autoSwitchTimeoutRef.current)
    }

    // Debounce the switch (250ms delay for fast & snappy feel)
    autoSwitchTimeoutRef.current = setTimeout(() => {
      console.log('🎯 Auto-switching print file for mesh:', meshName)

      // Find matching print file
      const matchingFile = findMatchingPrintFile(meshName)

      if (matchingFile) {
        // Find the index of the matching file
        const printFiles = product.print_files || []
        const fileIndex = printFiles.findIndex(file =>
          file.url === matchingFile.url || file.filename === matchingFile.filename
        )

        if (fileIndex !== -1 && fileIndex !== canvasViewManager.state.selectedPrintFileIndex) {
          console.log(`🎯 Switching to print file index ${fileIndex}: ${matchingFile.filename || matchingFile.url}`)

          // Trigger smooth left/right swipe animation by updating the selected print file
          canvasViewManager.setSelectedPrintFile(fileIndex)
        } else {
          console.log('🎯 Print file already selected or not found in array')
        }
      } else {
        console.log('🎯 No matching print file found for mesh:', meshName, '- staying on current view')
      }
    }, 250) // Fast & snappy 250ms debounce
  }, [findMatchingPrintFile, product.print_files, canvasViewManager])

  // Update ref whenever design areas change
  useEffect(() => {
    designAreasRef.current = designAreas
    console.log('🎨 Design areas ref updated:', designAreas.length, designAreas.map(a => a.areaName))
  }, [designAreas])

  // Refs
  const fileInputRef = useRef<HTMLInputElement>(null)
  const supabase = createSupabaseBrowserClient()

  // Initialize with first GLB file if available
  useEffect(() => {
    console.log('🔍 UnifiedCustomizer - Product received:', product)
    console.log('🔍 UnifiedCustomizer - GLB files:', product.glb_files)
    console.log('🔍 UnifiedCustomizer - GLB files length:', product.glb_files?.length)
    console.log('🔍 UnifiedCustomizer - GLB files type:', typeof product.glb_files)

    // Use actual product GLB files from database
    console.log('🔍 FIXED: Checking product GLB files...')
    console.log('🔍 FIXED: product.glb_files:', product.glb_files)
    console.log('🔍 FIXED: GLB files length:', product.glb_files?.length || 0)

    if (product.glb_files && product.glb_files.length > 0) {
      console.log('🎯 FIXED: Setting 3D view mode with GLB:', product.glb_files[0])
      console.log('🎯 FIXED: Available GLB files:', product.glb_files.length)
      console.log('🎯 FIXED: All GLB files:', product.glb_files.map(f => f.filename || f.name))
      canvasViewManager.setMainView('3d')
    } else if (product.mockup_files && product.mockup_files.length > 0) {
      console.log('🎯 Setting mockup view mode')
      canvasViewManager.setMainView('3d') // Default to 3D for now
    } else if (product.print_files && product.print_files.length > 0) {
      console.log('🎯 Setting printfile view mode')
      canvasViewManager.setMainView('printfile')
    } else {
      console.log('🎯 No files found, staying with default view mode')
    }
  }, [product])

  // Validate product setup on mount
  useEffect(() => {
    const validateProduct = async () => {
      try {
        const result = await validateProductSetup(product)
        setValidationResult(result)
        if (!result.isValid) {
          setSetupModal(true)
        }
      } catch (error) {
        console.error('Error validating product:', error)
        // Set a fallback validation result
        setValidationResult({
          isValid: true, // Don't show modal if validation fails
          errors: [],
          warnings: []
        })
      }
    }

    validateProduct()
  }, [product])

  // File URL helper
  const getFileUrl = useCallback((bucket: string, path: string) => {
    const { data } = supabase.storage.from(bucket).getPublicUrl(path)
    return data.publicUrl
  }, [supabase])

  // GLB URL helper
  const getGLBUrl = useCallback((glbFile: any) => {
    // Check if this is a public asset (temporary for testing)
    if (glbFile.isPublicAsset || glbFile.url.startsWith('/')) {
      return glbFile.url
    }
    // For database GLB files, the URL is stored in the 'url' field
    // and they're stored in 'product-glb-files' bucket
    console.log('🔗 Getting GLB URL for:', glbFile)
    return getFileUrl('product-glb-files', glbFile.url)
  }, [getFileUrl])

  // Design element operations - using centralized state
  const addElementLocal = useCallback((element: Omit<DesignElement, 'id' | 'zIndex'>) => {
    console.log('🎨 Adding element:', element)
    const newElement: DesignElement = {
      ...element,
      id: `element_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
      zIndex: designElements.length
    }
    console.log('🎨 New element created:', newElement)

    // Add to centralized state
    addElement(newElement)
    setSelectedElement(newElement.id)

    // Apply to 3D texture engine for immediate visual feedback (independent of interaction manager)
    if (shouldShow3DInMain && textureEngine && selectedDesignArea) {
      console.log('🎨 Applying to 3D texture engine:', {
        textureEngine: !!textureEngine,
        selectedDesignArea: selectedDesignArea?.areaName,
        initializationState
      })
      setTimeout(() => applyDesignTo3D([...designElements, newElement]), 10) // Minimal delay for state update
    } else {
      console.log('🎨 Not applying to 3D:', {
        shouldShow3DInMain: shouldShow3DInMain,
        textureEngine: !!textureEngine,
        selectedDesignArea: selectedDesignArea?.areaName,
        initializationState
      })
    }

    // Note: Interaction manager sync will happen automatically via useEffect when FULLY_INITIALIZED
  }, [designElements, shouldShow3DInMain, textureEngine, selectedDesignArea, enable3DInteraction, interactionManager, addElement, setSelectedElement, initializationState])

  const updateElementLocal = useCallback((id: string, updates: Partial<DesignElement>) => {
    // Update in centralized state
    updateElement(id, updates)

    // Use interaction manager if 3D interaction is enabled
    if (shouldShow3DInMain && enable3DInteraction && interactionManager) {
      const updatedElement = designElements.find(el => el.id === id)
      if (updatedElement) {
        setTimeout(() => interactionManager.updateElement(id, { ...updatedElement, ...updates }), 50)
      }
    }
    // Fallback to old texture engine method if interaction manager not available
    else if (shouldShow3DInMain && textureEngine && selectedDesignArea) {
      const updatedElements = designElements.map(el => el.id === id ? { ...el, ...updates } : el)
      setTimeout(() => applyDesignTo3D(updatedElements), 50) // Small delay for state update
    }
  }, [shouldShow3DInMain, textureEngine, selectedDesignArea, enable3DInteraction, interactionManager, updateElement, designElements])

  const deleteElementLocal = useCallback((id: string) => {
    // Remove from interaction manager if enabled
    if (enable3DInteraction && interactionManager) {
      interactionManager.removeElement(id)
    }

    // Remove from centralized state
    deleteElement(id)

    if (selectedElementId === id) {
      setSelectedElement(null)
    }
  }, [selectedElementId, enable3DInteraction, interactionManager, deleteElement, setSelectedElement])

  const reorderElements = useCallback((newOrder: DesignElement[]) => {
    // Update each element's zIndex in centralized state
    newOrder.forEach((el, index) => {
      updateElement(el.id, { zIndex: index })
    })
  }, [updateElement])

  // 3D-specific handlers
  const handleDesignAreasExtracted = useCallback((areas: DesignAreaUV[]) => {
    console.log('🎨 Design areas extracted:', areas.map(a => a.areaName))
    console.log('🎨 Setting design areas in state...')
    setDesignAreas(areas)
    setInitializationState(InitializationState.DESIGN_AREAS_EXTRACTED)

    if (areas.length > 0) {
      // Prefer "Front" area as it's most visible, fallback to first area
      const frontArea = areas.find(area => area.areaName === 'Front') || areas[0]
      setSelectedDesignArea(frontArea)
      setInitializationState(InitializationState.DESIGN_AREA_SELECTED)
      console.log('🎨 Auto-selected design area:', frontArea.areaName)
    }
  }, [])

  // Debug: Track design areas state changes
  useEffect(() => {
    console.log('🎨 Design areas state changed:', designAreas.length, designAreas.map(a => a.areaName))
  }, [designAreas])

  // Fallback to ensure a design area is always selected
  useEffect(() => {
    if (designAreas.length > 0 && !selectedDesignArea) {
      console.log('🎨 Fallback: No design area selected, auto-selecting Front or first area')
      const frontArea = designAreas.find(area => area.areaName === 'Front') || designAreas[0]
      setSelectedDesignArea(frontArea)
      console.log('🎨 Fallback selected design area:', frontArea.areaName)
    }
  }, [designAreas, selectedDesignArea])

  const handleTextureEngineReady = useCallback((engine: FinalTextureEngine) => {
    console.log('🎨 Texture engine ready!')
    setTextureEngine(engine)
    setInitializationState(InitializationState.TEXTURE_ENGINE_READY)
  }, [])

  // Inline editing handlers
  const handleStartTextEdit = useCallback((elementId: string) => {
    console.log('🎨 Starting text edit for element:', elementId)
    setEditingText(elementId)
    setSelectedElement(elementId) // Also select the element
  }, [])

  const handleFinishTextEdit = useCallback(() => {
    console.log('🎨 Finishing text edit, applying to 3D model')
    setEditingText(null)

    // Apply current design elements to 3D model after editing
    if (textureEngine && selectedDesignArea && designElements.length > 0) {
      setTimeout(() => applyDesignTo3D(designElements), 50)
    }
  }, [textureEngine, selectedDesignArea, designElements])

  // Handle state transitions to FULLY_INITIALIZED
  useEffect(() => {
    if (initializationState === InitializationState.INTERACTION_MANAGER_READY && selectedDesignArea && interactionManager) {
      interactionManager.setCurrentDesignArea(selectedDesignArea)
      setInitializationState(InitializationState.FULLY_INITIALIZED)
      console.log('🎯 3D System FULLY INITIALIZED!')
    } else if (initializationState === InitializationState.DESIGN_AREA_SELECTED && interactionManager) {
      interactionManager.setCurrentDesignArea(selectedDesignArea!)
      setInitializationState(InitializationState.FULLY_INITIALIZED)
      console.log('🎯 3D System FULLY INITIALIZED!')
    }
  }, [initializationState, selectedDesignArea, interactionManager, setInitializationState])

  // Sync design elements with interaction manager when FULLY INITIALIZED
  useEffect(() => {
    if (initializationState === InitializationState.FULLY_INITIALIZED && interactionManager && designElements.length > 0) {
      console.log('🎮 Syncing design elements with interaction manager (FULLY INITIALIZED)')
      designElements.forEach(element => {
        interactionManager.addElement(element)
      })
    }
  }, [initializationState, interactionManager, designElements])

  const handleMeshClick = useCallback((meshName: string) => {
    // Use ref to get current design areas (avoids stale closure)
    const currentDesignAreas = designAreasRef.current

    console.log('🎨 Mesh clicked:', meshName, 'Available areas:', currentDesignAreas.map(a => a.areaName))
    console.log('🎨 Current selected area:', selectedDesignArea?.areaName)

    // Skip empty mesh names
    if (!meshName || meshName.trim() === '') {
      console.log('🎨 Ignoring empty mesh name')
      return
    }

    // Skip mask clicks - they're not design areas
    if (meshName.toLowerCase().includes('mask')) {
      console.log('🎨 Ignoring mask click - not a design area')
      return
    }

    // Try exact match first
    let area = currentDesignAreas.find(area => area.areaName === meshName)

    // If no exact match, try fuzzy matching
    if (!area) {
      // Try case insensitive
      area = currentDesignAreas.find(area => area.areaName.toLowerCase() === meshName.toLowerCase())
    }

    if (!area) {
      // Try partial matches - if mesh name contains area name or vice versa
      area = currentDesignAreas.find(area =>
        area.areaName.toLowerCase().includes(meshName.toLowerCase()) ||
        meshName.toLowerCase().includes(area.areaName.toLowerCase())
      )
    }

    if (area) {
      console.log('🎯 SUCCESS! Switching to design area:', area.areaName)
      setSelectedDesignArea(area)

      // Set current design area in interaction manager
      if (interactionManager) {
        interactionManager.setCurrentDesignArea(area)
      }

      // 🎬 AUTO-SWITCH: Trigger print file auto-switch with smooth animation
      autoSwitchPrintFile(meshName)
    } else {
      console.log('🎨 No matching design area found for mesh:', meshName)
      console.log('🎨 Available design areas:', currentDesignAreas.map(a => a.areaName))
    }
  }, [selectedDesignArea, interactionManager, autoSwitchPrintFile])

  // Handle 3D interaction manager ready
  const handleInteractionReady = useCallback((manager: ThreeDInteractionManager) => {
    console.log('🎮 3D Interaction Manager ready')
    setInteractionManager(manager)
    setInitializationState(InitializationState.INTERACTION_MANAGER_READY)

    // Set current design area if one is already selected
    if (selectedDesignArea) {
      manager.setCurrentDesignArea(selectedDesignArea)
      setInitializationState(InitializationState.FULLY_INITIALIZED)
      console.log('🎯 3D System FULLY INITIALIZED!')
    }
  }, [selectedDesignArea])

  // Handle canvas updates from interaction manager
  const handleCanvasUpdate = useCallback((_canvas: HTMLCanvasElement) => {
    // This could be used to update texture engine or other systems
    console.log('🎮 Canvas updated from interaction manager')
  }, [])

  // Save design handler
  const handleSaveDesign = useCallback(async () => {
    if (designElements.length === 0) return

    try {
      setLoading(true)

      const designData = {
        productId: product.id,
        mainView: canvasViewManager.state.mainCanvasView,
        miniView: canvasViewManager.state.miniCanvasView,
        selectedDesignArea: selectedDesignArea?.areaName,
        designElements,
        timestamp: new Date().toISOString()
      }

      // For now, save to localStorage (can be extended to save to database)
      const savedDesigns = JSON.parse(localStorage.getItem('savedDesigns') || '[]')
      const designId = `design_${Date.now()}`
      savedDesigns.push({ id: designId, ...designData })
      localStorage.setItem('savedDesigns', JSON.stringify(savedDesigns))

      // Show success feedback
      alert('Design saved successfully!')

    } catch (error) {
      console.error('Error saving design:', error)
      alert('Failed to save design. Please try again.')
    } finally {
      setLoading(false)
    }
  }, [product.id, canvasViewManager.state, selectedDesignArea, designElements])

  // Helper function to get center position for design area
  const getCenterPosition = useCallback((designArea: DesignAreaUV | null) => {
    if (!designArea) {
      return { x: 256, y: 256 } // Default center for 512x512 canvas
    }

    // For smaller areas like sleeves and collar, use center of canvas
    // The texture engine will handle proper UV mapping
    return { x: 256, y: 256 }
  }, [])

  // File upload handler
  const handleFileUpload = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    console.log('🎨 File upload triggered:', event.target.files)
    const file = event.target.files?.[0]
    if (!file) {
      console.log('🎨 No file selected')
      return
    }

    console.log('🎨 File selected:', file.name, file.type, file.size)
    const reader = new FileReader()
    reader.onload = (e) => {
      const imageUrl = e.target?.result as string
      console.log('🎨 File loaded, adding image element')
      const centerPos = getCenterPosition(selectedDesignArea || null)
      addElementLocal({
        type: 'image',
        x: centerPos.x - 100, // Center the 200px wide image
        y: centerPos.y - 100, // Center the 200px tall image
        width: 200,
        height: 200,
        rotation: 0,
        data: {
          src: imageUrl,
          originalFile: file,
          meshName: selectedDesignArea?.areaName // Store which mesh this image belongs to
        }
      })
    }
    reader.readAsDataURL(file)

    // Reset the file input so the same file can be selected again
    event.target.value = ''
  }, [addElement, selectedDesignArea, getCenterPosition])

  // Apply design elements to 3D model
  const applyDesignTo3D = useCallback(async (elements: DesignElement[]) => {
    console.log('🎨 applyDesignTo3D called:', {
      textureEngine: !!textureEngine,
      selectedDesignArea: selectedDesignArea?.areaName,
      elementsCount: elements.length
    })

    if (!textureEngine || !selectedDesignArea || elements.length === 0) {
      console.log('🎨 applyDesignTo3D early return:', {
        textureEngine: !!textureEngine,
        selectedDesignArea: !!selectedDesignArea,
        elementsLength: elements.length
      })
      return
    }

    try {
      // Filter elements to only include those for the current design area
      const elementsForCurrentArea = elements.filter(element => {
        const elementMeshName = element.data?.meshName
        const currentMeshName = selectedDesignArea.areaName

        // If element doesn't have meshName, it's from old system - apply to current area
        if (!elementMeshName) return true

        // Only apply elements that belong to current design area
        return elementMeshName === currentMeshName
      })

      console.log('🎨 Filtered elements for', selectedDesignArea.areaName, ':', elementsForCurrentArea.length, 'out of', elements.length)

      // Create a canvas with the design elements
      const canvas = document.createElement('canvas')
      canvas.width = 512
      canvas.height = 512
      const ctx = canvas.getContext('2d')!

      // Fill with transparent background
      ctx.clearRect(0, 0, canvas.width, canvas.height)

      // Render each design element to the canvas - SIMPLIFIED approach
      for (const element of elementsForCurrentArea) {
        console.log('🎨 Processing element:', element.type, element.id)

        if (element.type === 'text') {
          console.log('🎨 Rendering text element:', element.data.text)

          // Production-ready text rendering - centered on canvas
          const fontSize = element.data.fontSize || 48
          ctx.fillStyle = element.data.color || '#000000'
          ctx.font = `${fontSize}px ${element.data.fontFamily || 'Arial'}`
          ctx.textAlign = 'center'
          ctx.textBaseline = 'middle'

          const textToRender = element.data.text || 'Sample Text'
          const x = canvas.width / 2
          const y = canvas.height / 2

          ctx.fillText(textToRender, x, y)
        } else if (element.type === 'shape') {
          console.log('🎨 Rendering shape element:', element.data.shape)
          ctx.fillStyle = element.data.color || '#000000'
          if (element.data.shape === 'rectangle') {
            ctx.fillRect(0, 0, element.width, element.height)
          } else if (element.data.shape === 'circle') {
            ctx.beginPath()
            ctx.arc(element.width / 2, element.height / 2, Math.min(element.width, element.height) / 2, 0, 2 * Math.PI)
            ctx.fill()
          }
        } else if (element.type === 'image' && element.data.src) {
          console.log('🎨 Rendering image element')
          // Handle images asynchronously - but don't break the main flow
          const img = new Image()
          img.crossOrigin = 'anonymous'
          img.onload = () => {
            console.log('🎨 Image loaded, drawing image to canvas')
            // Draw image centered on canvas
            const x = (canvas.width - element.width) / 2
            const y = (canvas.height - element.height) / 2
            ctx.drawImage(img, x, y, element.width, element.height)
            // Reapply the texture after image loads
            textureEngine.applyDesignToMesh(selectedDesignArea, canvas)
          }
          img.onerror = () => {
            console.error('🎨 Failed to load image:', element.data.src)
          }
          img.src = element.data.src
          // Don't restore context here, continue with other elements
        }

      }

      // Apply the canvas as texture to the selected design area
      console.log('🎨 Applying texture to mesh:', selectedDesignArea.areaName)
      textureEngine.applyDesignToMesh(selectedDesignArea, canvas)

    } catch (error) {
      console.error('Error applying design to 3D model:', error)
    }
  }, [textureEngine, selectedDesignArea])

  // Add to cart handler
  const handleAddToCart = useCallback(async () => {
    if (designElements.length === 0) {
      alert('Please add some design elements before adding to cart.')
      return
    }

    try {
      setLoading(true)

      if (isIframe && shop) {
        // Send postMessage to parent window (Shopify theme)
        const customizationData = {
          productId: product.id,
          variant: variant,
          designElements: designElements,
          mainView: canvasViewManager.state.mainCanvasView,
          miniView: canvasViewManager.state.miniCanvasView,
          selectedDesignArea: selectedDesignArea?.areaName,
          timestamp: new Date().toISOString()
        }

        window.parent.postMessage({
          type: 'ADD_TO_CART',
          data: customizationData
        }, '*')
      } else {
        // Handle standalone add to cart
        const cartData = {
          product,
          designElements,
          mainView: canvasViewManager.state.mainCanvasView,
          miniView: canvasViewManager.state.miniCanvasView,
          selectedDesignArea: selectedDesignArea?.areaName,
          customizationId: `custom_${Date.now()}`
        }

        // Save to localStorage for demo purposes
        const cart = JSON.parse(localStorage.getItem('customizedCart') || '[]')
        cart.push(cartData)
        localStorage.setItem('customizedCart', JSON.stringify(cart))

        alert('Product added to cart with customization!')
      }
    } catch (error) {
      console.error('Error adding to cart:', error)
      alert('Failed to add to cart. Please try again.')
    } finally {
      setLoading(false)
    }
  }, [isIframe, shop, product, variant, designElements, canvasViewManager.state, selectedDesignArea])



  return (
    <div className="customizer-container w-full h-screen flex flex-col relative overflow-hidden" style={{
      background: 'linear-gradient(135deg, #ffffff 0%, #e8e9eb 100%)',
      height: '100dvh', // Dynamic viewport height for mobile
    }}>
      {/* Mobile-first responsive styles - maintaining your loved layout */}
      <style jsx global>{`
        .dragging * {
          user-select: none !important;
          -webkit-user-select: none !important;
          -moz-user-select: none !important;
          -ms-user-select: none !important;
        }

        html, body {
          overflow: hidden;
          position: fixed;
          width: 100%;
          height: 100%;
          height: 100dvh;
          -webkit-overflow-scrolling: touch;
          overscroll-behavior: none;
        }

        .customizer-container {
          position: fixed;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          width: 100vw;
          height: 100vh;
          height: 100dvh;
          overflow: hidden;
        }

        .customizer-header {
          flex-shrink: 0;
          height: 60px;
          z-index: 50;
        }

        .customizer-canvas {
          flex: 1;
          position: relative;
          overflow: hidden;
          min-height: 0;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        @media (min-width: 768px) {
          /* Remove customizer-tools styles */
        }
      `}</style>

      {/* Hidden file input */}
      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        onChange={handleFileUpload}
        className="hidden"
      />

      {/* Header - maintaining your loved positioning */}
      <div className="customizer-header">
        <CustomizerHeader
          productName={product.name}
          isIframe={isIframe}
        />
      </div>



      {/* Main Canvas Area - your loved layout preserved */}
      <div className="customizer-canvas">
        {/* Top Right Action Buttons - maintaining your loved positioning */}
        <div className="absolute top-2 right-2 md:top-4 md:right-4 flex items-center space-x-1 md:space-x-3 z-10">
          {/* Debug Mapping Button */}
          <button
            onClick={() => setShowDebugMapping(true)}
            className="px-3 py-2 rounded-lg text-sm font-medium shadow-sm transition-colors bg-blue-500 hover:bg-blue-600 text-white"
            title="Debug mesh to print file mapping"
          >
            🔍 Debug
          </button>

          {/* Save Design Button */}
          <button
            onClick={handleSaveDesign}
            disabled={designElements.length === 0}
            className={`px-3 py-2 rounded-lg text-sm font-medium shadow-sm transition-colors ${
              designElements.length === 0
                ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                : 'bg-green-500 hover:bg-green-600 text-white'
            }`}
          >
            💾 Save
          </button>
        </div>

        {/* Dual Canvas Layout */}
        <DualCanvasLayout
          mainCanvas={
            canvasViewManager.shouldShow3DInMain ? (
              <div className="w-full h-full flex items-start justify-center p-2 md:p-4 pt-8" style={{ 
                height: 'calc(100vh - 60px)',
                marginTop: '60px'
              }}>
                <div className="bg-white rounded-xl shadow-lg p-2 md:p-4 max-w-full max-h-full flex flex-col items-center" style={{
                  marginTop: '-26px' // Negative value moves up, positive moves down
                }}>
                  <Simple3DViewer
                    glbUrl={product.glb_files && product.glb_files[canvasViewManager.state.selectedGLBIndex] ? getGLBUrl(product.glb_files[canvasViewManager.state.selectedGLBIndex]) : ''}
                    width={650}
                    height={650}
                    responsive={true}
                    showWireframe={showWireframe}
                    meshVisibility={meshVisibility}
                    onMeshesLoaded={setAvailableMeshes}
                    onDesignAreasExtracted={handleDesignAreasExtracted}
                    onTextureEngineReady={handleTextureEngineReady}
                    onMeshClick={handleMeshClick}
                    editingTextId={editingTextId}
                    designElements={designElements}
                    onUpdateElement={updateElementLocal}
                    onFinishTextEdit={handleFinishTextEdit}
                    selectedElement={selectedElementId}
                    onSelectElement={setSelectedElement}
                    onStartTextEdit={handleStartTextEdit}
                    selectedDesignArea={selectedDesignArea?.areaName}
                    enable3DInteraction={enable3DInteraction}
                    interactionConfig={{
                      enableHoverEffects: true,
                      enableTransformHandles: true,
                      enableVisualSelectors: true,
                      enableSurfaceDragging: true,
                      debugMode: false
                    }}
                    onInteractionReady={handleInteractionReady}
                    onCanvasUpdate={handleCanvasUpdate}
                  />
                </div>
              </div>
            ) : (
              // Print file view in main canvas - same container structure as 3D view
              <div className="w-full h-full flex items-start justify-center p-2 md:p-4 pt-8" style={{ 
                height: 'calc(100vh - 60px)',
                marginTop: '6px'
              }}>
                <div className="bg-white rounded-xl shadow-lg p-2 md:p-4 max-w-full max-h-full flex flex-col items-center">
                  <div className="w-[650px] h-[650px] flex items-center justify-center">
                    {product.print_files && product.print_files.length > 0 && (
                      <img
                        src={getFileUrl('product-files', product.print_files[canvasViewManager.state.selectedPrintFileIndex]?.url)}
                        alt={`Print file ${canvasViewManager.state.selectedPrintFileIndex + 1}`}
                        className="max-w-full max-h-full object-contain"
                      />
                    )}
                  </div>
                </div>
              </div>
            )
          }
          viewState={canvasViewManager.state}
          onSwapViews={canvasViewManager.swapViews}
          glbFiles={product.glb_files || []}
          selectedGLBIndex={canvasViewManager.state.selectedGLBIndex}
          onGLBSelect={canvasViewManager.setSelectedGLB}
          getGLBUrl={getGLBUrl}
          printFiles={product.print_files || []}
          selectedPrintFileIndex={canvasViewManager.state.selectedPrintFileIndex}
          onPrintFileSelect={canvasViewManager.setSelectedPrintFile}
          designElements={designElements}
          selectedDesignArea={selectedDesignArea}
          textureEngine={textureEngine}
          getFileUrl={getFileUrl}
          showLeftSidebar={responsiveConfig.showLeftSidebar}
          leftSidebarWidth={responsiveConfig.leftSidebarWidth}
          miniPreviewWidth={responsiveConfig.miniPreviewWidth}
          miniPreviewHeight={responsiveConfig.miniPreviewHeight}
        />
      </div>

      {/* Bottom Tools Panel - floating tools without full width background */}
      <div className="fixed bottom-4 left-1/2 transform -translate-x-1/2 z-40">
        <div className="flex flex-col items-center space-y-2 md:space-y-3">
          {/* Tools Panel - Floating */}
          <div className="transform translate-x-0 translate-y-0">
            <ToolsPanel
              onAddText={() => {
                console.log('🎨 Add Text clicked - creating inline editable text')

                if (!selectedDesignArea) {
                  // Try to auto-select a design area if available
                  if (designAreas.length > 0) {
                    const frontArea = designAreas.find(area => area.areaName === 'Front') || designAreas[0]
                    setSelectedDesignArea(frontArea)
                    console.log('🎨 Auto-selected design area for text:', frontArea.areaName)
                  } else {
                    alert('Please wait for the 3D model to load, then try again')
                    return
                  }
                }

                const centerPos = getCenterPosition(selectedDesignArea || null)
                const newElement: DesignElement = {
                  id: `element_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
                  type: 'text',
                  x: centerPos.x - 150, // Center the 300px wide text
                  y: centerPos.y - 40,  // Center the 80px tall text
                  width: 300,
                  height: 80,
                  rotation: 0,
                  zIndex: designElements.length,
                  data: {
                    text: 'HELLO WORLD',
                    fontSize: 48,
                    color: '#FF0000', // Use bright red for maximum visibility
                    fontFamily: 'Arial',
                    meshName: selectedDesignArea?.areaName || 'default' // Store which mesh this text belongs to
                  }
                }

                // Add the element using centralized state
                addElement(newElement)
                setSelectedElement(newElement.id)

                // Auto-apply to 3D model immediately
                if (textureEngine && selectedDesignArea) {
                  console.log('🎨 Auto-applying new text element to 3D model')
                  setTimeout(() => applyDesignTo3D([...designElements, newElement]), 50)
                }

                // Immediately start inline editing on the 3D model
                setTimeout(() => {
                  setEditingText(newElement.id)
                }, 100) // Small delay to ensure element is added
              }}
              onAddImage={() => {
                console.log('🎨 Add Photo clicked, triggering file input')

                // Ensure a design area is selected
                if (!selectedDesignArea) {
                  // Try to auto-select a design area if available
                  if (designAreas.length > 0) {
                    const frontArea = designAreas.find(area => area.areaName === 'Front') || designAreas[0]
                    setSelectedDesignArea(frontArea)
                    console.log('🎨 Auto-selected design area for photo:', frontArea.areaName)
                  } else {
                    alert('Please wait for the 3D model to load, then try again')
                    return
                  }
                }

                fileInputRef.current?.click()
              }}
              // Shape functionality removed - using 3D interaction system instead
              selectedColor={selectedColor}
            />
          </div>

          {/* Bottom Row: Color Picker and Add to Cart - separate floating elements */}
          <div className="flex items-center space-x-4">
            {/* Color Picker - Floating */}
            <ColorPicker
              selectedColor={selectedColor}
              onColorChange={setColor}
            />

            {/* Add to Cart - Floating */}
            <AddToCartButton
              onAddToCart={handleAddToCart}
            />
          </div>
        </div>
      </div>

      {/* Layer Panel - maintaining your loved positioning */}
      <LayerPanel
        designElements={designElements}
        selectedElement={selectedElementId}
        onSelectElement={setSelectedElement}
        onUpdateElement={updateElementLocal}
        onDeleteElement={deleteElementLocal}
        onReorderElements={reorderElements}
      />

      {/* Shape Creator removed - using 3D interaction system instead */}

      {/* Setup Incomplete Modal */}
      {validationResult && (
        <SetupIncompleteModal
          isOpen={showSetupModal}
          onClose={() => setSetupModal(false)}
          validationResult={validationResult}
          productName={product.name}
        />
      )}

      {/* Debug Mapping Screen */}
      <DebugMappingScreen
        isVisible={showDebugMapping}
        onClose={() => setShowDebugMapping(false)}
        designAreas={designAreas}
        printFiles={product.print_files || []}
        getFileUrl={getFileUrl}
        selectedDesignArea={selectedDesignArea || undefined}
      />
    </div>
  )
}
