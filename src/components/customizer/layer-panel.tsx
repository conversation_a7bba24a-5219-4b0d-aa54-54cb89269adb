'use client'

import { useState } from 'react'
import { DesignElement } from './unified-customizer-interface'

interface LayerPanelProps {
  designElements: DesignElement[]
  selectedElement: string | null
  onSelectElement: (id: string | null) => void
  onUpdateElement: (id: string, updates: Partial<DesignElement>) => void
  onDeleteElement: (id: string) => void
  onReorderElements: (elements: DesignElement[]) => void
}

const LayerPanel: React.FC<LayerPanelProps> = ({
  designElements,
  selectedElement,
  onSelectElement,
  onUpdateElement,
  onDeleteElement,
  onReorderElements
}) => {
  const [isOpen, setIsOpen] = useState(false)

  // Sort elements by z-index (highest first for display)
  const sortedElements = [...designElements].sort((a, b) => (b.zIndex || 0) - (a.zIndex || 0))

  // Move element up in layer order
  const moveUp = (elementId: string) => {
    const element = designElements.find(el => el.id === elementId)
    if (!element) return

    const currentZIndex = element.zIndex || 0
    const maxZIndex = Math.max(...designElements.map(el => el.zIndex || 0))
    
    if (currentZIndex < maxZIndex) {
      onUpdateElement(elementId, { zIndex: currentZIndex + 1 })
    }
  }

  // Move element down in layer order
  const moveDown = (elementId: string) => {
    const element = designElements.find(el => el.id === elementId)
    if (!element) return

    const currentZIndex = element.zIndex || 0
    const minZIndex = Math.min(...designElements.map(el => el.zIndex || 0))
    
    if (currentZIndex > minZIndex) {
      onUpdateElement(elementId, { zIndex: currentZIndex - 1 })
    }
  }

  // Move to front
  const moveToFront = (elementId: string) => {
    const maxZIndex = Math.max(...designElements.map(el => el.zIndex || 0))
    onUpdateElement(elementId, { zIndex: maxZIndex + 1 })
  }

  // Move to back
  const moveToBack = (elementId: string) => {
    const minZIndex = Math.min(...designElements.map(el => el.zIndex || 0))
    onUpdateElement(elementId, { zIndex: minZIndex - 1 })
  }

  // Toggle element visibility
  const toggleVisibility = (elementId: string) => {
    const element = designElements.find(el => el.id === elementId)
    if (!element) return

    onUpdateElement(elementId, { 
      data: { 
        ...element.data, 
        visible: element.data.visible !== false ? false : true 
      } 
    })
  }

  // Get element display name
  const getElementName = (element: DesignElement) => {
    switch (element.type) {
      case 'text':
        return `Text: ${(element.data.text || 'Text').substring(0, 20)}${element.data.text && element.data.text.length > 20 ? '...' : ''}`
      case 'shape':
        return `Shape: ${element.data.shape || 'Rectangle'}`
      case 'image':
        return 'Image'
      default:
        return 'Element'
    }
  }

  // Get element icon
  const getElementIcon = (element: DesignElement) => {
    switch (element.type) {
      case 'text':
        return (
          <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
            <path d="M5 4v3h5.5v12h3V7H19V4z"/>
          </svg>
        )
      case 'shape':
        if (element.data.shape === 'circle') {
          return (
            <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
              <circle cx="12" cy="12" r="10"/>
            </svg>
          )
        }
        return (
          <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
            <rect x="3" y="3" width="18" height="18" rx="2"/>
          </svg>
        )
      case 'image':
        return (
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
          </svg>
        )
      default:
        return null
    }
  }

  if (designElements.length === 0) return null

  return (
    <>
      {/* Layer Panel Toggle Button */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="fixed top-4 left-4 z-40 p-2 backdrop-blur-3xl border border-white/60 rounded-lg shadow-lg hover:shadow-xl transition-all duration-200"
        style={{
          background: 'linear-gradient(135deg, rgba(255, 255, 255, 0.4), rgba(255, 255, 255, 0.2))',
          backdropFilter: 'blur(40px) saturate(180%)',
        }}
        title="Layers"
      >
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14-7H5m14 14H5" />
        </svg>
      </button>

      {/* Layer Panel */}
      {isOpen && (
        <div
          className="fixed top-16 left-4 z-40 w-64 max-h-96 overflow-y-auto backdrop-blur-3xl border border-white/60 rounded-lg shadow-2xl"
          style={{
            background: 'linear-gradient(135deg, rgba(255, 255, 255, 0.4), rgba(255, 255, 255, 0.2))',
            backdropFilter: 'blur(40px) saturate(180%)',
          }}
        >
          <div className="p-4">
            <div className="flex items-center justify-between mb-3">
              <h3 className="text-sm font-semibold text-gray-800">Layers</h3>
              <button
                onClick={() => setIsOpen(false)}
                className="text-gray-500 hover:text-gray-700"
              >
                ×
              </button>
            </div>

            <div className="space-y-1">
              {sortedElements.map((element, index) => (
                <div
                  key={element.id}
                  className={`p-2 rounded-lg border transition-all duration-200 cursor-pointer ${
                    selectedElement === element.id
                      ? 'border-blue-500 bg-blue-50/50'
                      : 'border-transparent hover:bg-white/20'
                  }`}
                  onClick={() => onSelectElement(element.id)}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2 flex-1 min-w-0">
                      {getElementIcon(element)}
                      <span className="text-xs text-gray-700 truncate">
                        {getElementName(element)}
                      </span>
                    </div>

                    <div className="flex items-center space-x-1">
                      {/* Visibility Toggle */}
                      <button
                        onClick={(e) => {
                          e.stopPropagation()
                          toggleVisibility(element.id)
                        }}
                        className="p-1 hover:bg-white/20 rounded"
                        title={element.data.visible === false ? 'Show' : 'Hide'}
                      >
                        {element.data.visible === false ? (
                          <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
                          </svg>
                        ) : (
                          <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                          </svg>
                        )}
                      </button>

                      {/* Layer Controls */}
                      <div className="flex flex-col">
                        <button
                          onClick={(e) => {
                            e.stopPropagation()
                            moveUp(element.id)
                          }}
                          className="p-0.5 hover:bg-white/20 rounded text-xs"
                          title="Move Up"
                          disabled={index === 0}
                        >
                          ▲
                        </button>
                        <button
                          onClick={(e) => {
                            e.stopPropagation()
                            moveDown(element.id)
                          }}
                          className="p-0.5 hover:bg-white/20 rounded text-xs"
                          title="Move Down"
                          disabled={index === sortedElements.length - 1}
                        >
                          ▼
                        </button>
                      </div>

                      {/* Delete Button */}
                      <button
                        onClick={(e) => {
                          e.stopPropagation()
                          onDeleteElement(element.id)
                        }}
                        className="p-1 hover:bg-red-100 hover:text-red-600 rounded"
                        title="Delete"
                      >
                        <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                        </svg>
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Quick Actions */}
            {selectedElement && (
              <div className="mt-3 pt-3 border-t border-white/20">
                <div className="flex space-x-1">
                  <button
                    onClick={() => moveToFront(selectedElement)}
                    className="flex-1 px-2 py-1 text-xs bg-white/20 hover:bg-white/30 rounded transition-colors"
                  >
                    To Front
                  </button>
                  <button
                    onClick={() => moveToBack(selectedElement)}
                    className="flex-1 px-2 py-1 text-xs bg-white/20 hover:bg-white/30 rounded transition-colors"
                  >
                    To Back
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      )}
    </>
  )
}

export default LayerPanel
