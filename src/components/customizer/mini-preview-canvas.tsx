'use client'

import React, { useState, useRef, useEffect, useCallback, useMemo } from 'react'
import { ArrowsRightLeftIcon, ChevronLeftIcon, ChevronRightIcon } from '@heroicons/react/24/outline'
import { DesignElement } from './unified-customizer-interface'
import dynamic from 'next/dynamic'

// Dynamically import 3D viewer to avoid SSR issues
const Simple3DViewer = dynamic(() => import('@/components/Simple3DViewer'), {
  ssr: false,
  loading: () => (
    <div className="flex items-center justify-center w-full h-full">
      <div className="text-xs text-gray-500">Loading 3D...</div>
    </div>
  )
})

// Memoized constants to prevent re-creation on every render
const EMPTY_MESH_VISIBILITY = {}
const MINI_PREVIEW_INTERACTION_CONFIG = {
  enableHoverEffects: false,
  enableTransformHandles: false,
  enableVisualSelectors: false,
  enableSurfaceDragging: false,
  debugMode: false
}

// Memoized no-op functions to prevent re-creation
const NOOP = () => {}
const NOOP_UPDATE = () => {}

// Callback for texture engine ready in mini preview
const handleMiniTextureEngineReady = (engine: any) => {
  console.log('🔄 Mini preview texture engine ready')
}

interface PrintFile {
  id?: string
  filename: string
  url: string
  name?: string
}

interface MiniPreviewCanvasProps {
  // View configuration
  currentView: '3d' | 'printfile'
  onSwapViews: () => void
  
  // Print file data
  printFiles: PrintFile[]
  selectedPrintFileIndex: number
  onPrintFileSelect: (index: number) => void
  
  // 3D data
  glbFiles?: any[]
  selectedGLBIndex?: number
  onGLBSelect?: (index: number) => void
  
  // Design elements
  designElements: DesignElement[]

  // Synchronization props
  selectedDesignArea?: any
  textureEngine?: any

  // Utility functions
  getFileUrl: (bucket: string, path: string) => string
  getGLBUrl?: (glbFile: any) => string

  // Size and positioning
  width?: number
  height?: number
}

export default function MiniPreviewCanvas({
  currentView,
  onSwapViews,
  printFiles,
  selectedPrintFileIndex,
  onPrintFileSelect,
  glbFiles = [],
  selectedGLBIndex = 0,
  onGLBSelect,
  designElements,
  selectedDesignArea,
  textureEngine,
  getFileUrl,
  getGLBUrl,
  width = 280,
  height = 320
}: MiniPreviewCanvasProps) {
  const sliderRef = useRef<HTMLDivElement>(null)

  // Drag functionality state
  const [position, setPosition] = useState({ x: 20, y: 200 }) // Default left middle position
  const [isDragging, setIsDragging] = useState(false)
  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 })
  const containerRef = useRef<HTMLDivElement>(null)

  // Header height to avoid positioning underneath it
  const HEADER_HEIGHT = 120 // Approximate header height including banner

  // Memoize current files to prevent unnecessary recalculations
  const currentPrintFile = useMemo(() =>
    printFiles && printFiles.length > 0 ? printFiles[selectedPrintFileIndex] : null,
    [printFiles, selectedPrintFileIndex]
  )
  const currentGLBFile = useMemo(() =>
    glbFiles && glbFiles.length > 0 ? glbFiles[selectedGLBIndex] : null,
    [glbFiles, selectedGLBIndex]
  )

  const navigatePrintFile = useCallback((direction: 'prev' | 'next') => {
    if (!printFiles || printFiles.length <= 1) return

    let newIndex = selectedPrintFileIndex
    if (direction === 'prev') {
      newIndex = selectedPrintFileIndex > 0 ? selectedPrintFileIndex - 1 : printFiles.length - 1
    } else {
      newIndex = selectedPrintFileIndex < printFiles.length - 1 ? selectedPrintFileIndex + 1 : 0
    }
    onPrintFileSelect(newIndex)
  }, [printFiles, selectedPrintFileIndex, onPrintFileSelect])

  const navigateGLB = useCallback((direction: 'prev' | 'next') => {
    if (!glbFiles || glbFiles.length <= 1 || !onGLBSelect) return

    let newIndex = selectedGLBIndex
    if (direction === 'prev') {
      newIndex = selectedGLBIndex > 0 ? selectedGLBIndex - 1 : glbFiles.length - 1
    } else {
      newIndex = selectedGLBIndex < glbFiles.length - 1 ? selectedGLBIndex + 1 : 0
    }
    onGLBSelect(newIndex)
  }, [glbFiles, selectedGLBIndex, onGLBSelect])

  const scrollToFile = (index: number) => {
    if (sliderRef.current) {
      const thumbnailWidth = 60 // Width of each thumbnail + margin
      sliderRef.current.scrollTo({
        left: index * thumbnailWidth - (sliderRef.current.clientWidth / 2) + (thumbnailWidth / 2),
        behavior: 'smooth'
      })
    }
  }

  // Drag functionality
  const handleMouseDown = (e: React.MouseEvent) => {
    if (containerRef.current) {
      const rect = containerRef.current.getBoundingClientRect()
      // Calculate offset from the click point to the element's top-left corner
      setDragOffset({
        x: e.clientX - rect.left,
        y: e.clientY - rect.top
      })
      setIsDragging(true)
      e.preventDefault() // Prevent text selection during drag
    }
  }

  const handleMouseMove = (e: MouseEvent) => {
    if (isDragging && containerRef.current) {
      const newX = e.clientX - dragOffset.x
      const newY = e.clientY - dragOffset.y

      // Boundary checking to keep within viewport and below header
      const maxX = window.innerWidth - width
      const maxY = window.innerHeight - height
      const minY = HEADER_HEIGHT // Ensure it stays below the header

      setPosition({
        x: Math.max(0, Math.min(newX, maxX)),
        y: Math.max(minY, Math.min(newY, maxY))
      })
    }
  }

  const handleMouseUp = () => {
    setIsDragging(false)
  }

  // Set initial position to right middle on mount
  useEffect(() => {
    const setInitialPosition = () => {
      const viewportHeight = window.innerHeight
      const viewportWidth = window.innerWidth
      const middleY = Math.max(HEADER_HEIGHT, (viewportHeight - height) / 2)
      const rightX = viewportWidth - width - 20 // 20px margin from right edge
      setPosition({ x: rightX, y: middleY })
    }

    // Set position on mount and window resize
    setInitialPosition()
    window.addEventListener('resize', setInitialPosition)

    return () => {
      window.removeEventListener('resize', setInitialPosition)
    }
  }, [height, width])

  // Add global mouse event listeners for dragging
  useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove)
      document.addEventListener('mouseup', handleMouseUp)

      return () => {
        document.removeEventListener('mousemove', handleMouseMove)
        document.removeEventListener('mouseup', handleMouseUp)
      }
    }
  }, [isDragging, dragOffset.x, dragOffset.y, width, height])

  return (
    <>
      {/* CSS Animations for smooth swipe effect */}
      <style jsx>{`
        @keyframes slideInFromRight {
          0% {
            transform: translateX(20px);
            opacity: 0;
          }
          100% {
            transform: translateX(0);
            opacity: 1;
          }
        }

        @keyframes slideInFromLeft {
          0% {
            transform: translateX(-20px);
            opacity: 0;
          }
          100% {
            transform: translateX(0);
            opacity: 1;
          }
        }
      `}</style>

      <div
        ref={containerRef}
        className={`bg-white/10 backdrop-blur-sm rounded-lg border border-white/20 shadow-lg overflow-hidden transition-all duration-200 ${
          isDragging ? 'shadow-2xl scale-105 opacity-90' : 'shadow-lg'
        }`}
        style={{
          width,
          height,
          position: 'fixed',
          left: position.x,
          top: position.y,
          zIndex: isDragging ? 50 : 30,
          cursor: isDragging ? 'grabbing' : 'default'
        }}
      >
      {/* Header with swap button - Draggable area */}
      <div
        className="flex items-center justify-between p-3 border-b border-white/20 cursor-grab active:cursor-grabbing select-none"
        onMouseDown={handleMouseDown}
      >
        <div className="flex items-center space-x-2">
          <h3 className="text-sm font-medium text-gray-700">
            {currentView === 'printfile' ? 'Print Files' : '3D Model'}
          </h3>
          <div className="text-xs text-gray-500">
            {currentView === 'printfile'
              ? `${selectedPrintFileIndex + 1}/${printFiles.length}`
              : `${selectedGLBIndex + 1}/${glbFiles.length}`
            }
          </div>
        </div>
        
        <button
          onClick={onSwapViews}
          onMouseDown={(e) => e.stopPropagation()}
          className="p-2 rounded-md transition-all duration-200 bg-white/20 text-gray-600 hover:bg-blue-500 hover:text-white hover:shadow-md hover:scale-110"
          title="Swap views"
        >
          <ArrowsRightLeftIcon className="w-4 h-4" />
        </button>
      </div>

      {/* Preview Area - Dynamic sizing based on content */}
      <div className="relative flex-1 flex items-center justify-center p-3">
        {currentView === 'printfile' ? (
          <div className="w-full h-32 bg-gray-100/50 rounded-lg flex items-center justify-center overflow-hidden relative">
            {currentPrintFile ? (
              <>
                {/* Background print file with smooth swipe animation */}
                <img
                  key={`print-file-${selectedPrintFileIndex}`} // Key for React to trigger transitions
                  src={getFileUrl('product-files', currentPrintFile.url)}
                  alt={currentPrintFile.filename}
                  className="max-w-full max-h-full object-contain transition-all duration-300 ease-out"
                  style={{
                    animation: 'slideInFromRight 250ms ease-out'
                  }}
                  onError={(e) => {
                    console.error('Print file failed to load:', currentPrintFile.url)
                  }}
                />

                {/* Design elements overlay */}
                {designElements.length > 0 && (
                  <div className="absolute inset-0 pointer-events-none">
                    {designElements.map((element) => {
                      // Scale design elements to fit mini preview
                      const scale = 0.3 // Adjust based on mini preview size
                      const scaledStyle = {
                        position: 'absolute' as const,
                        left: element.x * scale,
                        top: element.y * scale,
                        width: element.width * scale,
                        height: element.height * scale,
                        transform: `rotate(${element.rotation}deg)`,
                        zIndex: element.zIndex,
                        opacity: 0.8 // Slightly transparent for overlay effect
                      }

                      switch (element.type) {
                        case 'text':
                          return (
                            <div
                              key={element.id}
                              style={{
                                ...scaledStyle,
                                fontSize: (element.data.fontSize || 16) * scale,
                                color: element.data.color || '#000000',
                                fontFamily: element.data.fontFamily || 'Arial',
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                userSelect: 'none',
                                fontWeight: element.data.fontWeight || 'normal'
                              }}
                            >
                              {element.data.text}
                            </div>
                          )

                        case 'image':
                          return (
                            <img
                              key={element.id}
                              src={element.data.src}
                              alt={element.data.filename || 'Design element'}
                              style={{
                                ...scaledStyle,
                                objectFit: 'contain'
                              }}
                              draggable={false}
                            />
                          )

                        case 'shape':
                          return (
                            <div
                              key={element.id}
                              style={{
                                ...scaledStyle,
                                backgroundColor: element.data.fill || '#000000',
                                borderRadius: element.data.rx || 0
                              }}
                            />
                          )

                        default:
                          return null
                      }
                    })}
                  </div>
                )}
              </>
            ) : (
              <div className="text-xs text-gray-500">No print file</div>
            )}
          </div>
        ) : (
          <div className="w-full h-full bg-gray-100/50 rounded-lg overflow-hidden relative">
            {currentGLBFile && getGLBUrl ? (
              <div className="w-full h-full flex items-center justify-center p-2">
                <div
                  className="bg-white rounded shadow-sm overflow-hidden"
                  style={{
                    width: '220px',
                    height: '160px',
                    maxWidth: '100%',
                    maxHeight: '100%'
                  }}
                >
                  <Simple3DViewer
                    glbUrl={getGLBUrl(currentGLBFile)}
                    width={220}
                    height={160}
                    responsive={false}
                    showWireframe={false}
                    meshVisibility={EMPTY_MESH_VISIBILITY}
                    onMeshesLoaded={NOOP}
                    onDesignAreasExtracted={NOOP}
                    onTextureEngineReady={handleMiniTextureEngineReady}
                    onMeshClick={NOOP}
                    editingTextId={null}
                    designElements={designElements}
                    onUpdateElement={NOOP_UPDATE}
                    onFinishTextEdit={NOOP}
                    selectedElement={null}
                    onSelectElement={NOOP}
                    onStartTextEdit={NOOP}
                    selectedDesignArea={selectedDesignArea}
                    enable3DInteraction={true}
                    interactionConfig={MINI_PREVIEW_INTERACTION_CONFIG}
                    onInteractionReady={NOOP}
                    onCanvasUpdate={NOOP}
                  />
                </div>
              </div>
            ) : (
              <div className="w-full h-full bg-gradient-to-br from-blue-100 to-purple-100 rounded flex items-center justify-center">
                <div className="text-xs text-gray-600 font-medium">No 3D Model</div>
              </div>
            )}
          </div>
        )}

        {/* Navigation arrows for print files */}
        {currentView === 'printfile' && printFiles.length > 1 && (
          <>
            <button
              onClick={() => navigatePrintFile('prev')}
              className="absolute left-1 top-1/2 -translate-y-1/2 p-1 bg-black/20 hover:bg-black/40 text-white rounded-full transition-all"
            >
              <ChevronLeftIcon className="w-3 h-3" />
            </button>
            <button
              onClick={() => navigatePrintFile('next')}
              className="absolute right-1 top-1/2 -translate-y-1/2 p-1 bg-black/20 hover:bg-black/40 text-white rounded-full transition-all"
            >
              <ChevronRightIcon className="w-3 h-3" />
            </button>
          </>
        )}

        {/* Navigation arrows for 3D models */}
        {currentView === '3d' && glbFiles.length > 1 && (
          <>
            <button
              onClick={() => navigateGLB('prev')}
              className="absolute left-1 top-1/2 -translate-y-1/2 p-1 bg-black/20 hover:bg-black/40 text-white rounded-full transition-all"
            >
              <ChevronLeftIcon className="w-3 h-3" />
            </button>
            <button
              onClick={() => navigateGLB('next')}
              className="absolute right-1 top-1/2 -translate-y-1/2 p-1 bg-black/20 hover:bg-black/40 text-white rounded-full transition-all"
            >
              <ChevronRightIcon className="w-3 h-3" />
            </button>
          </>
        )}
      </div>

      {/* Compact Slider for Print Files */}
      {currentView === 'printfile' && printFiles.length > 1 && (
        <div className="p-2 border-t border-white/20">
          <div
            ref={sliderRef}
            className="flex space-x-1 overflow-x-auto scrollbar-none justify-center"
            style={{ scrollbarWidth: 'none', msOverflowStyle: 'none' }}
          >
            {printFiles.map((file, index) => (
              <button
                key={index}
                onClick={() => {
                  onPrintFileSelect(index)
                  scrollToFile(index)
                }}
                className={`flex-shrink-0 w-12 h-12 rounded border-2 transition-all ${
                  selectedPrintFileIndex === index
                    ? 'border-blue-500 shadow-md scale-110'
                    : 'border-white/30 hover:border-white/50'
                }`}
              >
                <img
                  src={getFileUrl('product-files', file.url)}
                  alt={file.filename}
                  className="w-full h-full object-cover rounded"
                  onError={(e) => {
                    const target = e.target as HTMLImageElement
                    target.style.display = 'none'
                  }}
                />
              </button>
            ))}
          </div>

          {/* File indicator dots */}
          <div className="flex justify-center mt-2 space-x-1">
            {printFiles.map((_, index) => (
              <button
                key={index}
                onClick={() => onPrintFileSelect(index)}
                className={`w-1.5 h-1.5 rounded-full transition-all ${
                  selectedPrintFileIndex === index
                    ? 'bg-blue-500'
                    : 'bg-gray-300 hover:bg-gray-400'
                }`}
              />
            ))}
          </div>
        </div>
      )}

      {/* Compact Slider for 3D Models */}
      {currentView === '3d' && glbFiles.length > 1 && (
        <div className="p-2 border-t border-white/20">
          <div
            className="flex space-x-1 overflow-x-auto scrollbar-none justify-center"
            style={{ scrollbarWidth: 'none', msOverflowStyle: 'none' }}
          >
            {glbFiles.map((glbFile, index) => (
              <button
                key={index}
                onClick={() => {
                  if (onGLBSelect) {
                    onGLBSelect(index)
                  }
                }}
                className={`flex-shrink-0 w-12 h-12 rounded border-2 transition-all flex items-center justify-center ${
                  selectedGLBIndex === index
                    ? 'border-purple-500 shadow-md scale-110 bg-purple-500/20'
                    : 'border-white/30 hover:border-white/50 bg-white/10'
                }`}
                title={glbFile.name || `3D Model ${index + 1}`}
              >
                {/* 3D Model Icon */}
                <svg
                  className={`w-6 h-6 ${
                    selectedGLBIndex === index ? 'text-purple-300' : 'text-gray-400'
                  }`}
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="m21 7.5-9-5.25L3 7.5m18 0-9 5.25m9-5.25v9l-9 5.25M3 7.5l9 5.25M3 7.5v9l9 5.25m0-9v9"
                  />
                </svg>
              </button>
            ))}
          </div>

          {/* Model indicator dots */}
          <div className="flex justify-center mt-2 space-x-1">
            {glbFiles.map((_, index) => (
              <button
                key={index}
                onClick={() => {
                  if (onGLBSelect) {
                    onGLBSelect(index)
                  }
                }}
                className={`w-1.5 h-1.5 rounded-full transition-all ${
                  selectedGLBIndex === index
                    ? 'bg-purple-500'
                    : 'bg-gray-300 hover:bg-gray-400'
                }`}
              />
            ))}
          </div>
        </div>
      )}
    </div>
    </>
  )
}
