'use client'

interface ToolsPanelProps {
  onAddText: () => void
  onAddImage: () => void
  selectedColor: string
}

export default function ToolsPanel({
  onAddText,
  onAddImage,
  selectedColor
}: ToolsPanelProps) {
  const tools = [
    {
      id: 'text',
      label: 'Add Text',
      icon: (
        <svg className="w-4 h-4 md:w-5 md:h-5" fill="currentColor" viewBox="0 0 24 24">
          <path d="M5 4v3h5.5v12h3V7H19V4z"/>
        </svg>
      ),
      onClick: onAddText
    },
    {
      id: 'image',
      label: 'Add Photo',
      icon: (
        <svg className="w-4 h-4 md:w-5 md:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
        </svg>
      ),
      onClick: onAddImage
    }
  ]

  // Shapes removed - using 3D interaction system instead

  return (
    <div
      className="backdrop-blur-3xl border border-white/50 rounded-2xl shadow-2xl px-3 md:px-6 py-2 md:py-4"
      style={{
        background: 'linear-gradient(135deg, rgba(255, 255, 255, 0.4), rgba(255, 255, 255, 0.2))',
        backdropFilter: 'blur(40px) saturate(180%)',
        boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(255, 255, 255, 0.5) inset, 0 0 0 2px rgba(255, 255, 255, 0.1) inset'
      }}
    >
      {/* Mobile: Two-row layout */}
      <div className="md:hidden flex flex-col space-y-2 items-center">
        {/* Row 1: Main Tools - Compact buttons */}
        <div className="flex items-center justify-center space-x-2">
          {tools.map((tool) => (
            <button key={tool.id} onClick={tool.onClick} className="px-2 py-2 backdrop-blur-2xl border border-white/50 rounded-xl shadow-xl hover:shadow-2xl transition-all duration-200 flex items-center justify-center text-gray-700 hover:text-gray-900 hover:scale-105" style={{ background: 'linear-gradient(135deg, rgba(255, 255, 255, 0.4), rgba(255, 255, 255, 0.2))', backdropFilter: 'blur(30px) saturate(180%)', boxShadow: '0 8px 32px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(255, 255, 255, 0.5) inset, 0 0 0 2px rgba(255, 255, 255, 0.1) inset' }} title={tool.label}>
              <span className="w-4 h-4 flex items-center justify-center">{tool.icon}</span>
            </button>
          ))}
          
          {/* Shapes removed - using 3D interaction system instead */}
        </div>
        
        {/* Row 2: Undo/Redo centered */}
        <div className="flex items-center justify-center space-x-2">
          <button className="px-3 py-3 backdrop-blur-2xl border border-white/50 rounded-xl shadow-xl hover:shadow-2xl transition-all duration-200 flex items-center justify-center text-gray-700 hover:text-gray-900 hover:scale-105" style={{ background: 'linear-gradient(135deg, rgba(255, 255, 255, 0.4), rgba(255, 255, 255, 0.2))', backdropFilter: 'blur(30px) saturate(180%)', boxShadow: '0 8px 32px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(255, 255, 255, 0.5) inset, 0 0 0 2px rgba(255, 255, 255, 0.1) inset' }} title="Undo">
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 10h10a8 8 0 018 8v2M3 10l6 6m-6-6l6-6" />
            </svg>
          </button>
          
          <button className="px-3 py-3 backdrop-blur-2xl border border-white/50 rounded-xl shadow-xl hover:shadow-2xl transition-all duration-200 flex items-center justify-center text-gray-700 hover:text-gray-900 hover:scale-105" style={{ background: 'linear-gradient(135deg, rgba(255, 255, 255, 0.4), rgba(255, 255, 255, 0.2))', backdropFilter: 'blur(30px) saturate(180%)', boxShadow: '0 8px 32px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(255, 255, 255, 0.5) inset, 0 0 0 2px rgba(255, 255, 255, 0.1) inset' }} title="Redo">
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 10H11a8 8 0 00-8 8v2m18-10l-6-6m6 6l-6 6" />
            </svg>
          </button>
        </div>
      </div>

      {/* Desktop: Original single-row layout */}
      <div className="hidden md:flex items-center space-x-4">
        {/* Main Tools */}
        {tools.map((tool) => (
          <button key={tool.id} onClick={tool.onClick} className="group relative px-4 py-3 backdrop-blur-2xl border border-white/50 rounded-xl shadow-xl hover:shadow-2xl transition-all duration-200 flex items-center space-x-2 text-gray-700 hover:text-gray-900 hover:scale-105" style={{ background: 'linear-gradient(135deg, rgba(255, 255, 255, 0.4), rgba(255, 255, 255, 0.2))', backdropFilter: 'blur(30px) saturate(180%)', boxShadow: '0 8px 32px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(255, 255, 255, 0.5) inset, 0 0 0 2px rgba(255, 255, 255, 0.1) inset' }} title={tool.label}>
              <span className="w-5 h-5 flex items-center justify-center">{tool.icon}</span>
              <span className="text-sm font-medium">{tool.label}</span>
            </button>
          ))}

          {/* Shapes removed - using 3D interaction system instead */}

          {/* Divider */}
          <div className="h-8 w-px bg-gradient-to-b from-transparent via-gray-300 to-transparent" />

          {/* Undo/Redo */}
          <button
            className="px-3 py-3 backdrop-blur-2xl border border-white/50 rounded-xl shadow-xl hover:shadow-2xl transition-all duration-200 flex items-center justify-center text-gray-700 hover:text-gray-900 hover:scale-105"
            style={{
              background: 'linear-gradient(135deg, rgba(255, 255, 255, 0.4), rgba(255, 255, 255, 0.2))',
              backdropFilter: 'blur(30px) saturate(180%)',
              boxShadow: '0 8px 32px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(255, 255, 255, 0.5) inset, 0 0 0 2px rgba(255, 255, 255, 0.1) inset'
            }}
            title="Undo"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 10h10a8 8 0 018 8v2M3 10l6 6m-6-6l6-6" />
            </svg>
          </button>

          <button
            className="px-3 py-3 backdrop-blur-2xl border border-white/50 rounded-xl shadow-xl hover:shadow-2xl transition-all duration-200 flex items-center justify-center text-gray-700 hover:text-gray-900 hover:scale-105"
            style={{
              background: 'linear-gradient(135deg, rgba(255, 255, 255, 0.4), rgba(255, 255, 255, 0.2))',
              backdropFilter: 'blur(30px) saturate(180%)',
              boxShadow: '0 8px 32px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(255, 255, 255, 0.5) inset, 0 0 0 2px rgba(255, 255, 255, 0.1) inset'
            }}
            title="Redo"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 10H11a8 8 0 00-8 8v2m18-10l-6-6m6 6l-6 6" />
            </svg>
          </button>
        </div>
      </div>
  )
}
