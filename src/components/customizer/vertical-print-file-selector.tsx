'use client'

import React from 'react'
import { DocumentIcon } from '@heroicons/react/24/outline'

// Helper function to format print file names
const formatPrintFileName = (filename: string): string => {
  if (!filename) return ''

  // Extract just the filename from the full path (handle both forward and back slashes)
  const actualFilename = filename.split(/[/\\]/).pop() || filename

  // Remove file extension
  const nameWithoutExt = actualFilename.replace(/\.(png|jpg|jpeg|pdf|svg)$/i, '')

  // Replace underscores and hyphens with spaces
  const withSpaces = nameWithoutExt.replace(/[_-]/g, ' ')

  // Convert to lowercase first, then capitalize each word
  const capitalized = withSpaces.toLowerCase().replace(/\b\w/g, letter => letter.toUpperCase())

  return capitalized
}

interface PrintFile {
  id?: string
  filename: string
  url: string
  [key: string]: any
}

interface VerticalPrintFileSelectorProps {
  printFiles: PrintFile[]
  selectedIndex: number
  onPrintFileSelect: (index: number) => void
  getFileUrl: (bucket: string, path: string) => string
}

export default function VerticalPrintFileSelector({
  printFiles,
  selectedIndex,
  onPrintFileSelect,
  getFileUrl
}: VerticalPrintFileSelectorProps) {

  if (!printFiles || printFiles.length === 0) {
    return (
      <div className="w-full h-full flex items-center justify-center">
        <div className="bg-white/10 backdrop-blur-md rounded-2xl border border-white/20 shadow-xl p-6 text-center">
          <DocumentIcon className="w-8 h-8 text-gray-400 mx-auto mb-2" />
          <div className="text-sm text-gray-500">No print files</div>
        </div>
      </div>
    )
  }

  return (
    <div className="fixed left-0 top-20 bottom-0 w-48 z-50 p-4">
      {/* Glass morphism container */}
      <div className="bg-white/10 backdrop-blur-md rounded-2xl border border-white/20 shadow-xl h-full flex flex-col">

        {/* Header */}
        <div className="px-4 py-3 border-b border-white/20 bg-gradient-to-r from-white/5 to-white/10 flex-shrink-0">
          <div className="flex items-center space-x-2">
            <DocumentIcon className="w-5 h-5 text-gray-600" />
            <h3 className="text-sm font-semibold text-gray-700">Print Files</h3>
            <div className="ml-auto bg-white/20 rounded-full px-2 py-1">
              <span className="text-xs font-medium text-gray-600">{printFiles.length}</span>
            </div>
          </div>
        </div>

        {/* Print File Buttons - Responsive Grid */}
        <div className="flex-1 p-3 overflow-hidden">
          <div className="h-full grid gap-2" style={{
            gridTemplateRows: `repeat(${printFiles.length}, 1fr)`
          }}>
            {printFiles.map((printFile, index) => (
              <button
                key={index}
                onClick={() => onPrintFileSelect(index)}
                className={`w-full rounded-xl transition-all duration-300 transform hover:scale-105 ${
                  selectedIndex === index
                    ? 'bg-gradient-to-r from-green-500/80 to-blue-500/80 text-white shadow-lg shadow-green-500/25 border border-green-400/50'
                    : 'bg-white/10 hover:bg-white/20 text-gray-700 border border-white/20 hover:border-white/30'
                }`}
              >
                <div className="flex items-center space-x-2 px-2 py-1">
                  {/* Thumbnail or Icon */}
                  <div className={`${
                    selectedIndex === index
                      ? 'bg-white/20 text-white'
                      : 'bg-gray-200/50 text-gray-600'
                  } rounded-lg p-1 flex-shrink-0 relative overflow-hidden`}>
                    <img
                      src={getFileUrl('product-files', printFile.url)}
                      alt={printFile.filename}
                      className="w-8 h-8 object-cover rounded"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement
                        target.style.display = 'none'
                        const parent = target.parentElement
                        if (parent) {
                          const icon = document.createElement('div')
                          icon.innerHTML = '<svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path></svg>'
                          parent.appendChild(icon)
                        }
                      }}
                    />
                  </div>

                  {/* Content */}
                  <div className="flex-1 text-left min-w-0">
                    <div className="text-xs font-semibold leading-tight truncate">
                      {formatPrintFileName(printFile.filename) || `File ${index + 1}`}
                    </div>
                    <div className={`text-xs opacity-75 ${
                      selectedIndex === index ? 'text-green-100' : 'text-gray-500'
                    }`}>
                      {index + 1}/{printFiles.length}
                    </div>
                  </div>

                  {/* Active indicator */}
                  {selectedIndex === index && (
                    <div className="w-2 h-2 bg-white rounded-full flex-shrink-0 animate-pulse" />
                  )}
                </div>
              </button>
            ))}
          </div>
        </div>

      </div>
    </div>
  )
}
