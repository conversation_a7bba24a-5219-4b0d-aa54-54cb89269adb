'use client'

import React from 'react'
import { CubeIcon } from '@heroicons/react/24/outline'

interface GLBFile {
  id?: string
  name?: string
  url: string
  thumbnail?: string
  isPublicAsset?: boolean
}

interface VerticalGLBSelectorProps {
  glbFiles: GLBFile[]
  selectedIndex: number
  onGLBSelect: (index: number) => void
  getGLBUrl: (glbFile: GLBFile) => string
}

export default function VerticalGLBSelector({
  glbFiles,
  selectedIndex,
  onGLBSelect,
  getGLBUrl
}: VerticalGLBSelectorProps) {

  if (!glbFiles || glbFiles.length === 0) {
    return (
      <div className="w-full h-full flex items-center justify-center">
        <div className="bg-white/10 backdrop-blur-md rounded-2xl border border-white/20 shadow-xl p-6 text-center">
          <CubeIcon className="w-8 h-8 text-gray-400 mx-auto mb-2" />
          <div className="text-sm text-gray-500">No 3D models</div>
        </div>
      </div>
    )
  }

  // Calculate responsive sizing based on number of GLB files
  const getButtonHeight = () => {
    if (glbFiles.length <= 2) return 'h-32' // Large buttons for 1-2 files
    if (glbFiles.length <= 3) return 'h-24' // Medium buttons for 3 files
    if (glbFiles.length <= 4) return 'h-20' // Smaller buttons for 4 files
    return 'h-16' // Compact buttons for 5+ files
  }

  const getIconSize = () => {
    if (glbFiles.length <= 2) return 'w-8 h-8'
    if (glbFiles.length <= 3) return 'w-6 h-6'
    return 'w-5 h-5'
  }

  const getTextSize = () => {
    if (glbFiles.length <= 2) return 'text-sm'
    if (glbFiles.length <= 3) return 'text-xs'
    return 'text-xs'
  }

  const buttonHeight = getButtonHeight()
  const iconSize = getIconSize()
  const textSize = getTextSize()

  return (
    <div className="w-full h-full flex flex-col">
      {/* Glass morphism container */}
      <div className="bg-white/10 backdrop-blur-md rounded-2xl border border-white/20 shadow-xl overflow-hidden h-full">

        {/* Header */}
        <div className="px-4 py-3 border-b border-white/20 bg-gradient-to-r from-white/5 to-white/10">
          <div className="flex items-center space-x-2">
            <CubeIcon className="w-5 h-5 text-gray-600" />
            <h3 className="text-sm font-semibold text-gray-700">3D Views</h3>
            <div className="ml-auto bg-white/20 rounded-full px-2 py-1">
              <span className="text-xs font-medium text-gray-600">{glbFiles.length}</span>
            </div>
          </div>
        </div>

        {/* GLB Buttons - Responsive Grid */}
        <div className="flex-1 p-3">
          <div className="h-full flex flex-col space-y-2">
            {glbFiles.map((glbFile, index) => (
              <button
                key={index}
                onClick={() => onGLBSelect(index)}
                className={`${buttonHeight} w-full rounded-xl transition-all duration-300 transform hover:scale-105 ${
                  selectedIndex === index
                    ? 'bg-gradient-to-r from-blue-500/80 to-purple-500/80 text-white shadow-lg shadow-blue-500/25 border border-blue-400/50'
                    : 'bg-white/10 hover:bg-white/20 text-gray-700 border border-white/20 hover:border-white/30'
                }`}
              >
                <div className="h-full flex items-center justify-center space-x-3 px-3">
                  {/* 3D Icon */}
                  <div className={`${
                    selectedIndex === index
                      ? 'bg-white/20 text-white'
                      : 'bg-gray-200/50 text-gray-600'
                  } rounded-lg p-2 flex-shrink-0`}>
                    <CubeIcon className={iconSize} />
                  </div>

                  {/* Content */}
                  <div className="flex-1 text-left min-w-0">
                    <div className={`${textSize} font-semibold truncate`}>
                      {glbFile.name?.replace('.glb', '') || `View ${index + 1}`}
                    </div>
                    <div className={`text-xs opacity-75 ${
                      selectedIndex === index ? 'text-blue-100' : 'text-gray-500'
                    }`}>
                      {index + 1} of {glbFiles.length}
                    </div>
                  </div>

                  {/* Active indicator */}
                  {selectedIndex === index && (
                    <div className="w-2 h-2 bg-white rounded-full flex-shrink-0 animate-pulse" />
                  )}
                </div>
              </button>
            ))}
          </div>
        </div>

        {/* Footer with current selection indicator */}
        <div className="px-4 py-2 border-t border-white/20 bg-gradient-to-r from-white/5 to-white/10">
          <div className="flex items-center justify-between">
            <span className="text-xs text-gray-600">Active View</span>
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse" />
              <span className="text-xs font-medium text-gray-700">
                {glbFiles[selectedIndex]?.name?.replace('.glb', '') || `View ${selectedIndex + 1}`}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
