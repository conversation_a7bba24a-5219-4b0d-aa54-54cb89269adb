'use client'

interface CustomizerHeaderProps {
  productName: string
  isIframe?: boolean
}

export default function CustomizerHeader({ productName, isIframe }: CustomizerHeaderProps) {
  return (
    <header
      className="relative z-50 backdrop-blur-3xl border-b border-white/40 shadow-2xl"
      style={{
        background: 'linear-gradient(135deg, rgba(255, 255, 255, 0.25), rgba(255, 255, 255, 0.1))',
        backdropFilter: 'blur(40px) saturate(180%)',
        boxShadow: '0 8px 32px rgba(0, 0, 0, 0.12), 0 0 0 1px rgba(255, 255, 255, 0.3) inset, 0 0 0 2px rgba(255, 255, 255, 0.1) inset'
      }}
    >
      <div className="px-3 md:px-6 py-1.5 md:py-4 flex items-center justify-between h-12 md:h-auto">
        {/* BIYPOD Logo */}
        <div className="flex items-center space-x-2 md:space-x-3">
          <div className="w-5 h-5 md:w-8 md:h-8 bg-gradient-to-br from-blue-600 to-purple-600 rounded-md md:rounded-lg flex items-center justify-center">
            <span className="text-white font-bold text-xs md:text-sm">B</span>
          </div>
          <div>
            <h1 className="text-xs md:text-lg font-semibold text-gray-800">BIYPOD</h1>
            <p className="text-xs text-gray-600 hidden md:block">Customizer</p>
          </div>
        </div>

        {/* Product Info */}
        <div className="flex-1 text-center">
          <h2 className="text-xs md:text-lg font-medium text-gray-800 truncate max-w-md mx-auto">
            <span className="hidden md:inline">Customize </span>{productName}
          </h2>
        </div>

        {/* Actions */}
        <div className="flex items-center space-x-1 md:space-x-3">
          {!isIframe && (
            <button className="px-2 md:px-4 py-1 md:py-2 text-xs md:text-sm text-gray-600 hover:text-gray-800 transition-colors hidden md:block">
              Save Design
            </button>
          )}

          <button className="p-0.5 md:p-2 text-gray-600 hover:text-gray-800 transition-colors">
            <svg className="w-3 h-3 md:w-5 md:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
            </svg>
          </button>
        </div>
      </div>
    </header>
  )
}
