'use client'

import { useState } from 'react'

interface ColorPickerProps {
  selectedColor: string
  onColorChange: (color: string) => void
}

export default function ColorPicker({ selectedColor, onColorChange }: ColorPickerProps) {
  const [isOpen, setIsOpen] = useState(false)

  const presetColors = [
    '#000000', '#ffffff', '#ff0000', '#00ff00', '#0000ff',
    '#ffff00', '#ff00ff', '#00ffff', '#ffa500', '#800080',
    '#ffc0cb', '#a52a2a', '#808080', '#000080', '#008000',
    '#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#ffeaa7',
    '#dda0dd', '#98d8c8', '#f7dc6f', '#bb8fce', '#85c1e9'
  ]

  return (
    <div className="relative">
      <div
        className="backdrop-blur-3xl border border-white/60 rounded-2xl shadow-2xl p-2 md:p-3"
        style={{
          background: 'linear-gradient(135deg, rgba(255, 255, 255, 0.4), rgba(255, 255, 255, 0.2))',
          backdropFilter: 'blur(50px) saturate(180%)',
          boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(255, 255, 255, 0.6) inset, 0 0 0 2px rgba(255, 255, 255, 0.2) inset'
        }}
      >
        <div className="flex items-center space-x-2 md:space-x-4">
          {/* Color Display */}
          <div className="flex items-center space-x-1 md:space-x-2">
            <div
              className="w-6 h-6 md:w-8 md:h-8 rounded-lg border-2 border-white shadow-md cursor-pointer"
              style={{ backgroundColor: selectedColor }}
              onClick={() => setIsOpen(!isOpen)}
            />
            <span className="text-xs md:text-sm font-medium text-gray-700">Color</span>
          </div>

          {/* Preset Colors - Reduced for mobile */}
          <div className="flex items-center space-x-0.5 md:space-x-1">
            {presetColors.slice(0, 5).map((color) => ( /* Reduced from 10 to 5 for mobile */
              <button
                key={color}
                className={`w-4 h-4 md:w-6 md:h-6 rounded-md border-2 transition-all duration-200 hover:scale-110 ${
                  selectedColor === color ? 'border-blue-500 shadow-md' : 'border-white/50'
                }`}
                style={{ backgroundColor: color }}
                onClick={() => onColorChange(color)}
              />
            ))}

            {/* More Colors Button */}
            <button
              onClick={() => setIsOpen(!isOpen)}
              className="w-4 h-4 md:w-6 md:h-6 rounded-md border-2 border-gray-300 bg-gradient-to-br from-red-400 via-yellow-400 to-blue-400 hover:scale-110 transition-all duration-200 flex items-center justify-center"
            >
              <span className="text-white text-xs font-bold">+</span>
            </button>
          </div>

          {/* Custom Color Input - Hidden on mobile */}
          <div className="hidden md:flex items-center space-x-2">
            <input
              type="color"
              value={selectedColor}
              onChange={(e) => onColorChange(e.target.value)}
              className="w-8 h-8 rounded-lg border-2 border-white shadow-md cursor-pointer"
            />
            <span className="text-xs text-gray-600 font-mono">{selectedColor}</span>
          </div>
        </div>

        {/* Extended Color Palette */}
        {isOpen && (
          <div className="mt-3 pt-3 border-t border-gray-200/50">
            <div className="grid grid-cols-10 gap-1">
              {presetColors.map((color) => (
                <button
                  key={color}
                  className={`w-6 h-6 rounded-md border-2 transition-all duration-200 hover:scale-110 ${
                    selectedColor === color ? 'border-blue-500 shadow-md' : 'border-white/50'
                  }`}
                  style={{ backgroundColor: color }}
                  onClick={() => {
                    onColorChange(color)
                    setIsOpen(false)
                  }}
                />
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
