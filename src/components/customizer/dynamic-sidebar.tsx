'use client'

import React from 'react'
import VerticalGLBSelector from './vertical-glb-selector'
import VerticalPrintFileSelector from './vertical-print-file-selector'

interface PrintFile {
  id?: string
  filename: string
  url: string
  [key: string]: any
}

interface GLBFile {
  id?: string
  name?: string
  url: string
  [key: string]: any
}

interface DynamicSidebarProps {
  // View state - determines which selector to show
  currentView: '3d' | 'printfile'
  
  // GLB data and handlers
  glbFiles: GLBFile[]
  selectedGLBIndex: number
  onGLBSelect: (index: number) => void
  getGLBUrl: (glbFile: GLBFile) => string
  
  // Print file data and handlers
  printFiles: PrintFile[]
  selectedPrintFileIndex: number
  onPrintFileSelect: (index: number) => void
  getFileUrl: (bucket: string, path: string) => string
}

export default function DynamicSidebar({
  currentView,
  glbFiles = [],
  selectedGLBIndex,
  onGLBSelect,
  getGLBUrl,
  printFiles = [],
  selectedPrintFileIndex,
  onPrintFileSelect,
  getFileUrl
}: DynamicSidebarProps) {

  // Show 3D selector when currentView is '3d'
  if (currentView === '3d') {
    // Only show if there are GLB files
    if (glbFiles.length === 0) {
      return (
        <div className="w-full h-full flex items-center justify-center">
          <div className="bg-white/10 backdrop-blur-md rounded-2xl border border-white/20 shadow-xl p-6 text-center">
            <div className="text-sm text-gray-500">No 3D models available</div>
          </div>
        </div>
      )
    }

    return (
      <VerticalGLBSelector
        glbFiles={glbFiles}
        selectedIndex={selectedGLBIndex}
        onGLBSelect={onGLBSelect}
        getGLBUrl={getGLBUrl}
      />
    )
  }

  // Show print file selector when currentView is 'printfile'
  if (currentView === 'printfile') {
    // Only show if there are print files
    if (printFiles.length === 0) {
      return (
        <div className="w-full h-full flex items-center justify-center">
          <div className="bg-white/10 backdrop-blur-md rounded-2xl border border-white/20 shadow-xl p-6 text-center">
            <div className="text-sm text-gray-500">No print files available</div>
          </div>
        </div>
      )
    }

    return (
      <VerticalPrintFileSelector
        printFiles={printFiles}
        selectedIndex={selectedPrintFileIndex}
        onPrintFileSelect={onPrintFileSelect}
        getFileUrl={getFileUrl}
      />
    )
  }

  // Fallback - should not reach here
  return null
}
