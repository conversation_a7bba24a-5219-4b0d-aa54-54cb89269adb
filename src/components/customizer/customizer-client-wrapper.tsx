'use client'

import dynamic from 'next/dynamic'
import { CustomizerProvider } from '@/components/providers/customizer-provider'

// Dynamic import to ensure client-side only rendering
const UnifiedCustomizerInterface = dynamic(
  () => import('@/components/customizer/unified-customizer-interface'),
  {
    ssr: false,
    loading: () => (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    )
  }
)

interface CustomizerClientWrapperProps {
  product: any
  isIframe: boolean
  shop?: string
  variant?: string
}

export default function CustomizerClientWrapper({
  product,
  isIframe,
  shop,
  variant
}: CustomizerClientWrapperProps) {
  return (
    <CustomizerProvider>
      <UnifiedCustomizerInterface
        product={product}
        isIframe={isIframe}
        shop={shop}
        variant={variant}
      />
    </CustomizerProvider>
  )
}
