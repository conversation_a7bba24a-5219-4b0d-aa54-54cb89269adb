'use client'

import React from 'react'
import DynamicSidebar from './dynamic-sidebar'
import MiniPreviewCanvas from './mini-preview-canvas'
import { CanvasViewState } from './canvas-view-manager'

interface DualCanvasLayoutProps {
  // Main canvas content
  mainCanvas: React.ReactNode
  
  // View state management
  viewState: CanvasViewState
  onSwapViews: () => void
  
  // GLB data and handlers
  glbFiles: any[]
  selectedGLBIndex: number
  onGLBSelect: (index: number) => void
  getGLBUrl: (glbFile: any) => string
  
  // Print file data and handlers
  printFiles: any[]
  selectedPrintFileIndex: number
  onPrintFileSelect: (index: number) => void
  
  // Design elements
  designElements: any[]

  // Synchronization props
  selectedDesignArea?: any
  textureEngine?: any

  // Utility functions
  getFileUrl: (bucket: string, path: string) => string
  
  // Layout configuration
  showLeftSidebar?: boolean
  showMiniPreview?: boolean
  leftSidebarWidth?: number
  miniPreviewWidth?: number
  miniPreviewHeight?: number
  
  // Additional panels
  toolsPanel?: React.ReactNode
  headerContent?: React.ReactNode
}

export default function DualCanvasLayout({
  mainCanvas,
  viewState,
  onSwapViews,
  glbFiles = [],
  selectedGLBIndex,
  onGLBSelect,
  getGLBUrl,
  printFiles = [],
  selectedPrintFileIndex,
  onPrintFileSelect,
  designElements = [],
  selectedDesignArea,
  textureEngine,
  getFileUrl,
  showLeftSidebar = true,
  showMiniPreview = true,
  leftSidebarWidth = 200,
  miniPreviewWidth = 280,
  miniPreviewHeight = 320,
  toolsPanel,
  headerContent
}: DualCanvasLayoutProps) {
  
  return (
    <div className="w-full flex flex-col relative">
      {/* Header Content */}
      {headerContent && (
        <div className="flex-shrink-0 z-20">
          {headerContent}
        </div>
      )}
      
      {/* Main Layout Container */}
      <div className="flex-1 flex relative overflow-hidden">
        {/* Left Sidebar - Dynamic Selector - Positioned absolutely */}
        {showLeftSidebar && (glbFiles.length > 0 || printFiles.length > 0) && (
          <div
            className="absolute left-0 top-10 bottom-4 p-4 z-10"
            style={{ width: leftSidebarWidth }}
          >
            <DynamicSidebar
              currentView={viewState.mainCanvasView}
              glbFiles={glbFiles}
              selectedGLBIndex={selectedGLBIndex}
              onGLBSelect={onGLBSelect}
              getGLBUrl={getGLBUrl}
              printFiles={printFiles}
              selectedPrintFileIndex={selectedPrintFileIndex}
              onPrintFileSelect={onPrintFileSelect}
              getFileUrl={getFileUrl}
            />
          </div>
        )}

        {/* Main Canvas Area - Always full width and centered */}
        <div className="flex-1 relative flex items-center justify-center p-4">
          {/* Main Canvas Content */}
          <div className={`w-full h-full transition-all duration-300 ${
            viewState.isSwapping ? 'opacity-50 scale-95' : 'opacity-100 scale-100'
          }`}>
            {mainCanvas}
          </div>
          
          {/* Tools Panel Overlay */}
          {toolsPanel && (
            <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 z-20">
              {toolsPanel}
            </div>
          )}
        </div>
        
        {/* Mini Preview Canvas - Positioned absolutely */}
        {showMiniPreview && (
          <div className="absolute right-0 top-0 z-30">
            <MiniPreviewCanvas
              currentView={viewState.miniCanvasView}
              onSwapViews={onSwapViews}
              printFiles={printFiles}
              selectedPrintFileIndex={selectedPrintFileIndex}
              onPrintFileSelect={onPrintFileSelect}
              glbFiles={glbFiles}
              selectedGLBIndex={selectedGLBIndex}
              onGLBSelect={onGLBSelect}
              designElements={designElements}
              selectedDesignArea={selectedDesignArea}
              textureEngine={textureEngine}
              getFileUrl={getFileUrl}
              getGLBUrl={getGLBUrl}
              width={miniPreviewWidth}
              height={miniPreviewHeight}
            />
          </div>
        )}
      </div>
      
      {/* Swap Animation Overlay */}
      {viewState.isSwapping && (
        <div className="absolute inset-0 bg-white/20 backdrop-blur-sm flex items-center justify-center z-40 pointer-events-none">
          <div className="bg-white/90 backdrop-blur-sm rounded-lg px-6 py-3 shadow-lg">
            <div className="flex items-center space-x-3">
              <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-500"></div>
              <span className="text-sm font-medium text-gray-700">Swapping views...</span>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

// Responsive layout hook for mobile optimization
export function useDualCanvasResponsive() {
  const [isMobile, setIsMobile] = React.useState(false)
  const [screenSize, setScreenSize] = React.useState({ width: 0, height: 0 })
  
  React.useEffect(() => {
    const checkScreenSize = () => {
      const width = window.innerWidth
      const height = window.innerHeight
      setScreenSize({ width, height })
      setIsMobile(width < 768)
    }
    
    checkScreenSize()
    window.addEventListener('resize', checkScreenSize)
    return () => window.removeEventListener('resize', checkScreenSize)
  }, [])
  
  const getResponsiveConfig = () => ({
    showLeftSidebar: !isMobile,
    leftSidebarWidth: isMobile ? 0 : 200,
    miniPreviewWidth: isMobile ? 200 : 280,
    miniPreviewHeight: isMobile ? 240 : 320,
    toolsPanelPosition: isMobile ? 'bottom' : 'overlay'
  })
  
  return {
    isMobile,
    screenSize,
    ...getResponsiveConfig()
  }
}
