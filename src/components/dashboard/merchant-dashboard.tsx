'use client'

import { useState } from 'react'
import { AuthUser } from '@/lib/auth'
import { Database } from '@/lib/database.types'

type UserProfile = Database['public']['Tables']['user_profiles']['Row']
import {
  ShoppingBagIcon,
  CubeIcon,
  ChartBarIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  ClockIcon,
  PlusIcon,
  HomeIcon,
  ClipboardDocumentListIcon,
  HeartIcon,
  Cog6ToothIcon,
  RectangleStackIcon,
  UserGroupIcon,
  TicketIcon,
  CreditCardIcon
} from '@heroicons/react/24/outline'
import Link from 'next/link'

interface MerchantDashboardProps {
  user: AuthUser
  profile: UserProfile
  data: any
  merchant?: any
  isEmbedded?: boolean
}

export default function MerchantDashboard({ user, profile, data, merchant, isEmbedded = false }: MerchantDashboardProps) {
  const { merchantAccount, shopifyData, customOrders, customProducts } = data
  const [activeTab, setActiveTab] = useState('dashboard')

  // Function to render content based on active tab
  const renderTabContent = () => {
    switch (activeTab) {
      case 'dashboard':
        return renderDashboardContent()
      case 'products':
        return renderProductsContent()
      case 'orders':
        return renderOrdersContent()
      case 'catalog':
        return renderCatalogContent()
      case 'customer-designs':
        return renderCustomerDesignsContent()
      case 'tickets':
        return renderTicketsContent()
      case 'subscription':
        return renderSubscriptionContent()
      case 'settings':
        return renderSettingsContent()
      default:
        return renderDashboardContent()
    }
  }

  const renderDashboardContent = () => (
    <>
      {/* Header */}
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Dashboard</h1>
        <p className="text-gray-600">Welcome back! Here's what's happening with your store.</p>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {stats.map((stat) => (
          <div key={stat.name} className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <div className={`flex-shrink-0 p-3 rounded-lg ${stat.bgColor}`}>
                <stat.icon className={`h-6 w-6 ${stat.color}`} />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">{stat.name}</p>
                <p className="text-2xl font-semibold text-gray-900">{stat.value}</p>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Welcome Section */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8">
        <div className="flex items-start">
          <div className="flex-shrink-0">
            <div className="flex items-center justify-center h-12 w-12 rounded-md bg-blue-500 text-white">
              <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
            </div>
          </div>
          <div className="ml-4">
            <h3 className="text-lg font-medium text-blue-900">Welcome to Biypod Customizer!</h3>
            <p className="mt-2 text-sm text-blue-700">
              Your app has been successfully installed. Start customizing your products and engaging your customers with personalized designs.
            </p>
          </div>
        </div>
      </div>
    </>
  )

  const renderProductsContent = () => (
    <>
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Products</h1>
        <p className="text-gray-600">Manage your customizable products and settings.</p>
      </div>

      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="text-center py-12">
          <CubeIcon className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No products configured</h3>
          <p className="mt-1 text-sm text-gray-500">Get started by enabling customization on your products.</p>
          <div className="mt-6">
            <button className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
              <PlusIcon className="-ml-1 mr-2 h-5 w-5" />
              Enable Product Customization
            </button>
          </div>
        </div>
      </div>
    </>
  )

  const renderOrdersContent = () => (
    <>
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Orders</h1>
        <p className="text-gray-600">Monitor and manage custom orders from your customers.</p>
      </div>

      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="text-center py-12">
          <ClipboardDocumentListIcon className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No custom orders yet</h3>
          <p className="mt-1 text-sm text-gray-500">Custom orders will appear here once customers start personalizing products.</p>
        </div>
      </div>
    </>
  )

  const renderCatalogContent = () => (
    <>
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Product Catalog</h1>
        <p className="text-gray-600">View and publish products from the super admin catalog to your store.</p>
      </div>

      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="text-center py-12">
          <RectangleStackIcon className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No catalog products available</h3>
          <p className="mt-1 text-sm text-gray-500">Products from the super admin catalog will appear here for you to publish to your store.</p>
          <div className="mt-6">
            <button className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
              <PlusIcon className="-ml-1 mr-2 h-5 w-5" />
              Browse Catalog
            </button>
          </div>
        </div>
      </div>
    </>
  )

  const renderCustomerDesignsContent = () => (
    <>
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Customer Designs</h1>
        <p className="text-gray-600">View and manage designs created by your customers.</p>
      </div>

      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="text-center py-12">
          <UserGroupIcon className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No customer designs yet</h3>
          <p className="mt-1 text-sm text-gray-500">Designs created by your customers will be displayed here.</p>
        </div>
      </div>
    </>
  )

  const renderTicketsContent = () => (
    <>
      <div className="mb-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Support Tickets</h1>
            <p className="text-gray-600">Create and track support requests.</p>
          </div>
          <button className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
            <PlusIcon className="-ml-1 mr-2 h-5 w-5" />
            New Ticket
          </button>
        </div>
      </div>

      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="text-center py-12">
          <TicketIcon className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No support tickets</h3>
          <p className="mt-1 text-sm text-gray-500">Your support requests will appear here. Create a ticket if you need help.</p>
        </div>
      </div>
    </>
  )

  const renderSubscriptionContent = () => (
    <>
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Subscription</h1>
        <p className="text-gray-600">Manage your app billing plan and subscription.</p>
      </div>

      <div className="space-y-6">
        {/* Current Plan */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Current Plan</h3>
          <div className="flex items-center justify-between">
            <div>
              <div className="flex items-center">
                <span className="text-2xl font-bold text-gray-900">Free Trial</span>
                <span className="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                  Active
                </span>
              </div>
              <p className="text-sm text-gray-500 mt-1">Trial ends in 14 days</p>
            </div>
            <div className="text-right">
              <p className="text-2xl font-bold text-gray-900">$0/month</p>
              <p className="text-sm text-gray-500">Then $29/month</p>
            </div>
          </div>
        </div>

        {/* Plan Features */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Plan Features</h3>
          <div className="space-y-3">
            <div className="flex items-center">
              <CheckCircleIcon className="h-5 w-5 text-green-500 mr-3" />
              <span className="text-sm text-gray-900">Unlimited product customizations</span>
            </div>
            <div className="flex items-center">
              <CheckCircleIcon className="h-5 w-5 text-green-500 mr-3" />
              <span className="text-sm text-gray-900">Customer design library</span>
            </div>
            <div className="flex items-center">
              <CheckCircleIcon className="h-5 w-5 text-green-500 mr-3" />
              <span className="text-sm text-gray-900">Order management</span>
            </div>
            <div className="flex items-center">
              <CheckCircleIcon className="h-5 w-5 text-green-500 mr-3" />
              <span className="text-sm text-gray-900">Email support</span>
            </div>
          </div>
        </div>

        {/* Billing Actions */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Billing Actions</h3>
          <div className="space-y-3">
            <button className="w-full inline-flex justify-center items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
              Upgrade to Pro Plan
            </button>
            <button className="w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
              View Billing History
            </button>
          </div>
        </div>
      </div>
    </>
  )

  const renderSettingsContent = () => (
    <>
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Settings</h1>
        <p className="text-gray-600">Configure your app preferences and customization options.</p>
      </div>

      <div className="space-y-6">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">General Settings</h3>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium text-gray-900">Enable Customizer</label>
                <p className="text-sm text-gray-500">Allow customers to customize products</p>
              </div>
              <button className="relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent bg-blue-600 transition-colors duration-200 ease-in-out">
                <span className="translate-x-5 inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out"></span>
              </button>
            </div>
            <div className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium text-gray-900">Auto-fulfill Orders</label>
                <p className="text-sm text-gray-500">Automatically fulfill custom orders</p>
              </div>
              <button className="relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent bg-gray-200 transition-colors duration-200 ease-in-out">
                <span className="translate-x-0 inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out"></span>
              </button>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Theme Settings</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700">Primary Color</label>
              <div className="mt-1 flex items-center space-x-3">
                <div className="w-8 h-8 bg-blue-500 rounded border border-gray-300"></div>
                <input type="text" value="#3B82F6" className="block w-full border-gray-300 rounded-md shadow-sm text-sm text-black" />
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">Secondary Color</label>
              <div className="mt-1 flex items-center space-x-3">
                <div className="w-8 h-8 bg-green-500 rounded border border-gray-300"></div>
                <input type="text" value="#10B981" className="block w-full border-gray-300 rounded-md shadow-sm text-sm" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  )

  // Calculate stats
  const totalOrders = customOrders?.length || 0
  const totalProducts = customProducts?.length || 0
  const pendingOrders = customOrders?.filter((order: any) => order.status === 'pending')?.length || 0
  const totalRevenue = customOrders?.reduce((sum: number, order: any) => sum + (order.total_amount || 0), 0) || 0

  const stats = [
    {
      name: 'Total Orders',
      value: totalOrders,
      icon: ShoppingBagIcon,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100',
    },
    {
      name: 'Custom Products',
      value: totalProducts,
      icon: CubeIcon,
      color: 'text-green-600',
      bgColor: 'bg-green-100',
    },
    {
      name: 'Pending Orders',
      value: pendingOrders,
      icon: ClockIcon,
      color: 'text-yellow-600',
      bgColor: 'bg-yellow-100',
    },
    {
      name: 'Revenue',
      value: `$${totalRevenue.toFixed(2)}`,
      icon: ChartBarIcon,
      color: 'text-purple-600',
      bgColor: 'bg-purple-100',
    },
  ]

  if (isEmbedded) {
    return (
      <div className="min-h-screen bg-gray-50 flex pb-safe-bottom">
        {/* Sidebar Navigation */}
        <div className="w-64 bg-white shadow-sm border-r border-gray-200 flex flex-col">
          {/* Header */}
          <div className="p-6 border-b border-gray-200">
            <h1 className="text-xl font-bold text-gray-900">Biypod Customizer</h1>
            <p className="text-sm text-gray-500 mt-1">
              {merchant?.shop_name || 'Dashboard'}
            </p>
          </div>

          {/* Navigation */}
          <nav className="flex-1 p-4 space-y-2">
            <button
              onClick={() => setActiveTab('dashboard')}
              className={`w-full flex items-center px-3 py-2 text-sm font-medium rounded-md ${
                activeTab === 'dashboard'
                  ? 'text-blue-600 bg-blue-50'
                  : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
              }`}
            >
              <HomeIcon className="mr-3 h-5 w-5" />
              Dashboard
            </button>
            <button
              onClick={() => setActiveTab('products')}
              className={`w-full flex items-center px-3 py-2 text-sm font-medium rounded-md ${
                activeTab === 'products'
                  ? 'text-blue-600 bg-blue-50'
                  : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
              }`}
            >
              <CubeIcon className="mr-3 h-5 w-5" />
              Products
            </button>
            <button
              onClick={() => setActiveTab('orders')}
              className={`w-full flex items-center px-3 py-2 text-sm font-medium rounded-md ${
                activeTab === 'orders'
                  ? 'text-blue-600 bg-blue-50'
                  : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
              }`}
            >
              <ClipboardDocumentListIcon className="mr-3 h-5 w-5" />
              Orders
            </button>
            <button
              onClick={() => setActiveTab('catalog')}
              className={`w-full flex items-center px-3 py-2 text-sm font-medium rounded-md ${
                activeTab === 'catalog'
                  ? 'text-blue-600 bg-blue-50'
                  : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
              }`}
            >
              <RectangleStackIcon className="mr-3 h-5 w-5" />
              Product Catalog
            </button>
            <button
              onClick={() => setActiveTab('customer-designs')}
              className={`w-full flex items-center px-3 py-2 text-sm font-medium rounded-md ${
                activeTab === 'customer-designs'
                  ? 'text-blue-600 bg-blue-50'
                  : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
              }`}
            >
              <UserGroupIcon className="mr-3 h-5 w-5" />
              Customer Designs
            </button>
            <button
              onClick={() => setActiveTab('tickets')}
              className={`w-full flex items-center px-3 py-2 text-sm font-medium rounded-md ${
                activeTab === 'tickets'
                  ? 'text-blue-600 bg-blue-50'
                  : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
              }`}
            >
              <TicketIcon className="mr-3 h-5 w-5" />
              Support Tickets
            </button>
            <button
              onClick={() => setActiveTab('subscription')}
              className={`w-full flex items-center px-3 py-2 text-sm font-medium rounded-md ${
                activeTab === 'subscription'
                  ? 'text-blue-600 bg-blue-50'
                  : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
              }`}
            >
              <CreditCardIcon className="mr-3 h-5 w-5" />
              Subscription
            </button>
            <button
              onClick={() => setActiveTab('settings')}
              className={`w-full flex items-center px-3 py-2 text-sm font-medium rounded-md ${
                activeTab === 'settings'
                  ? 'text-blue-600 bg-blue-50'
                  : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
              }`}
            >
              <Cog6ToothIcon className="mr-3 h-5 w-5" />
              Settings
            </button>
          </nav>

          {/* Status */}
          <div className="p-4 border-t border-gray-200">
            <div className="flex items-center">
              <div className="h-2 w-2 bg-green-400 rounded-full mr-2"></div>
              <span className="text-sm text-gray-600">Connected</span>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="flex-1 overflow-auto">
          <div className="p-6">
            {renderTabContent()}
          </div>
        </div>
      </div>
    )
  }

  // Regular dashboard for non-embedded view
  return (
    <div className="min-h-screen bg-gray-50 pb-safe-bottom">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <h1 className="text-2xl font-bold text-gray-900 mb-8">Merchant Dashboard</h1>
        <p className="text-gray-600">This is the regular merchant dashboard view.</p>
      </div>
    </div>
  )
}
