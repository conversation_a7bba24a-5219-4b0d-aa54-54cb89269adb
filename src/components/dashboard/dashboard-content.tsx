'use client'

import { AuthUser } from '@/lib/auth'
import { Database } from '@/lib/database.types'
import SuperAdminDashboard from './super-admin-dashboard'
import MerchantDashboard from './merchant-dashboard'
import CustomerDashboard from './customer-dashboard'

type UserProfile = Database['public']['Tables']['user_profiles']['Row']
type UserRole = Database['public']['Tables']['users']['Row']['role']

interface DashboardContentProps {
  user: AuthUser
  profile: UserProfile
  userRole: UserRole
  dashboardData: any
}

export default function DashboardContent({
  user,
  profile,
  userRole,
  dashboardData
}: DashboardContentProps) {
  if (userRole === 'super_admin') {
    return (
      <SuperAdminDashboard 
        user={user}
        profile={profile}
        data={dashboardData}
      />
    )
  }

  if (userRole === 'merchant') {
    return (
      <MerchantDashboard 
        user={user}
        profile={profile}
        data={dashboardData}
      />
    )
  }

  return (
    <CustomerDashboard
      user={user}
      profile={profile}
      userRole={userRole}
      data={dashboardData}
    />
  )
}
