'use client';

import React, { useRef, useEffect, useState } from 'react';
import { DesignAreaUV } from '@/lib/uv-extractor';

interface DesignCanvasProps {
  width?: number;
  height?: number;
  selectedDesignArea?: DesignAreaUV;
  onCanvasChange?: (canvas: HTMLCanvasElement) => void;
}

export default function DesignCanvas({
  width = 400,
  height = 400,
  selectedDesignArea,
  onCanvasChange
}: DesignCanvasProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [isReady, setIsReady] = useState(false);

  useEffect(() => {
    if (!canvasRef.current) return;

    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d')!;

    // Set canvas size only if it's different
    if (canvas.width !== width || canvas.height !== height) {
      canvas.width = width;
      canvas.height = height;

      // Clear canvas with white background only when resizing
      ctx.fillStyle = 'white';
      ctx.fillRect(0, 0, width, height);
    }

    setIsReady(true);

    // Only call onCanvasChange if canvas has content or is newly initialized
    if (onCanvasChange) {
      onCanvasChange(canvas);
    }
  }, [width, height]);

  const addText = () => {
    if (!canvasRef.current) return;
    console.log('🎨 Adding text to canvas');

    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d')!;

    ctx.font = '24px Arial';
    ctx.fillStyle = '#000000';
    ctx.textAlign = 'center';
    ctx.fillText('Your Design', width / 2, height / 2);

    console.log('🎨 Text added, calling onCanvasChange');
    if (onCanvasChange) {
      onCanvasChange(canvas);
    }
  };

  const addRectangle = () => {
    if (!canvasRef.current) return;
    console.log('🎨 Adding rectangle to canvas');

    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d')!;

    ctx.fillStyle = '#ff0000';
    ctx.fillRect(50, 50, 100, 100);
    ctx.strokeStyle = '#000000';
    ctx.lineWidth = 2;
    ctx.strokeRect(50, 50, 100, 100);

    // Debug: Check what's actually on the canvas
    const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
    let redPixels = 0;
    for (let i = 0; i < imageData.data.length; i += 4) {
      if (imageData.data[i] === 255 && imageData.data[i + 1] === 0 && imageData.data[i + 2] === 0) {
        redPixels++;
      }
    }

    console.log('🎨 Rectangle added, calling onCanvasChange', {
      canvasSize: `${canvas.width}x${canvas.height}`,
      redPixels,
      totalPixels: imageData.data.length / 4
    });
    if (onCanvasChange) {
      onCanvasChange(canvas);
    }
  };

  const addCircle = () => {
    if (!canvasRef.current) return;

    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d')!;

    ctx.beginPath();
    ctx.arc(width / 2, height / 2 + 50, 40, 0, Math.PI * 2);
    ctx.fillStyle = '#00ff00';
    ctx.fill();
    ctx.strokeStyle = '#000000';
    ctx.lineWidth = 2;
    ctx.stroke();

    if (onCanvasChange) {
      onCanvasChange(canvas);
    }
  };

  const clearCanvas = () => {
    if (!canvasRef.current) return;

    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d')!;

    ctx.fillStyle = 'white';
    ctx.fillRect(0, 0, width, height);

    if (onCanvasChange) {
      onCanvasChange(canvas);
    }
  };

  return (
    <div className="design-canvas">
      <div className="mb-4">
        <h3 className="text-lg font-semibold mb-2">Design Canvas</h3>
        {selectedDesignArea && (
          <p className="text-sm text-black mb-2">
            Editing: <strong>{selectedDesignArea.areaName}</strong>
            ({selectedDesignArea.mapping.textureSize.width}x{selectedDesignArea.mapping.textureSize.height})
          </p>
        )}

        <div className="flex flex-wrap gap-2 mb-4">
          <button
            onClick={addText}
            className="px-3 py-1 bg-blue-500 text-white rounded text-sm hover:bg-blue-600"
            disabled={!isReady}
          >
            Add Text
          </button>
          <button
            onClick={addRectangle}
            className="px-3 py-1 bg-green-500 text-white rounded text-sm hover:bg-green-600"
            disabled={!isReady}
          >
            Add Rectangle
          </button>
          <button
            onClick={addCircle}
            className="px-3 py-1 bg-purple-500 text-white rounded text-sm hover:bg-purple-600"
            disabled={!isReady}
          >
            Add Circle
          </button>
          <button
            onClick={clearCanvas}
            className="px-3 py-1 bg-red-500 text-white rounded text-sm hover:bg-red-600"
            disabled={!isReady}
          >
            Clear
          </button>
          <button
            onClick={() => {
              if (!canvasRef.current || !selectedDesignArea) return;
              const canvas = canvasRef.current;
              const ctx = canvas.getContext('2d')!;

              // Clear canvas first
              ctx.fillStyle = 'white';
              ctx.fillRect(0, 0, width, height);

              // Create distinctive pattern for each area
              const areaName = selectedDesignArea.areaName;
              const colors = {
                'Front': '#ff0000',    // Red
                'Back': '#00ff00',     // Green
                'Left_Sleeve': '#0000ff',  // Blue
                'Right_Sleeve': '#ffff00', // Yellow
                'Collar': '#ff00ff'    // Magenta
              };

              const color = colors[areaName as keyof typeof colors] || '#ff00ff';

              // Fill with area-specific color
              ctx.fillStyle = color;
              ctx.fillRect(50, 50, width - 100, height - 100);

              // Add area name text
              ctx.fillStyle = 'white';
              ctx.font = 'bold 24px Arial';
              ctx.textAlign = 'center';
              ctx.fillText(areaName, width / 2, height / 2);

              if (onCanvasChange) {
                onCanvasChange(canvas);
              }
            }}
            className="px-3 py-1 bg-purple-500 text-white rounded text-sm hover:bg-purple-600"
            disabled={!isReady || !selectedDesignArea}
          >
            Test {selectedDesignArea?.areaName || 'Area'}
          </button>
        </div>
      </div>

      <div className="border border-gray-300 rounded">
        <canvas
          ref={canvasRef}
          className="block cursor-crosshair"
          style={{ maxWidth: '100%', height: 'auto' }}
        />
      </div>

      {selectedDesignArea && (
        <div className="mt-2 text-xs text-black">
          <p>UV Bounds: [{selectedDesignArea.mapping.boundingBox.min[0].toFixed(3)}, {selectedDesignArea.mapping.boundingBox.min[1].toFixed(3)}]
          to [{selectedDesignArea.mapping.boundingBox.max[0].toFixed(3)}, {selectedDesignArea.mapping.boundingBox.max[1].toFixed(3)}]</p>
        </div>
      )}
    </div>
  );
}