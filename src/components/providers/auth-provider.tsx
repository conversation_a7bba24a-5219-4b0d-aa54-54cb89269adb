'use client'

import { createContext, useContext, useEffect, useState } from 'react'
import { createSupabaseBrowserClient } from '@/lib/supabase'
import type { User } from '@supabase/supabase-js'
import type { Database } from '@/lib/database.types'

type UserProfile = Database['public']['Tables']['users']['Row'] & {
  user_profiles?: Database['public']['Tables']['user_profiles']['Row']
  merchants?: Database['public']['Tables']['merchants']['Row']
}

interface AuthContextType {
  user: User | null
  userProfile: UserProfile | null
  isLoading: boolean
  signOut: () => Promise<void>
  refreshUser: () => Promise<void>
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  const supabase = createSupabaseBrowserClient()

  const fetchUserProfile = async (userId: string) => {
    try {
      const { data, error } = await supabase
        .from('users')
        .select(`
          *,
          user_profiles(*),
          merchants(*)
        `)
        .eq('id', userId)
        .single()

      if (error) {
        console.error('Error fetching user profile:', error)
        return null
      }

      // Transform the data to match UserProfile type
      const userProfile: UserProfile = {
        ...data,
        user_profiles: data.user_profiles || undefined,
        merchants: data.merchants?.[0] || undefined
      }

      return userProfile
    } catch (error) {
      console.error('Error fetching user profile:', error)
      return null
    }
  }

  const refreshUser = async () => {
    try {
      const { data: { user: authUser } } = await supabase.auth.getUser()

      if (authUser) {
        const profile = await fetchUserProfile(authUser.id)
        setUserProfile(profile)
      } else {
        setUserProfile(null)
      }

      setUser(authUser)
    } catch (error) {
      console.error('Error refreshing user:', error)
      setUser(null)
      setUserProfile(null)
    }
  }

  const signOut = async () => {
    try {
      await supabase.auth.signOut()
      setUser(null)
      setUserProfile(null)
    } catch (error) {
      console.error('Error signing out:', error)
    }
  }

  useEffect(() => {
    // Get initial session
    const getInitialSession = async () => {
      try {
        const { data: { session } } = await supabase.auth.getSession()

        if (session?.user) {
          setUser(session.user)
          const profile = await fetchUserProfile(session.user.id)
          setUserProfile(profile)
        }
      } catch (error) {
        console.error('Error getting initial session:', error)
      } finally {
        setIsLoading(false)
      }
    }

    getInitialSession()

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        console.log('Auth state changed:', event, session?.user?.id)

        if (session?.user) {
          setUser(session.user)
          const profile = await fetchUserProfile(session.user.id)
          setUserProfile(profile)
        } else {
          setUser(null)
          setUserProfile(null)
        }

        setIsLoading(false)
      }
    )

    return () => {
      subscription.unsubscribe()
    }
  }, [supabase.auth])

  const value = {
    user,
    userProfile,
    isLoading,
    signOut,
    refreshUser,
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

// Helper hooks for specific roles
export function useUser() {
  const { user, userProfile, isLoading } = useAuth()
  return { user, userProfile, isLoading }
}

export function useMerchant() {
  const { userProfile, isLoading } = useAuth()
  
  const merchant = userProfile?.role === 'merchant' && userProfile.merchants 
    ? (Array.isArray(userProfile.merchants) ? userProfile.merchants[0] : userProfile.merchants)
    : null

  return { 
    merchant, 
    isMerchant: userProfile?.role === 'merchant',
    isLoading 
  }
}

export function useIsSuperAdmin() {
  const { userProfile, isLoading } = useAuth()
  return { 
    isSuperAdmin: userProfile?.role === 'super_admin',
    isLoading 
  }
}

export function useIsCustomer() {
  const { userProfile, isLoading } = useAuth()
  return { 
    isCustomer: userProfile?.role === 'customer',
    isLoading 
  }
}
