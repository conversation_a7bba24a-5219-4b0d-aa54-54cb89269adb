'use client'

import { createContext, useContext, useReducer, useCallback, ReactNode } from 'react'
import { DesignAreaUV } from '@/lib/uv-extractor'
import { FinalTextureEngine } from '@/lib/final-texture-engine'
import { ThreeDInteractionManager } from '@/lib/3d-interaction-manager'
import { CanvasViewState } from '../customizer/canvas-view-manager'

// Design Element Interface
export interface DesignElement {
  id: string
  type: 'text' | 'image' | 'shape'
  x: number
  y: number
  width: number
  height: number
  rotation: number
  zIndex: number
  data: any
}

// Initialization States
export enum InitializationState {
  IDLE = 'idle',
  TEXTURE_ENGINE_READY = 'texture_engine_ready',
  GLB_LOADING = 'glb_loading',
  DESIGN_AREAS_EXTRACTED = 'design_areas_extracted',
  DESIGN_AREA_SELECTED = 'design_area_selected',
  INTERACTION_MANAGER_READY = 'interaction_manager_ready',
  FULLY_INITIALIZED = 'fully_initialized'
}

// Centralized State Interface
export interface CustomizerState {
  // Design Elements
  designElements: DesignElement[]
  selectedElementId: string | null
  editingTextId: string | null
  
  // UI State
  selectedColor: string
  isLoading: boolean
  showSetupModal: boolean
  
  // 3D State
  designAreas: DesignAreaUV[]
  selectedDesignArea: DesignAreaUV | null
  textureEngine: FinalTextureEngine | null
  interactionManager: ThreeDInteractionManager | null
  
  // 3D Configuration
  showWireframe: boolean
  meshVisibility: Record<string, boolean>
  availableMeshes: string[]
  enable3DInteraction: boolean
  
  // Initialization
  initializationState: InitializationState
  
  // Canvas View State (integrated)
  canvasView: CanvasViewState
}

// Action Types
export type CustomizerAction =
  // Design Element Actions
  | { type: 'ADD_ELEMENT'; payload: DesignElement }
  | { type: 'UPDATE_ELEMENT'; payload: { id: string; updates: Partial<DesignElement> } }
  | { type: 'DELETE_ELEMENT'; payload: string }
  | { type: 'SET_SELECTED_ELEMENT'; payload: string | null }
  | { type: 'SET_EDITING_TEXT'; payload: string | null }
  | { type: 'CLEAR_ELEMENTS' }
  
  // UI Actions
  | { type: 'SET_COLOR'; payload: string }
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_SETUP_MODAL'; payload: boolean }
  
  // 3D Actions
  | { type: 'SET_DESIGN_AREAS'; payload: DesignAreaUV[] }
  | { type: 'SET_SELECTED_DESIGN_AREA'; payload: DesignAreaUV | null }
  | { type: 'SET_TEXTURE_ENGINE'; payload: FinalTextureEngine | null }
  | { type: 'SET_INTERACTION_MANAGER'; payload: ThreeDInteractionManager | null }
  
  // 3D Configuration Actions
  | { type: 'SET_WIREFRAME'; payload: boolean }
  | { type: 'SET_MESH_VISIBILITY'; payload: Record<string, boolean> }
  | { type: 'SET_AVAILABLE_MESHES'; payload: string[] }
  | { type: 'SET_3D_INTERACTION'; payload: boolean }
  
  // Initialization Actions
  | { type: 'SET_INITIALIZATION_STATE'; payload: InitializationState }
  
  // Canvas View Actions
  | { type: 'SET_CANVAS_VIEW'; payload: CanvasViewState }

// Initial State
const initialState: CustomizerState = {
  // Design Elements
  designElements: [],
  selectedElementId: null,
  editingTextId: null,
  
  // UI State
  selectedColor: '#000000',
  isLoading: false,
  showSetupModal: false,
  
  // 3D State
  designAreas: [],
  selectedDesignArea: null,
  textureEngine: null,
  interactionManager: null,
  
  // 3D Configuration
  showWireframe: true,
  meshVisibility: {},
  availableMeshes: [],
  enable3DInteraction: true,
  
  // Initialization
  initializationState: InitializationState.IDLE,
  
  // Canvas View State
  canvasView: {
    mainCanvasView: '3d',
    miniCanvasView: 'printfile',
    selectedGLBIndex: 0,
    selectedPrintFileIndex: 0,
    isSwapping: false
  }
}

// Reducer Function
function customizerReducer(state: CustomizerState, action: CustomizerAction): CustomizerState {
  switch (action.type) {
    // Design Element Cases
    case 'ADD_ELEMENT':
      return {
        ...state,
        designElements: [...state.designElements, action.payload]
      }
      
    case 'UPDATE_ELEMENT':
      return {
        ...state,
        designElements: state.designElements.map(element =>
          element.id === action.payload.id
            ? { ...element, ...action.payload.updates }
            : element
        )
      }
      
    case 'DELETE_ELEMENT':
      return {
        ...state,
        designElements: state.designElements.filter(element => element.id !== action.payload),
        selectedElementId: state.selectedElementId === action.payload ? null : state.selectedElementId
      }
      
    case 'SET_SELECTED_ELEMENT':
      return {
        ...state,
        selectedElementId: action.payload
      }
      
    case 'SET_EDITING_TEXT':
      return {
        ...state,
        editingTextId: action.payload
      }
      
    case 'CLEAR_ELEMENTS':
      return {
        ...state,
        designElements: [],
        selectedElementId: null,
        editingTextId: null
      }
      
    // UI Cases
    case 'SET_COLOR':
      return {
        ...state,
        selectedColor: action.payload
      }
      
    case 'SET_LOADING':
      return {
        ...state,
        isLoading: action.payload
      }
      
    case 'SET_SETUP_MODAL':
      return {
        ...state,
        showSetupModal: action.payload
      }
      
    // 3D Cases
    case 'SET_DESIGN_AREAS':
      return {
        ...state,
        designAreas: action.payload
      }
      
    case 'SET_SELECTED_DESIGN_AREA':
      return {
        ...state,
        selectedDesignArea: action.payload
      }
      
    case 'SET_TEXTURE_ENGINE':
      return {
        ...state,
        textureEngine: action.payload
      }
      
    case 'SET_INTERACTION_MANAGER':
      return {
        ...state,
        interactionManager: action.payload
      }
      
    // 3D Configuration Cases
    case 'SET_WIREFRAME':
      return {
        ...state,
        showWireframe: action.payload
      }
      
    case 'SET_MESH_VISIBILITY':
      return {
        ...state,
        meshVisibility: action.payload
      }
      
    case 'SET_AVAILABLE_MESHES':
      return {
        ...state,
        availableMeshes: action.payload
      }
      
    case 'SET_3D_INTERACTION':
      return {
        ...state,
        enable3DInteraction: action.payload
      }
      
    // Initialization Cases
    case 'SET_INITIALIZATION_STATE':
      return {
        ...state,
        initializationState: action.payload
      }
      
    // Canvas View Cases
    case 'SET_CANVAS_VIEW':
      return {
        ...state,
        canvasView: action.payload
      }
      
    default:
      return state
  }
}

// Context Interface
interface CustomizerContextType {
  state: CustomizerState
  dispatch: React.Dispatch<CustomizerAction>
  
  // Convenience action creators
  addElement: (element: DesignElement) => void
  updateElement: (id: string, updates: Partial<DesignElement>) => void
  deleteElement: (id: string) => void
  setSelectedElement: (id: string | null) => void
  setEditingText: (id: string | null) => void
  clearElements: () => void
  
  setColor: (color: string) => void
  setLoading: (loading: boolean) => void
  setSetupModal: (show: boolean) => void
  
  setDesignAreas: (areas: DesignAreaUV[]) => void
  setSelectedDesignArea: (area: DesignAreaUV | null) => void
  setTextureEngine: (engine: FinalTextureEngine | null) => void
  setInteractionManager: (manager: ThreeDInteractionManager | null) => void
  
  setWireframe: (show: boolean) => void
  setMeshVisibility: (visibility: Record<string, boolean>) => void
  setAvailableMeshes: (meshes: string[]) => void
  set3DInteraction: (enabled: boolean) => void
  
  setInitializationState: (state: InitializationState) => void
  setCanvasView: (view: CanvasViewState) => void
}

// Create Context
const CustomizerContext = createContext<CustomizerContextType | undefined>(undefined)

// Provider Component
export function CustomizerProvider({ children }: { children: ReactNode }) {
  const [state, dispatch] = useReducer(customizerReducer, initialState)
  
  // Action Creators
  const addElement = useCallback((element: DesignElement) => {
    dispatch({ type: 'ADD_ELEMENT', payload: element })
  }, [])
  
  const updateElement = useCallback((id: string, updates: Partial<DesignElement>) => {
    dispatch({ type: 'UPDATE_ELEMENT', payload: { id, updates } })
  }, [])
  
  const deleteElement = useCallback((id: string) => {
    dispatch({ type: 'DELETE_ELEMENT', payload: id })
  }, [])
  
  const setSelectedElement = useCallback((id: string | null) => {
    dispatch({ type: 'SET_SELECTED_ELEMENT', payload: id })
  }, [])
  
  const setEditingText = useCallback((id: string | null) => {
    dispatch({ type: 'SET_EDITING_TEXT', payload: id })
  }, [])
  
  const clearElements = useCallback(() => {
    dispatch({ type: 'CLEAR_ELEMENTS' })
  }, [])
  
  const setColor = useCallback((color: string) => {
    dispatch({ type: 'SET_COLOR', payload: color })
  }, [])
  
  const setLoading = useCallback((loading: boolean) => {
    dispatch({ type: 'SET_LOADING', payload: loading })
  }, [])
  
  const setSetupModal = useCallback((show: boolean) => {
    dispatch({ type: 'SET_SETUP_MODAL', payload: show })
  }, [])
  
  const setDesignAreas = useCallback((areas: DesignAreaUV[]) => {
    dispatch({ type: 'SET_DESIGN_AREAS', payload: areas })
  }, [])
  
  const setSelectedDesignArea = useCallback((area: DesignAreaUV | null) => {
    dispatch({ type: 'SET_SELECTED_DESIGN_AREA', payload: area })
  }, [])
  
  const setTextureEngine = useCallback((engine: FinalTextureEngine | null) => {
    dispatch({ type: 'SET_TEXTURE_ENGINE', payload: engine })
  }, [])
  
  const setInteractionManager = useCallback((manager: ThreeDInteractionManager | null) => {
    dispatch({ type: 'SET_INTERACTION_MANAGER', payload: manager })
  }, [])
  
  const setWireframe = useCallback((show: boolean) => {
    dispatch({ type: 'SET_WIREFRAME', payload: show })
  }, [])
  
  const setMeshVisibility = useCallback((visibility: Record<string, boolean>) => {
    dispatch({ type: 'SET_MESH_VISIBILITY', payload: visibility })
  }, [])
  
  const setAvailableMeshes = useCallback((meshes: string[]) => {
    dispatch({ type: 'SET_AVAILABLE_MESHES', payload: meshes })
  }, [])
  
  const set3DInteraction = useCallback((enabled: boolean) => {
    dispatch({ type: 'SET_3D_INTERACTION', payload: enabled })
  }, [])
  
  const setInitializationState = useCallback((state: InitializationState) => {
    dispatch({ type: 'SET_INITIALIZATION_STATE', payload: state })
  }, [])
  
  const setCanvasView = useCallback((view: CanvasViewState) => {
    dispatch({ type: 'SET_CANVAS_VIEW', payload: view })
  }, [])
  
  const value: CustomizerContextType = {
    state,
    dispatch,
    
    // Convenience action creators
    addElement,
    updateElement,
    deleteElement,
    setSelectedElement,
    setEditingText,
    clearElements,
    
    setColor,
    setLoading,
    setSetupModal,
    
    setDesignAreas,
    setSelectedDesignArea,
    setTextureEngine,
    setInteractionManager,
    
    setWireframe,
    setMeshVisibility,
    setAvailableMeshes,
    set3DInteraction,
    
    setInitializationState,
    setCanvasView
  }
  
  return (
    <CustomizerContext.Provider value={value}>
      {children}
    </CustomizerContext.Provider>
  )
}

// Hook to use the context
export function useCustomizer() {
  const context = useContext(CustomizerContext)
  if (context === undefined) {
    throw new Error('useCustomizer must be used within a CustomizerProvider')
  }
  return context
}
