'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { createClient } from '@supabase/supabase-js'

interface Product {
  id: string
  name: string
  description: string
  base_price: number
  category_id?: string
  sku: string
  weight?: number
  materials?: string[]
  tags?: string[]
  care_instructions?: string
  dimensions?: any
  featured: boolean
  print_files?: Array<{
    url: string
    filename: string
  }>
  mockup_files?: Array<{
    url: string
    filename: string
  }>
  glb_files?: Array<{
    url: string
    filename: string
  }>
  texture_files?: Array<{
    url: string
    filename: string
  }>
  preview_files?: Array<{
    url: string
    filename: string
  }>
  product_images?: Array<{
    id?: string
    url: string
    alt_text?: string
    is_primary: boolean
  }>
  merchant_products?: Array<{
    product_variants?: Array<{
      id: string
      title: string
      price: number
      compare_at_price?: number
      sku?: string
      inventory_quantity: number
      option1?: string
      option2?: string
      option3?: string
      weight?: number
    }>
  }>
}

interface EditProductModalProps {
  isOpen: boolean
  onClose: () => void
  onSuccess: () => void
  product: Product | null
  categories: Array<{ id: string; name: string }>
}

export default function EditProductModal({ isOpen, onClose, onSuccess, product, categories }: EditProductModalProps) {
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState('')

  const supabase = createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
  )

  // Helper function to get public URL for files
  const getFileUrl = (bucket: string, path: string) => {
    const { data } = supabase.storage.from(bucket).getPublicUrl(path)
    return data.publicUrl
  }
  
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    basePrice: '',
    categoryId: '',
    sku: '',
    weight: '',
    materials: '',
    tags: '',
    careInstructions: '',
    dimensions: '',
    featured: false
  })

  const [files, setFiles] = useState({
    images: [] as File[],
    printFiles: [] as File[],
    mockupFiles: [] as File[],
    glbFiles: [] as File[],
    textureFiles: [] as File[],
    previewFiles: [] as File[]
  })

  const [existingFiles, setExistingFiles] = useState({
    images: [] as Array<{ id?: string; url: string; alt_text?: string; is_primary: boolean }>,
    printFiles: [] as Array<{ url: string; filename: string }>,
    mockupFiles: [] as Array<{ url: string; filename: string }>,
    glbFiles: [] as Array<{ url: string; filename: string }>,
    textureFiles: [] as Array<{ url: string; filename: string }>,
    previewFiles: [] as Array<{ url: string; filename: string }>
  })

  const [filesToDelete, setFilesToDelete] = useState({
    images: [] as string[],
    printFiles: [] as string[],
    mockupFiles: [] as string[],
    glbFiles: [] as string[],
    textureFiles: [] as string[],
    previewFiles: [] as string[]
  })

  const [variants, setVariants] = useState([
    {
      id: Date.now(),
      title: 'Default',
      price: '',
      compareAtPrice: '',
      sku: '',
      inventoryQuantity: '',
      option1: '',
      option2: '',
      option3: '',
      weight: ''
    }
  ])

  // Load product data when modal opens
  useEffect(() => {
    if (product && isOpen) {
      setFormData({
        name: product.name || '',
        description: product.description || '',
        basePrice: product.base_price?.toString() || '',
        categoryId: product.category_id || '',
        sku: product.sku || '',
        weight: product.weight?.toString() || '',
        materials: product.materials?.join(', ') || '',
        tags: product.tags?.join(', ') || '',
        careInstructions: product.care_instructions || '',
        dimensions: product.dimensions ? JSON.stringify(product.dimensions) : '',
        featured: product.featured || false
      })

      // Load existing variants
      const existingVariants = product.merchant_products?.[0]?.product_variants || []
      if (existingVariants.length > 0) {
        setVariants(existingVariants.map(v => ({
          id: parseInt(v.id),
          title: v.title,
          price: v.price.toString(),
          compareAtPrice: v.compare_at_price?.toString() || '',
          sku: v.sku || '',
          inventoryQuantity: v.inventory_quantity.toString(),
          option1: v.option1 || '',
          option2: v.option2 || '',
          option3: v.option3 || '',
          weight: v.weight?.toString() || ''
        })))
      }

      // Load existing files
      setExistingFiles({
        images: product.product_images || [],
        printFiles: product.print_files || [],
        mockupFiles: product.mockup_files || [],
        glbFiles: product.glb_files || [],
        textureFiles: product.texture_files || [],
        previewFiles: product.preview_files || []
      })

      // Reset files to delete
      setFilesToDelete({
        images: [],
        printFiles: [],
        mockupFiles: [],
        glbFiles: [],
        textureFiles: [],
        previewFiles: []
      })
    }
  }, [product, isOpen])

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : value
    }))
  }

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>, fileType: 'images' | 'printFiles' | 'mockupFiles' | 'glbFiles' | 'textureFiles' | 'previewFiles') => {
    const selectedFiles = Array.from(e.target.files || [])
    console.log(`Selected ${selectedFiles.length} ${fileType}:`, selectedFiles.map(f => f.name))
    setFiles(prev => ({
      ...prev,
      [fileType]: selectedFiles
    }))
  }

  const handleDeleteExistingFile = (fileType: 'images' | 'printFiles' | 'mockupFiles' | 'glbFiles' | 'textureFiles' | 'previewFiles', fileUrl: string, fileId?: string) => {
    // Add to files to delete
    setFilesToDelete(prev => ({
      ...prev,
      [fileType]: [...prev[fileType], fileId || fileUrl]
    }))

    // Remove from existing files display
    setExistingFiles(prev => ({
      ...prev,
      [fileType]: prev[fileType].filter(file =>
        fileType === 'images' ? (file as any).id !== fileId : file.url !== fileUrl
      )
    }))
  }

  const addVariant = () => {
    setVariants(prev => [...prev, {
      id: Date.now(),
      title: `Variant ${prev.length + 1}`,
      price: formData.basePrice,
      compareAtPrice: '',
      sku: '',
      inventoryQuantity: '0',
      option1: '',
      option2: '',
      option3: '',
      weight: formData.weight
    }])
  }

  const removeVariant = (id: number) => {
    if (variants.length > 1) {
      setVariants(prev => prev.filter(v => v.id !== id))
    }
  }

  const updateVariant = (id: number, field: string, value: string) => {
    setVariants(prev => prev.map(v => 
      v.id === id ? { ...v, [field]: value } : v
    ))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!product) return
    
    setIsLoading(true)
    setError('')

    try {
      const submitFormData = new FormData()
      
      // Add text fields
      Object.entries(formData).forEach(([key, value]) => {
        if (key === 'materials' || key === 'tags') {
          const stringValue = typeof value === 'string' ? value : ''
          const arrayValue = stringValue ? stringValue.split(',').map(item => item.trim()).filter(Boolean) : []
          submitFormData.append(key, JSON.stringify(arrayValue))
        } else if (key === 'dimensions') {
          try {
            const stringValue = typeof value === 'string' ? value : ''
            const dimensionsObj = stringValue ? JSON.parse(stringValue) : null
            submitFormData.append(key, JSON.stringify(dimensionsObj))
          } catch {
            submitFormData.append(key, JSON.stringify(null))
          }
        } else {
          submitFormData.append(key, value.toString())
        }
      })

      // Add files
      files.images.forEach((file, index) => {
        submitFormData.append(`images${index}`, file)
      })
      
      files.printFiles.forEach((file, index) => {
        submitFormData.append(`printFiles${index}`, file)
      })
      
      files.mockupFiles.forEach((file, index) => {
        submitFormData.append(`mockupFiles${index}`, file)
      })

      files.glbFiles.forEach((file, index) => {
        submitFormData.append(`glbFiles${index}`, file)
      })

      files.textureFiles.forEach((file, index) => {
        submitFormData.append(`textureFiles${index}`, file)
      })

      files.previewFiles.forEach((file, index) => {
        submitFormData.append(`previewFiles${index}`, file)
      })

      // Add variants data
      submitFormData.append('variants', JSON.stringify(variants))

      const response = await fetch(`/api/admin/products/${product.id}`, {
        method: 'PUT',
        body: submitFormData
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || 'Failed to update product')
      }

      onSuccess()
      onClose()

    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred')
    } finally {
      setIsLoading(false)
    }
  }

  if (!isOpen || !product) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold text-gray-900">Edit Product</h3>
            <button 
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
              disabled={isLoading}
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>

        <form onSubmit={handleSubmit} className="px-6 py-4 space-y-6">
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-md p-3">
              <p className="text-sm text-red-600">{error}</p>
            </div>
          )}

          {/* Basic Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Product Name *
              </label>
              <Input
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                required
                placeholder="e.g., Custom T-Shirt"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                SKU
              </label>
              <Input
                name="sku"
                value={formData.sku}
                onChange={handleInputChange}
                placeholder="e.g., TSH-001"
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Description
            </label>
            <textarea
              name="description"
              value={formData.description}
              onChange={handleInputChange}
              rows={3}
              className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm text-black placeholder:text-gray-500"
              placeholder="Product description..."
            />
          </div>

          {/* Pricing and Category */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Base Price * ($)
              </label>
              <Input
                name="basePrice"
                type="number"
                step="0.01"
                value={formData.basePrice}
                onChange={handleInputChange}
                required
                placeholder="19.99"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Category
              </label>
              <select
                name="categoryId"
                value={formData.categoryId}
                onChange={handleInputChange}
                className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm text-black"
              >
                <option value="">Select Category</option>
                {categories.map(category => (
                  <option key={category.id} value={category.id}>
                    {category.name}
                  </option>
                ))}
              </select>
            </div>
          </div>

          {/* Additional Product Details */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Weight (lbs)
              </label>
              <Input
                name="weight"
                type="number"
                step="0.01"
                value={formData.weight}
                onChange={handleInputChange}
                placeholder="0.5"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Materials (comma separated)
              </label>
              <Input
                name="materials"
                value={formData.materials}
                onChange={handleInputChange}
                placeholder="Cotton, Polyester"
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Tags (comma separated)
              </label>
              <Input
                name="tags"
                value={formData.tags}
                onChange={handleInputChange}
                placeholder="custom, apparel, trending"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Care Instructions
              </label>
              <Input
                name="careInstructions"
                value={formData.careInstructions}
                onChange={handleInputChange}
                placeholder="Machine wash cold"
              />
            </div>
          </div>

          {/* File Uploads */}
          <div className="space-y-4">
            <h4 className="text-md font-medium text-gray-900">File Uploads</h4>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Product Images (includes 3D Preview Images)
                </label>

                {/* Existing Images */}
                {(existingFiles.images.length > 0 || existingFiles.previewFiles.length > 0) && (
                  <div className="mb-3">
                    <p className="text-sm text-gray-600 mb-2">Current Images:</p>
                    <div className="grid grid-cols-2 gap-2">
                      {/* Traditional Product Images */}
                      {existingFiles.images.map((image, index) => (
                        <div key={`image-${image.id || index}`} className="relative group">
                          <img
                            src={getFileUrl('product-images', image.url)}
                            alt={image.alt_text || 'Product image'}
                            className="w-full h-20 object-cover rounded border"
                            onError={(e) => {
                              // Fallback to placeholder if image fails to load
                              const target = e.target as HTMLImageElement
                              target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjI0IiBoZWlnaHQ9IjI0IiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0xMiA5VjEzTTEyIDE3SDE2TTE2IDlIMTJNMTIgOUg4VjEzSDEyVjlaTTggMTNWMTdIMTJWMTNIOFoiIHN0cm9rZT0iIzlDQTNBRiIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPC9zdmc+'
                              target.className = 'w-full h-20 object-contain rounded border bg-gray-100'
                            }}
                          />
                          {image.is_primary && (
                            <span className="absolute top-1 left-1 bg-blue-500 text-white text-xs px-1 rounded">
                              Primary
                            </span>
                          )}
                          <button
                            type="button"
                            onClick={() => handleDeleteExistingFile('images', image.url, image.id || '')}
                            className="absolute top-1 right-1 bg-red-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs opacity-0 group-hover:opacity-100 transition-opacity"
                          >
                            ×
                          </button>
                        </div>
                      ))}

                      {/* 3D Preview Images as Product Images */}
                      {existingFiles.previewFiles.map((previewFile, index) => (
                        <div key={`preview-${index}`} className="relative group">
                          <img
                            src={getFileUrl('product-files', previewFile.url)}
                            alt={previewFile.filename}
                            className="w-full h-20 object-cover rounded border"
                            onError={(e) => {
                              // Fallback to placeholder if image fails to load
                              const target = e.target as HTMLImageElement
                              target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjI0IiBoZWlnaHQ9IjI0IiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0xMiA5VjEzTTEyIDE3SDE2TTE2IDlIMTJNMTIgOUg4VjEzSDEyVjlaTTggMTNWMTdIMTJWMTNIOFoiIHN0cm9rZT0iIzlDQTNBRiIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPC9zdmc+'
                              target.className = 'w-full h-20 object-contain rounded border bg-gray-100'
                            }}
                          />
                          <span className="absolute top-1 left-1 bg-teal-500 text-white text-xs px-1 rounded">
                            3D Preview
                          </span>
                          <div className="absolute bottom-0 left-0 right-0 bg-black bg-opacity-50 text-white text-xs p-1 rounded-b truncate">
                            {previewFile.filename}
                          </div>
                          <button
                            type="button"
                            onClick={() => handleDeleteExistingFile('previewFiles', previewFile.url)}
                            className="absolute top-1 right-1 bg-red-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs opacity-0 group-hover:opacity-100 transition-opacity"
                          >
                            ×
                          </button>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                <p className="text-xs text-gray-500 mb-2">
                  Upload additional product images. 3D Preview Images from bulk upload are automatically included above.
                </p>
                <input
                  type="file"
                  multiple
                  accept="image/*"
                  onChange={(e) => handleFileChange(e, 'images')}
                  className="w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
                />
                {files.images.length > 0 && (
                  <div className="mt-2">
                    <p className="text-sm text-green-600">{files.images.length} new image(s) selected:</p>
                    <ul className="text-xs text-gray-600 ml-4">
                      {files.images.map((file, index) => (
                        <li key={index}>• {file.name} ({(file.size / 1024 / 1024).toFixed(2)} MB)</li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Print Files (PDF, SVG, PNG, JPG)
                </label>

                {/* Existing Print Files */}
                {existingFiles.printFiles.length > 0 && (
                  <div className="mb-3">
                    <p className="text-sm text-gray-600 mb-2">Current Print Files:</p>
                    <div className="space-y-2">
                      {existingFiles.printFiles.map((file, index) => {
                        const isImageFile = file.filename?.match(/\.(png|jpg|jpeg)$/i)

                        return (
                          <div key={index} className="flex items-center justify-between bg-gray-50 p-2 rounded border">
                            <div className="flex items-center space-x-2">
                              {isImageFile ? (
                                <div className="w-8 h-8 rounded border overflow-hidden flex-shrink-0">
                                  <img
                                    src={getFileUrl('product-files', file.url)}
                                    alt={file.filename}
                                    className="w-full h-full object-cover"
                                    onError={(e) => {
                                      console.error('Failed to load print file image:', file.url, getFileUrl('product-files', file.url))
                                      // Fallback to icon if image fails to load
                                      const target = e.target as HTMLImageElement
                                      target.style.display = 'none'
                                      const parent = target.parentElement
                                      if (parent) {
                                        parent.innerHTML = '<div class="w-full h-full bg-gray-200 flex items-center justify-center text-xs text-gray-500">IMG</div>'
                                      }
                                    }}
                                    onLoad={() => {
                                      console.log('Successfully loaded print file image:', file.url)
                                    }}
                                  />
                                </div>
                              ) : (
                                <div className="w-8 h-8 rounded border bg-gray-100 flex items-center justify-center text-xs text-gray-600">
                                  {file.filename?.includes('.pdf') ? 'PDF' :
                                   file.filename?.includes('.svg') ? 'SVG' : 'FILE'}
                                </div>
                              )}
                              <div>
                                <p className="text-sm font-medium text-gray-900">{file.filename}</p>
                                <p className="text-xs text-gray-900">
                                  {isImageFile ? 'Print image' : 'Print file'}
                                </p>
                              </div>
                            </div>
                            <button
                              type="button"
                              onClick={() => handleDeleteExistingFile('printFiles', file.url)}
                              className="text-red-500 hover:text-red-700 text-sm"
                            >
                              Delete
                            </button>
                          </div>
                        )
                      })}
                    </div>
                  </div>
                )}

                <input
                  type="file"
                  multiple
                  accept=".pdf,.svg,.png,.jpg,.jpeg"
                  onChange={(e) => handleFileChange(e, 'printFiles')}
                  className="w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-semibold file:bg-green-50 file:text-green-700 hover:file:bg-green-100"
                />
                {files.printFiles.length > 0 && (
                  <div className="mt-2">
                    <p className="text-sm text-green-600">{files.printFiles.length} new print file(s) selected:</p>
                    <ul className="text-xs text-gray-600 ml-4">
                      {files.printFiles.map((file, index) => (
                        <li key={index}>• {file.name} ({(file.size / 1024 / 1024).toFixed(2)} MB)</li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Mockup Files
                </label>

                {/* Existing Mockup Files */}
                {existingFiles.mockupFiles.length > 0 && (
                  <div className="mb-3">
                    <p className="text-sm text-gray-600 mb-2">Current Mockup Files:</p>
                    <div className="grid grid-cols-2 gap-2">
                      {existingFiles.mockupFiles.map((file, index) => (
                        <div key={index} className="relative group">
                          <img
                            src={getFileUrl('product-mockups', file.url)}
                            alt={file.filename}
                            className="w-full h-20 object-cover rounded border"
                            onError={(e) => {
                              // Fallback to placeholder if image fails to load
                              const target = e.target as HTMLImageElement
                              target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjI0IiBoZWlnaHQ9IjI0IiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0xMiA5VjEzTTEyIDE3SDE2TTE2IDlIMTJNMTIgOUg4VjEzSDEyVjlaTTggMTNWMTdIMTJWMTNIOFoiIHN0cm9rZT0iIzlDQTNBRiIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPC9zdmc+'
                              target.className = 'w-full h-20 object-contain rounded border bg-gray-100'
                            }}
                          />
                          <div className="absolute bottom-0 left-0 right-0 bg-black bg-opacity-50 text-white text-xs p-1 rounded-b">
                            {file.filename}
                          </div>
                          <button
                            type="button"
                            onClick={() => handleDeleteExistingFile('mockupFiles', file.url)}
                            className="absolute top-1 right-1 bg-red-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs opacity-0 group-hover:opacity-100 transition-opacity"
                          >
                            ×
                          </button>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                <input
                  type="file"
                  multiple
                  accept="image/*"
                  onChange={(e) => handleFileChange(e, 'mockupFiles')}
                  className="w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-semibold file:bg-purple-50 file:text-purple-700 hover:file:bg-purple-100"
                />
                {files.mockupFiles.length > 0 && (
                  <div className="mt-2">
                    <p className="text-sm text-green-600">{files.mockupFiles.length} new mockup file(s) selected:</p>
                    <ul className="text-xs text-gray-600 ml-4">
                      {files.mockupFiles.map((file, index) => (
                        <li key={index}>• {file.name} ({(file.size / 1024 / 1024).toFixed(2)} MB)</li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>

              {/* 3D GLB Files */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  3D GLB Files
                </label>

                {/* Existing GLB Files */}
                {existingFiles.glbFiles.length > 0 && (
                  <div className="mb-3">
                    <p className="text-sm text-gray-600 mb-2">Current GLB Files:</p>
                    <div className="space-y-2">
                      {existingFiles.glbFiles.map((file, index) => (
                        <div key={index} className="flex items-center justify-between bg-gray-50 p-2 rounded border">
                          <div className="flex items-center space-x-2">
                            <div className="w-8 h-8 bg-blue-100 rounded flex items-center justify-center">
                              <span className="text-blue-600 text-xs font-bold">3D</span>
                            </div>
                            <div>
                              <p className="text-sm font-medium text-gray-900">{file.filename}</p>
                              <p className="text-xs text-gray-500">GLB File</p>
                            </div>
                          </div>
                          <button
                            type="button"
                            onClick={() => handleDeleteExistingFile('glbFiles', file.url)}
                            className="text-red-500 hover:text-red-700 text-sm"
                          >
                            Delete
                          </button>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                <input
                  type="file"
                  multiple
                  accept=".glb,.gltf"
                  onChange={(e) => handleFileChange(e, 'glbFiles')}
                  className="w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
                />
                {files.glbFiles.length > 0 && (
                  <div className="mt-2">
                    <p className="text-sm text-green-600">{files.glbFiles.length} new GLB file(s) selected:</p>
                    <ul className="text-xs text-gray-600 ml-4">
                      {files.glbFiles.map((file, index) => (
                        <li key={index}>• {file.name} ({(file.size / 1024 / 1024).toFixed(2)} MB)</li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>

              {/* Texture Files */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Texture Files
                </label>

                {/* Existing Texture Files */}
                {existingFiles.textureFiles.length > 0 && (
                  <div className="mb-3">
                    <p className="text-sm text-gray-600 mb-2">Current Texture Files:</p>
                    <div className="grid grid-cols-2 gap-2">
                      {existingFiles.textureFiles.map((file, index) => (
                        <div key={index} className="relative group">
                          <img
                            src={getFileUrl('product-files', file.url)}
                            alt={file.filename}
                            className="w-full h-20 object-cover rounded border"
                            onError={(e) => {
                              const target = e.target as HTMLImageElement;
                              target.style.display = 'none';
                              target.nextElementSibling!.classList.remove('hidden');
                            }}
                          />
                          <div className="hidden w-full h-20 bg-gray-200 rounded border flex items-center justify-center">
                            <span className="text-gray-500 text-xs">Texture</span>
                          </div>
                          <div className="absolute bottom-0 left-0 right-0 bg-black bg-opacity-50 text-white text-xs p-1 rounded-b">
                            {file.filename}
                          </div>
                          <button
                            type="button"
                            onClick={() => handleDeleteExistingFile('textureFiles', file.url)}
                            className="absolute top-1 right-1 bg-red-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs opacity-0 group-hover:opacity-100 transition-opacity"
                          >
                            ×
                          </button>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                <input
                  type="file"
                  multiple
                  accept=".png,.jpg,.jpeg"
                  onChange={(e) => handleFileChange(e, 'textureFiles')}
                  className="w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-semibold file:bg-orange-50 file:text-orange-700 hover:file:bg-orange-100"
                />
                {files.textureFiles.length > 0 && (
                  <div className="mt-2">
                    <p className="text-sm text-green-600">{files.textureFiles.length} new texture file(s) selected:</p>
                    <ul className="text-xs text-gray-600 ml-4">
                      {files.textureFiles.map((file, index) => (
                        <li key={index}>• {file.name} ({(file.size / 1024 / 1024).toFixed(2)} MB)</li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>

              {/* Preview Files */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  3D Preview Images
                </label>

                {/* Existing Preview Files */}
                {existingFiles.previewFiles.length > 0 && (
                  <div className="mb-3">
                    <p className="text-sm text-gray-600 mb-2">Current Preview Files:</p>
                    <div className="grid grid-cols-2 gap-2">
                      {existingFiles.previewFiles.map((file, index) => (
                        <div key={index} className="relative group">
                          <img
                            src={getFileUrl('product-files', file.url)}
                            alt={file.filename}
                            className="w-full h-20 object-cover rounded border"
                          />
                          <div className="absolute bottom-0 left-0 right-0 bg-black bg-opacity-50 text-white text-xs p-1 rounded-b">
                            {file.filename}
                          </div>
                          <button
                            type="button"
                            onClick={() => handleDeleteExistingFile('previewFiles', file.url)}
                            className="absolute top-1 right-1 bg-red-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs opacity-0 group-hover:opacity-100 transition-opacity"
                          >
                            ×
                          </button>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                <input
                  type="file"
                  multiple
                  accept="image/*"
                  onChange={(e) => handleFileChange(e, 'previewFiles')}
                  className="w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-semibold file:bg-teal-50 file:text-teal-700 hover:file:bg-teal-100"
                />
                {files.previewFiles.length > 0 && (
                  <div className="mt-2">
                    <p className="text-sm text-green-600">{files.previewFiles.length} new preview file(s) selected:</p>
                    <ul className="text-xs text-gray-600 ml-4">
                      {files.previewFiles.map((file, index) => (
                        <li key={index}>• {file.name} ({(file.size / 1024 / 1024).toFixed(2)} MB)</li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Product Variants */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h4 className="text-md font-medium text-gray-900">Product Variants</h4>
              <button
                type="button"
                onClick={addVariant}
                className="bg-green-600 text-white px-3 py-1 rounded-md text-sm hover:bg-green-700"
              >
                Add Variant
              </button>
            </div>

            {variants.map((variant, index) => (
              <div key={variant.id} className="border border-gray-200 rounded-lg p-4 space-y-3">
                <div className="flex items-center justify-between">
                  <h5 className="text-sm font-medium text-gray-800">Variant {index + 1}</h5>
                  {variants.length > 1 && (
                    <button
                      type="button"
                      onClick={() => removeVariant(variant.id)}
                      className="text-red-600 hover:text-red-800 text-sm"
                    >
                      Remove
                    </button>
                  )}
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Variant Title
                    </label>
                    <Input
                      value={variant.title}
                      onChange={(e) => updateVariant(variant.id, 'title', e.target.value)}
                      placeholder="e.g., Small Red"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Price ($)
                    </label>
                    <Input
                      type="number"
                      step="0.01"
                      value={variant.price}
                      onChange={(e) => updateVariant(variant.id, 'price', e.target.value)}
                      placeholder="19.99"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Compare at Price ($)
                    </label>
                    <Input
                      type="number"
                      step="0.01"
                      value={variant.compareAtPrice}
                      onChange={(e) => updateVariant(variant.id, 'compareAtPrice', e.target.value)}
                      placeholder="24.99"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      SKU
                    </label>
                    <Input
                      value={variant.sku}
                      onChange={(e) => updateVariant(variant.id, 'sku', e.target.value)}
                      placeholder="TSH-001-SM-RED"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Inventory Quantity
                    </label>
                    <Input
                      type="number"
                      value={variant.inventoryQuantity}
                      onChange={(e) => updateVariant(variant.id, 'inventoryQuantity', e.target.value)}
                      placeholder="100"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Weight (lbs)
                    </label>
                    <Input
                      type="number"
                      step="0.01"
                      value={variant.weight}
                      onChange={(e) => updateVariant(variant.id, 'weight', e.target.value)}
                      placeholder="0.5"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Option 1 (e.g., Size)
                    </label>
                    <Input
                      value={variant.option1}
                      onChange={(e) => updateVariant(variant.id, 'option1', e.target.value)}
                      placeholder="Small"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Option 2 (e.g., Color)
                    </label>
                    <Input
                      value={variant.option2}
                      onChange={(e) => updateVariant(variant.id, 'option2', e.target.value)}
                      placeholder="Red"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Option 3 (e.g., Material)
                    </label>
                    <Input
                      value={variant.option3}
                      onChange={(e) => updateVariant(variant.id, 'option3', e.target.value)}
                      placeholder="Cotton"
                    />
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Options */}
          <div className="flex items-center">
            <input
              type="checkbox"
              name="featured"
              checked={formData.featured}
              onChange={handleInputChange}
              className="h-4 w-4 text-blue-600 border-gray-300 rounded"
            />
            <label className="ml-2 text-sm text-gray-700">
              Featured Product
            </label>
          </div>

          {/* Actions */}
          <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              disabled={isLoading}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isLoading}
            >
              {isLoading ? 'Updating...' : 'Update Product'}
            </Button>
          </div>
        </form>
      </div>
    </div>
  )
}
