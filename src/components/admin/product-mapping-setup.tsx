'use client'

import { useState, useRef, useEffect } from 'react'
import { createSupabaseBrowserClient } from '@/lib/supabase'
import { MappingArea, createRectanglePath, calculateDisplayDimensions } from '@/lib/svg-mapping-engine'
import SimpleVisualMapping from './simple-visual-mapping'

interface Product {
  id: string
  name: string
  canvas_config?: any
  print_files?: Array<{
    url: string
    filename: string
  }>
  mockup_files?: Array<{
    url: string
    filename: string
  }>
}

interface ProductMappingSetupProps {
  product: Product
  onClose: () => void
  onSuccess: () => void
}

interface DrawingArea {
  id: string
  name: string
  x: number
  y: number
  width: number
  height: number
  isDrawing: boolean
}

export default function ProductMappingSetup({ product, onClose, onSuccess }: ProductMappingSetupProps) {
  // Use the new simple visual mapping interface
  return (
    <SimpleVisualMapping
      product={product}
      onClose={onClose}
      onSuccess={onSuccess}
    />
  )
}
