'use client'

import { useState, useRef } from 'react'
import { X, Upload, FolderOpen, Image, FileText, Box, Palette, GripVertical } from 'lucide-react'

interface ParsedFiles {
  glbFiles: File[]
  printFiles: File[]
  previewFiles: File[]
  textureFiles: File[]
  productImages: File[]
}

interface FileMapping {
  glb: File
  preview?: File
  texture?: File
  name: string
}

interface BulkUploadModalProps {
  isOpen: boolean
  onClose: () => void
  onSuccess: () => void
  categories: Array<{ id: string; name: string }>
}

export default function BulkUploadModal({ isOpen, onClose, onSuccess, categories }: BulkUploadModalProps) {
  const [step, setStep] = useState<'select' | 'preview' | 'upload'>('select')
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState('')
  const [uploadProgress, setUploadProgress] = useState(0)
  const [parsedFiles, setParsedFiles] = useState<ParsedFiles | null>(null)
  const [fileMappings, setFileMappings] = useState<FileMapping[]>([])
  const [productImages, setProductImages] = useState<File[]>([])
  const fileInputRef = useRef<HTMLInputElement>(null)

  const [productData, setProductData] = useState({
    name: '',
    description: '',
    basePrice: '',
    categoryId: '',
    sku: '',
    weight: '',
    materials: '',
    tags: '',
    careInstructions: '',
    dimensions: '',
    featured: false
  })

  const handleFolderSelect = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || [])
    if (files.length === 0) return

    setIsLoading(true)
    setError('')

    try {
      // Parse folder structure
      const parsed = await parseFiles(files)
      setParsedFiles(parsed)
      
      // Create file mappings
      const mappings = createFileMappings(parsed)
      setFileMappings(mappings)
      
      // Set initial product images (all previews)
      setProductImages([...parsed.previewFiles])
      
      // Extract product name from folder structure
      const folderPath = files[0]?.webkitRelativePath || ''
      const productName = folderPath.split('/')[0] || 'New Product'
      setProductData(prev => ({ ...prev, name: productName }))
      
      setStep('preview')
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to parse folder')
    } finally {
      setIsLoading(false)
    }
  }

  const parseFiles = async (files: File[]): Promise<ParsedFiles> => {
    const parsed: ParsedFiles = {
      glbFiles: [],
      printFiles: [],
      previewFiles: [],
      textureFiles: [],
      productImages: []
    }

    for (const file of files) {
      const path = file.webkitRelativePath.toLowerCase()
      
      // Skip duplicate files with hash suffixes
      if (path.includes('-') && /[a-f0-9]{10,}/.test(path)) continue
      
      // Categorize files by path and extension
      if (path.includes('print files') && file.name.endsWith('.png')) {
        parsed.printFiles.push(file)
      } else if (path.includes('visualmodels') && file.name.endsWith('.glb')) {
        parsed.glbFiles.push(file)
      } else if (path.includes('previews') && file.name.match(/\.(jpg|jpeg|png)$/i)) {
        parsed.previewFiles.push(file)
      } else if (path.includes('texture') && file.name.match(/\.(ktx2|png|jpg|jpeg)$/i)) {
        parsed.textureFiles.push(file)
      }
    }

    return parsed
  }

  const createFileMappings = (parsed: ParsedFiles): FileMapping[] => {
    const mappings: FileMapping[] = []

    for (const glbFile of parsed.glbFiles) {
      const baseName = glbFile.name.replace('.glb', '')
      
      // Find matching preview and texture files
      const preview = parsed.previewFiles.find(f => 
        f.name.toLowerCase().includes(baseName.toLowerCase())
      )
      const texture = parsed.textureFiles.find(f => 
        f.name.toLowerCase().includes(baseName.toLowerCase())
      )

      mappings.push({
        glb: glbFile,
        preview,
        texture,
        name: baseName
      })
    }

    return mappings
  }

  const handleProductImageReorder = (fromIndex: number, toIndex: number) => {
    const newImages = [...productImages]
    const [moved] = newImages.splice(fromIndex, 1)
    newImages.splice(toIndex, 0, moved)
    setProductImages(newImages)
  }

  const handleProductImageRemove = (index: number) => {
    setProductImages(prev => prev.filter((_, i) => i !== index))
  }

  const handleUpload = async () => {
    if (!parsedFiles) return

    setIsLoading(true)
    setError('')
    setStep('upload')
    setUploadProgress(0)

    try {
      // Create FormData with all files and mappings
      const formData = new FormData()
      
      // Add product data
      Object.entries(productData).forEach(([key, value]) => {
        formData.append(key, value.toString())
      })

      // Add file mappings data
      formData.append('fileMappings', JSON.stringify(fileMappings.map(m => ({
        glbName: m.glb.name,
        previewName: m.preview?.name,
        textureName: m.texture?.name
      }))))

      // Add product images order
      formData.append('productImagesOrder', JSON.stringify(productImages.map(f => f.name)))

      // Add all files
      parsedFiles.glbFiles.forEach((file, index) => {
        formData.append(`glbFiles${index}`, file)
      })
      parsedFiles.printFiles.forEach((file, index) => {
        formData.append(`printFiles${index}`, file)
      })
      parsedFiles.previewFiles.forEach((file, index) => {
        formData.append(`previewFiles${index}`, file)
      })
      parsedFiles.textureFiles.forEach((file, index) => {
        formData.append(`textureFiles${index}`, file)
      })

      // Upload with progress tracking
      const response = await fetch('/api/admin/products/bulk-upload', {
        method: 'POST',
        body: formData
      })

      if (!response.ok) {
        const result = await response.json()
        throw new Error(result.error || 'Upload failed')
      }

      setUploadProgress(100)
      onSuccess()
      onClose()
      
      // Reset form
      setStep('select')
      setParsedFiles(null)
      setFileMappings([])
      setProductImages([])
      setProductData({
        name: '',
        description: '',
        basePrice: '',
        categoryId: '',
        sku: '',
        weight: '',
        materials: '',
        tags: '',
        careInstructions: '',
        dimensions: '',
        featured: false
      })

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Upload failed')
      setStep('preview')
    } finally {
      setIsLoading(false)
    }
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-6xl max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">
            Bulk Upload Product
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-140px)]">
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-md p-3 mb-4">
              <p className="text-sm text-red-600">{error}</p>
            </div>
          )}

          {step === 'select' && (
            <div className="text-center py-12">
              <FolderOpen className="w-16 h-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Select Product Folder
              </h3>
              <p className="text-gray-600 mb-6">
                Choose a folder containing your product files with the standard structure
              </p>
              
              <input
                ref={fileInputRef}
                type="file"
                {...({ webkitdirectory: "" } as any)}
                multiple
                onChange={handleFolderSelect}
                className="hidden"
              />
              
              <button
                onClick={() => fileInputRef.current?.click()}
                disabled={isLoading}
                className="bg-blue-600 text-white px-6 py-3 rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center mx-auto"
              >
                <Upload className="w-5 h-5 mr-2" />
                {isLoading ? 'Processing...' : 'Select Folder'}
              </button>
            </div>
          )}

          {step === 'preview' && parsedFiles && (
            <div className="space-y-6">
              {/* File Summary */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="bg-blue-50 p-4 rounded-lg text-center">
                  <Box className="w-8 h-8 text-blue-600 mx-auto mb-2" />
                  <div className="text-2xl font-bold text-blue-600">{parsedFiles.glbFiles.length}</div>
                  <div className="text-sm text-blue-600">GLB Files</div>
                </div>
                <div className="bg-green-50 p-4 rounded-lg text-center">
                  <FileText className="w-8 h-8 text-green-600 mx-auto mb-2" />
                  <div className="text-2xl font-bold text-green-600">{parsedFiles.printFiles.length}</div>
                  <div className="text-sm text-green-600">Print Files</div>
                </div>
                <div className="bg-purple-50 p-4 rounded-lg text-center">
                  <Image className="w-8 h-8 text-purple-600 mx-auto mb-2" />
                  <div className="text-2xl font-bold text-purple-600">{parsedFiles.previewFiles.length}</div>
                  <div className="text-sm text-purple-600">Previews</div>
                </div>
                <div className="bg-orange-50 p-4 rounded-lg text-center">
                  <Palette className="w-8 h-8 text-orange-600 mx-auto mb-2" />
                  <div className="text-2xl font-bold text-orange-600">{parsedFiles.textureFiles.length}</div>
                  <div className="text-sm text-orange-600">Textures</div>
                </div>
              </div>

              {/* Product Information */}
              <div className="bg-gray-50 p-4 rounded-lg">
                <h4 className="font-medium text-gray-900 mb-4">Product Information</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Product Name *</label>
                    <input
                      type="text"
                      value={productData.name}
                      onChange={(e) => setProductData(prev => ({ ...prev, name: e.target.value }))}
                      className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm text-gray-900"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Base Price * ($)</label>
                    <input
                      type="number"
                      step="0.01"
                      value={productData.basePrice}
                      onChange={(e) => setProductData(prev => ({ ...prev, basePrice: e.target.value }))}
                      className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm text-gray-900"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Category</label>
                    <select
                      value={productData.categoryId}
                      onChange={(e) => setProductData(prev => ({ ...prev, categoryId: e.target.value }))}
                      className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm text-gray-900"
                    >
                      <option value="">Select Category</option>
                      {categories.map(category => (
                        <option key={category.id} value={category.id}>{category.name}</option>
                      ))}
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">SKU</label>
                    <input
                      type="text"
                      value={productData.sku}
                      onChange={(e) => setProductData(prev => ({ ...prev, sku: e.target.value }))}
                      className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm text-gray-900"
                    />
                  </div>
                </div>
                <div className="mt-4">
                  <label className="block text-sm font-medium text-gray-700 mb-1">Description</label>
                  <textarea
                    value={productData.description}
                    onChange={(e) => setProductData(prev => ({ ...prev, description: e.target.value }))}
                    rows={3}
                    className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm text-gray-900"
                  />
                </div>
              </div>

              {/* File Mappings */}
              <div>
                <h4 className="font-medium text-gray-900 mb-4">File Mappings</h4>
                <div className="space-y-3">
                  {fileMappings.map((mapping, index) => (
                    <div key={index} className="bg-white border border-gray-200 rounded-lg p-4">
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">GLB File</label>
                          <div className="flex items-center space-x-2">
                            <Box className="w-4 h-4 text-blue-600" />
                            <span className="text-sm text-gray-900">{mapping.glb.name}</span>
                          </div>
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">Preview</label>
                          <div className="flex items-center space-x-2">
                            <Image className="w-4 h-4 text-purple-600" />
                            <span className="text-sm text-gray-900">{mapping.preview?.name || 'None'}</span>
                          </div>
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">Texture</label>
                          <div className="flex items-center space-x-2">
                            <Palette className="w-4 h-4 text-orange-600" />
                            <span className="text-sm text-gray-900">{mapping.texture?.name || 'None'}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Product Images Management */}
              <div>
                <h4 className="font-medium text-gray-900 mb-4">Product Images ({productImages.length})</h4>
                <p className="text-sm text-gray-600 mb-4">
                  These images will be used for product display. Drag to reorder, click × to remove.
                </p>
                <div className="space-y-2">
                  {productImages.map((file, index) => (
                    <div
                      key={index}
                      className="flex items-center space-x-3 bg-white border border-gray-200 rounded-lg p-3 group hover:bg-gray-50"
                      draggable
                      onDragStart={(e) => {
                        e.dataTransfer.setData('text/plain', index.toString())
                      }}
                      onDragOver={(e) => {
                        e.preventDefault()
                      }}
                      onDrop={(e) => {
                        e.preventDefault()
                        const fromIndex = parseInt(e.dataTransfer.getData('text/plain'))
                        if (fromIndex !== index) {
                          handleProductImageReorder(fromIndex, index)
                        }
                      }}
                    >
                      <GripVertical className="w-4 h-4 text-gray-400 cursor-move" />
                      <div className="w-12 h-12 bg-gray-100 rounded border flex items-center justify-center flex-shrink-0">
                        <Image className="w-6 h-6 text-gray-400" />
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-gray-900 truncate">{file.name}</p>
                        <p className="text-xs text-gray-500">
                          {(file.size / 1024 / 1024).toFixed(2)} MB
                          {index === 0 && <span className="ml-2 text-blue-600 font-medium">• Primary Image</span>}
                        </p>
                      </div>
                      <div className="flex items-center space-x-2">
                        {index === 0 && (
                          <span className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">
                            Primary
                          </span>
                        )}
                        <button
                          type="button"
                          onClick={() => handleProductImageRemove(index)}
                          className="text-red-500 hover:text-red-700 p-1 rounded opacity-0 group-hover:opacity-100 transition-opacity"
                        >
                          <X className="w-4 h-4" />
                        </button>
                      </div>
                    </div>
                  ))}
                  {productImages.length === 0 && (
                    <div className="text-center py-8 text-gray-500">
                      <Image className="w-12 h-12 mx-auto mb-2 text-gray-300" />
                      <p className="text-sm">No product images selected</p>
                      <p className="text-xs">Preview files will be used as product images by default</p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}

          {step === 'upload' && (
            <div className="text-center py-12">
              <Upload className="w-16 h-16 text-blue-600 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Uploading Product...
              </h3>
              <div className="w-full max-w-md mx-auto bg-gray-200 rounded-full h-2 mb-4">
                <div
                  className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${uploadProgress}%` }}
                />
              </div>
              <p className="text-gray-600">{uploadProgress}% complete</p>
            </div>
          )}
        </div>

        {/* Footer */}
        {step === 'preview' && (
          <div className="flex items-center justify-between p-6 border-t border-gray-200">
            <button
              onClick={() => setStep('select')}
              className="text-gray-600 hover:text-gray-800"
            >
              Back
            </button>
            <div className="flex space-x-3">
              <button
                onClick={onClose}
                className="px-4 py-2 text-gray-600 hover:text-gray-800"
              >
                Cancel
              </button>
              <button
                onClick={handleUpload}
                disabled={!productData.name || !productData.basePrice || isLoading}
                className="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Upload Product
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
