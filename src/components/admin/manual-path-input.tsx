'use client'

import { useState } from 'react'
import { createSupabaseBrowserClient } from '@/lib/supabase'

interface ManualPathInputProps {
  productId: string
  viewType: 'mockup' | 'printfile'
  onPathAdded: () => void
}

interface PathInput {
  areaId: string
  areaName: string
  pathData: string
}

export default function ManualPathInput({ 
  productId, 
  viewType, 
  onPathAdded 
}: ManualPathInputProps) {
  const [pathInput, setPathInput] = useState<PathInput>({
    areaId: '',
    areaName: '',
    pathData: ''
  })
  const [isAdding, setIsAdding] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState<string | null>(null)
  
  const supabase = createSupabaseBrowserClient()

  const validateSVGPath = (pathData: string): boolean => {
    try {
      // Basic SVG path validation
      if (!pathData.trim()) return false

      // Check for basic SVG path commands
      const validCommands = /^[MmLlHhVvCcSsQqTtAaZz0-9\s,.-]+$/
      if (!validCommands.test(pathData)) return false

      // Must start with a move command
      if (!/^[Mm]/.test(pathData.trim())) return false

      return true
    } catch {
      return false
    }
  }

  const calculatePathBounds = (pathData: string) => {
    if (!validateSVGPath(pathData)) {
      return { x: 0, y: 0, width: 0, height: 0 }
    }

    // Simple bounds calculation - extract all coordinate pairs
    const coords = pathData.match(/[-+]?[0-9]*\.?[0-9]+/g)?.map(Number) || []

    if (coords.length < 2) {
      return { x: 0, y: 0, width: 0, height: 0 }
    }

    const xCoords = coords.filter((_, i) => i % 2 === 0)
    const yCoords = coords.filter((_, i) => i % 2 === 1)

    const minX = Math.min(...xCoords)
    const maxX = Math.max(...xCoords)
    const minY = Math.min(...yCoords)
    const maxY = Math.max(...yCoords)

    return {
      x: minX,
      y: minY,
      width: maxX - minX,
      height: maxY - minY
    }
  }

  const handleAddPath = async () => {
    if (!pathInput.areaId.trim() || !pathInput.areaName.trim() || !pathInput.pathData.trim()) {
      setError('Please fill in all fields')
      return
    }

    if (!validateSVGPath(pathInput.pathData)) {
      setError('Invalid SVG path data. Please check your path syntax.')
      return
    }

    setIsAdding(true)
    setError(null)
    setSuccess(null)

    try {
      // Calculate bounds for the path
      const bounds = calculatePathBounds(pathInput.pathData)

      // Create mapping area record
      const boundsJson = {
        x: bounds.x,
        y: bounds.y,
        width: bounds.width,
        height: bounds.height
      }

      const mappingArea = {
        product_id: productId,
        area_id: pathInput.areaId.toLowerCase().replace(/\s+/g, '-'),
        area_name: pathInput.areaName,
        view_type: viewType,
        svg_path: pathInput.pathData,
        svg_element_id: pathInput.areaId.toLowerCase().replace(/\s+/g, '-'),
        svg_path_data: pathInput.pathData,
        is_active: true,
        sort_order: 0,
        display_bounds: boundsJson,
        print_bounds: boundsJson
      }

      // Save to database
      const { error: dbError } = await supabase
        .from('product_mapping_areas')
        .upsert(mappingArea, {
          onConflict: 'product_id,area_id,view_type'
        })

      if (dbError) throw dbError

      setSuccess(`Successfully added "${pathInput.areaName}" mapping area!`)
      
      // Clear form
      setPathInput({
        areaId: '',
        areaName: '',
        pathData: ''
      })

      // Notify parent component
      onPathAdded()

    } catch (err: any) {
      console.error('Error adding path:', err)
      setError(`Failed to add path: ${err.message}`)
    } finally {
      setIsAdding(false)
    }
  }

  const handlePresetPath = (preset: string) => {
    const presets = {
      'front-rect': {
        areaId: 'front',
        areaName: 'Front Design Area',
        pathData: 'M 200,150 L 400,150 L 400,300 L 200,300 Z'
      },
      'back-rect': {
        areaId: 'back',
        areaName: 'Back Design Area', 
        pathData: 'M 200,350 L 400,350 L 400,500 L 200,500 Z'
      },
      'logo-circle': {
        areaId: 'logo',
        areaName: 'Logo Area',
        pathData: 'M 270,100 A 30,30 0 1,1 330,100 A 30,30 0 1,1 270,100 Z'
      }
    }

    if (presets[preset as keyof typeof presets]) {
      setPathInput(presets[preset as keyof typeof presets])
    }
  }

  return (
    <div className="space-y-4">
      <div>
        <h4 className="text-lg font-medium text-gray-900 mb-2">
          Manual Path Input ({viewType === 'mockup' ? 'Mockup' : 'Print File'} View)
        </h4>
        <p className="text-sm text-gray-600">
          Directly input SVG path data and area information
        </p>
      </div>

      {/* Preset Buttons */}
      <div className="flex flex-wrap gap-2">
        <button
          onClick={() => handlePresetPath('front-rect')}
          className="px-3 py-1 text-xs bg-blue-100 text-blue-700 rounded hover:bg-blue-200"
        >
          Front Rectangle
        </button>
        <button
          onClick={() => handlePresetPath('back-rect')}
          className="px-3 py-1 text-xs bg-green-100 text-green-700 rounded hover:bg-green-200"
        >
          Back Rectangle
        </button>
        <button
          onClick={() => handlePresetPath('logo-circle')}
          className="px-3 py-1 text-xs bg-purple-100 text-purple-700 rounded hover:bg-purple-200"
        >
          Logo Circle
        </button>
      </div>

      {/* Form Fields */}
      <div className="grid grid-cols-1 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-900 mb-1">
            Area ID
          </label>
          <input
            type="text"
            value={pathInput.areaId}
            onChange={(e) => setPathInput(prev => ({ ...prev, areaId: e.target.value }))}
            placeholder="e.g., front, back, logo"
            className="w-full px-3 py-2 border border-gray-300 rounded-md bg-white text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
          <p className="text-xs text-gray-600 mt-1">
            Use the same ID in both mockup and print file views to link them
          </p>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-900 mb-1">
            Area Name
          </label>
          <input
            type="text"
            value={pathInput.areaName}
            onChange={(e) => setPathInput(prev => ({ ...prev, areaName: e.target.value }))}
            placeholder="e.g., Front Design Area"
            className="w-full px-3 py-2 border border-gray-300 rounded-md bg-white text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-900 mb-1">
            SVG Path Data
          </label>
          <textarea
            value={pathInput.pathData}
            onChange={(e) => setPathInput(prev => ({ ...prev, pathData: e.target.value }))}
            placeholder="e.g., M 100,100 L 300,100 L 300,200 L 100,200 Z"
            rows={3}
            className="w-full px-3 py-2 border border-gray-300 rounded-md bg-white text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-mono text-sm"
          />
          <p className="text-xs text-gray-600 mt-1">
            SVG path data (d attribute). Supports M, L, A, Q, C, Z commands
          </p>
        </div>
      </div>

      {/* Path Preview */}
      {pathInput.pathData && (
        <div>
          <h5 className="text-sm font-medium text-gray-700 mb-2">Path Preview:</h5>
          <div className="border border-gray-300 rounded p-2 bg-gray-50">
            {validateSVGPath(pathInput.pathData) ? (
              <svg viewBox="0 0 500 400" className="w-full h-32 border border-gray-200 rounded bg-white">
                <path
                  d={pathInput.pathData}
                  fill="rgba(59, 130, 246, 0.2)"
                  stroke="#3b82f6"
                  strokeWidth="2"
                />
              </svg>
            ) : (
              <div className="w-full h-32 border border-gray-200 rounded bg-white flex items-center justify-center text-red-500 text-sm">
                Invalid SVG path data. Please check your path syntax.
              </div>
            )}
          </div>
        </div>
      )}

      {/* Messages */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded p-3 text-red-700 text-sm">
          <strong>Error:</strong> {error}
        </div>
      )}

      {success && (
        <div className="bg-green-50 border border-green-200 rounded p-3 text-green-700 text-sm">
          <strong>Success:</strong> {success}
        </div>
      )}

      {/* Add Button */}
      <button
        onClick={handleAddPath}
        disabled={isAdding || !pathInput.areaId.trim() || !pathInput.areaName.trim() || !pathInput.pathData.trim()}
        className="w-full px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
      >
        {isAdding ? 'Adding Path...' : 'Add Mapping Area'}
      </button>

      {/* Instructions */}
      <div className="bg-blue-50 border border-blue-200 rounded p-3 text-sm">
        <h6 className="font-medium text-blue-900 mb-1">Instructions:</h6>
        <ul className="text-blue-700 space-y-1 text-xs">
          <li>• Use the same Area ID in both mockup and print file views to link them</li>
          <li>• SVG path data should be valid (test with the preview above)</li>
          <li>• Bounds are calculated automatically from the path coordinates</li>
          <li>• You can use preset buttons for common shapes</li>
        </ul>
      </div>
    </div>
  )
}
