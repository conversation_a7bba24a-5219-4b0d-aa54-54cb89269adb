'use client'

import { useState, useEffect } from 'react'
import { createSupabaseBrowserClient } from '@/lib/supabase'

interface SVGMappingVisualizerProps {
  productId: string
  viewType: 'mockup' | 'printfile'
  onAreaSelect?: (area: any) => void
}

interface MappingArea {
  id: string
  area_id: string
  area_name: string
  svg_path: string
  view_type: string
  product_id: string
  display_bounds: any
  print_bounds: any
  is_active: boolean | null
  sort_order: number | null
  created_at: string | null
  updated_at: string | null
  transform_matrix: any
}

interface SVGConfig {
  svg_content: string
  svg_viewbox: string
}

// Helper function to extract bounds from JSON
const getBounds = (area: MappingArea) => {
  const bounds = area.display_bounds as any
  if (bounds && typeof bounds === 'object') {
    return {
      x: bounds.x || 0,
      y: bounds.y || 0,
      width: bounds.width || 0,
      height: bounds.height || 0
    }
  }
  return { x: 0, y: 0, width: 0, height: 0 }
}

export default function SVGMappingVisualizer({
  productId, 
  viewType, 
  onAreaSelect 
}: SVGMappingVisualizerProps) {
  const [svgConfig, setSvgConfig] = useState<SVGConfig | null>(null)
  const [mappingAreas, setMappingAreas] = useState<MappingArea[]>([])
  const [selectedArea, setSelectedArea] = useState<string | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  
  const supabase = createSupabaseBrowserClient()

  useEffect(() => {
    loadSVGConfig()
    loadMappingAreas()
  }, [productId, viewType])

  const loadSVGConfig = async () => {
    try {
      // Note: SVG config would be loaded from a different source
      // For now, we'll just log that this feature is not implemented
      console.log('SVG config loading not implemented - table does not exist')
      setSvgConfig(null)
    } catch (err: any) {
      console.error('Error loading SVG config:', err)
      setError(`Failed to load SVG config: ${err instanceof Error ? err.message : String(err)}`)
    }
  }

  const loadMappingAreas = async () => {
    try {
      const { data, error } = await supabase
        .from('product_mapping_areas')
        .select('*')
        .eq('product_id', productId)
        .eq('view_type', viewType)
        .eq('is_active', true)
        .order('sort_order')

      if (error) throw error
      
      setMappingAreas(data || [])
    } catch (err: any) {
      console.error('Error loading mapping areas:', err)
      setError(`Failed to load mapping areas: ${err.message}`)
    } finally {
      setLoading(false)
    }
  }

  const handleAreaClick = (area: MappingArea) => {
    setSelectedArea(area.area_id)
    if (onAreaSelect) {
      onAreaSelect(area)
    }
  }

  const toggleAreaActive = async (area: MappingArea) => {
    try {
      const { error } = await supabase
        .from('product_mapping_areas')
        .update({ is_active: !area.is_active })
        .eq('id', area.id)

      if (error) throw error
      
      // Reload areas
      loadMappingAreas()
    } catch (err: any) {
      console.error('Error toggling area:', err)
      setError(`Failed to toggle area: ${err.message}`)
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-gray-500">Loading SVG mapping...</div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded p-4 text-red-700">
        <strong>Error:</strong> {error}
      </div>
    )
  }

  if (!svgConfig) {
    return (
      <div className="bg-yellow-50 border border-yellow-200 rounded p-4 text-yellow-700">
        <strong>No SVG mapping found.</strong> Please upload an SVG file for the {viewType} view.
      </div>
    )
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold text-gray-900">
          {viewType === 'mockup' ? 'Mockup' : 'Print File'} Mapping Areas
        </h3>
        <div className="text-sm text-gray-500">
          {mappingAreas.length} areas found
        </div>
      </div>

      {/* SVG Visualization */}
      <div className="border border-gray-300 rounded-lg p-4 bg-white">
        <div className="relative">
          <svg
            viewBox={svgConfig.svg_viewbox}
            className="w-full h-64 border border-gray-200 rounded"
            style={{ maxHeight: '400px' }}
          >
            {/* Render original SVG content */}
            <g dangerouslySetInnerHTML={{ __html: svgConfig.svg_content.replace(/<svg[^>]*>|<\/svg>/g, '') }} />
            
            {/* Overlay mapping areas */}
            {mappingAreas.map((area) => (
              <g key={area.area_id}>
                {/* Area path */}
                <path
                  d={area.svg_path}
                  fill={selectedArea === area.area_id ? 'rgba(59, 130, 246, 0.3)' : 'rgba(34, 197, 94, 0.2)'}
                  stroke={selectedArea === area.area_id ? '#3b82f6' : '#22c55e'}
                  strokeWidth="2"
                  className="cursor-pointer hover:fill-opacity-50"
                  onClick={() => handleAreaClick(area)}
                />
                
                {/* Area label */}
                {(() => {
                  const bounds = getBounds(area)
                  return bounds.x !== 0 || bounds.y !== 0 ? (
                    <text
                      x={bounds.x + bounds.width / 2}
                      y={bounds.y + bounds.height / 2}
                      textAnchor="middle"
                      dominantBaseline="middle"
                      className="text-xs font-medium fill-gray-700 pointer-events-none"
                      style={{ fontSize: '12px' }}
                    >
                      {area.area_name}
                    </text>
                  ) : null
                })()}
              </g>
            ))}
          </svg>
        </div>
      </div>

      {/* Mapping Areas List */}
      <div className="space-y-2">
        <h4 className="text-sm font-medium text-gray-700">Mapping Areas:</h4>
        {mappingAreas.length === 0 ? (
          <div className="text-sm text-gray-500 italic">
            No mapping areas found. Upload an SVG file and extract areas.
          </div>
        ) : (
          <div className="space-y-1">
            {mappingAreas.map((area) => (
              <div
                key={area.area_id}
                className={`flex items-center justify-between p-2 rounded border cursor-pointer transition-colors ${
                  selectedArea === area.area_id
                    ? 'bg-blue-50 border-blue-200'
                    : 'bg-gray-50 border-gray-200 hover:bg-gray-100'
                }`}
                onClick={() => handleAreaClick(area)}
              >
                <div className="flex items-center space-x-3">
                  <div className={`w-3 h-3 rounded-full ${area.is_active ? 'bg-green-500' : 'bg-gray-400'}`} />
                  <div>
                    <div className="text-sm font-medium text-gray-900">{area.area_name}</div>
                    <div className="text-xs text-gray-500">
                      {(() => {
                        const bounds = getBounds(area)
                        return bounds.width && bounds.height
                          ? `${bounds.width.toFixed(0)}×${bounds.height.toFixed(0)}px`
                          : 'No bounds data'
                      })()}
                    </div>
                  </div>
                </div>
                
                <button
                  onClick={(e) => {
                    e.stopPropagation()
                    toggleAreaActive(area)
                  }}
                  className={`px-2 py-1 text-xs rounded ${
                    area.is_active
                      ? 'bg-green-100 text-green-700 hover:bg-green-200'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  }`}
                >
                  {area.is_active ? 'Active' : 'Inactive'}
                </button>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  )
}
