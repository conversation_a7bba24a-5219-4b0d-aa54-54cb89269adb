'use client'

import { useState, useEffect, useRef } from 'react'
import { createSupabaseBrowserClient } from '@/lib/supabase'

interface SimpleVisualMappingProps {
  product: any
  onClose: () => void
  onSuccess: () => void
}

interface MappingArea {
  id: string
  area_id: string
  area_name: string
  svg_path: string
  view_type: string
  product_id: string
  display_bounds: any
  print_bounds: any
  is_active: boolean | null
  sort_order: number | null
  created_at: string | null
  updated_at: string | null
  transform_matrix: any
}

interface CanvasConfig {
  width: number
  height: number
  dpi: number
}

// Helper function to extract bounds from JSON
const getBounds = (area: MappingArea) => {
  const bounds = area.display_bounds as any
  if (bounds && typeof bounds === 'object') {
    return {
      x: bounds.x || 0,
      y: bounds.y || 0,
      width: bounds.width || 0,
      height: bounds.height || 0
    }
  }
  return { x: 0, y: 0, width: 0, height: 0 }
}

export default function SimpleVisualMapping({ product, onClose, onSuccess }: SimpleVisualMappingProps) {
  // Extract canvas config from JSON field or use defaults
  const productCanvasConfig = (product as any).canvas_config
  const defaultWidth = productCanvasConfig?.print_file?.width_px || productCanvasConfig?.display?.width_px || 3000
  const defaultHeight = productCanvasConfig?.print_file?.height_px || productCanvasConfig?.display?.height_px || 4000
  const defaultDpi = productCanvasConfig?.print_file?.dpi || 300

  const [canvasConfig, setCanvasConfig] = useState<CanvasConfig>({
    width: defaultWidth,
    height: defaultHeight,
    dpi: defaultDpi
  })
  
  const [mappingAreas, setMappingAreas] = useState<MappingArea[]>([])
  const [selectedArea, setSelectedArea] = useState<string | null>(null)
  const [isAddingArea, setIsAddingArea] = useState<{ view: 'mockup' | 'printfile' | null }>({ view: null })
  const [newAreaForm, setNewAreaForm] = useState({
    name: '',
    path: ''
  })

  const [mockupImage, setMockupImage] = useState<string | null>(null)
  const [printFileImage, setPrintFileImage] = useState<string | null>(null)
  const [isSavingConfig, setIsSavingConfig] = useState(false)
  const [configSaveMessage, setConfigSaveMessage] = useState<string | null>(null)
  
  const supabase = createSupabaseBrowserClient()
  const mockupCanvasRef = useRef<SVGSVGElement>(null)
  const printFileCanvasRef = useRef<SVGSVGElement>(null)

  useEffect(() => {
    loadCanvasConfig()
    loadMappingAreas()
    loadImages()
  }, [product.id])

  const loadCanvasConfig = async () => {
    try {
      // Load the latest canvas config from database
      const { data, error } = await supabase
        .from('products')
        .select('canvas_config')
        .eq('id', product.id)
        .single()

      if (error) throw error

      if (data && data.canvas_config) {
        const canvasConfigData = data.canvas_config as any
        const newConfig = {
          width: canvasConfigData?.print_file?.width_px || canvasConfigData?.display?.width_px || 3000,
          height: canvasConfigData?.print_file?.height_px || canvasConfigData?.display?.height_px || 4000,
          dpi: canvasConfigData?.print_file?.dpi || 300
        }

        console.log('Loading canvas config from database:', newConfig)
        setCanvasConfig(newConfig)
      }
    } catch (err) {
      console.error('Error loading canvas config:', err)
      // Use defaults if loading fails
      console.log('Using default canvas config:', canvasConfig)
    }
  }

  const loadMappingAreas = async () => {
    try {
      const { data, error } = await supabase
        .from('product_mapping_areas')
        .select('*')
        .eq('product_id', product.id)
        .order('sort_order')

      if (error) throw error
      setMappingAreas(data || [])
    } catch (err) {
      console.error('Error loading mapping areas:', err)
    }
  }

  const loadImages = async () => {
    try {
      console.log('Loading images for product:', product)
      console.log('Product mockup_files:', product.mockup_files)
      console.log('Product print_files:', product.print_files)

      // Load mockup image
      if (product.mockup_files && product.mockup_files.length > 0) {
        const mockupFile = product.mockup_files[0]
        console.log('Loading mockup file:', mockupFile)

        // Try different possible property names for the file path
        const filePath = mockupFile.filePath || mockupFile.file_path || mockupFile.url || mockupFile.path
        const bucket = mockupFile.bucket || 'product-mockups'

        if (filePath) {
          const mockupUrl = supabase.storage
            .from(bucket)
            .getPublicUrl(filePath).data.publicUrl
          console.log('Generated mockup URL:', mockupUrl)

          // Test if the URL is accessible
          const img = new Image()
          img.onload = () => {
            console.log('Mockup image loaded successfully')
            setMockupImage(mockupUrl)
          }
          img.onerror = () => {
            console.error('Failed to load mockup image from URL:', mockupUrl)
            setMockupImage(null)
          }
          img.src = mockupUrl
        } else {
          console.warn('No valid file path found for mockup file:', mockupFile)
        }
      } else {
        console.log('No mockup files found')
      }

      // Load print file image
      if (product.print_files && product.print_files.length > 0) {
        const printFile = product.print_files[0]
        console.log('Loading print file:', printFile)

        // Try different possible property names for the file path
        const filePath = printFile.filePath || printFile.file_path || printFile.url || printFile.path
        const bucket = printFile.bucket || 'product-files'

        if (filePath) {
          const printFileUrl = supabase.storage
            .from(bucket)
            .getPublicUrl(filePath).data.publicUrl
          console.log('Generated print file URL:', printFileUrl)

          // Test if the URL is accessible
          const img = new Image()
          img.onload = () => {
            console.log('Print file image loaded successfully')
            setPrintFileImage(printFileUrl)
          }
          img.onerror = () => {
            console.error('Failed to load print file image from URL:', printFileUrl)
            setPrintFileImage(null)
          }
          img.src = printFileUrl
        } else {
          console.warn('No valid file path found for print file:', printFile)
        }
      } else {
        console.log('No print files found')
      }
    } catch (err) {
      console.error('Error loading images:', err)
    }
  }

  const saveCanvasConfig = async () => {
    setIsSavingConfig(true)
    setConfigSaveMessage(null)

    try {
      console.log('Saving canvas config:', canvasConfig)
      console.log('Product ID:', product.id)

      // First check if the product exists
      const { data: existingProduct, error: checkError } = await supabase
        .from('products')
        .select('id, canvas_config')
        .eq('id', product.id)
        .single()

      if (checkError) {
        console.error('Error checking product:', checkError)
        throw new Error(`Product not found: ${checkError.message}`)
      }

      console.log('Existing product data:', existingProduct)

      // Construct the canvas config JSON
      const canvasConfigJson = {
        print_file: {
          width_px: canvasConfig.width,
          height_px: canvasConfig.height,
          dpi: canvasConfig.dpi
        },
        display: {
          width_px: canvasConfig.width,
          height_px: canvasConfig.height
        }
      }

      // Now update the canvas config
      const { error } = await supabase
        .from('products')
        .update({
          canvas_config: canvasConfigJson
        })
        .eq('id', product.id)

      if (error) {
        console.error('Database update error:', error)
        throw error
      }

      console.log('Canvas config update completed successfully')

      // Verify the update by fetching the data again
      const { data: verifyData, error: verifyError } = await supabase
        .from('products')
        .select('id, canvas_config')
        .eq('id', product.id)
        .single()

      if (verifyError) {
        console.error('Error verifying update:', verifyError)
        throw new Error(`Update verification failed: ${verifyError.message}`)
      }

      console.log('Verified updated data:', verifyData)

      if (verifyData) {
        setConfigSaveMessage('Canvas configuration saved successfully!')
        // Update local state to match saved data
        const savedCanvasConfig = verifyData.canvas_config as any
        setCanvasConfig({
          width: savedCanvasConfig?.print_file?.width_px || savedCanvasConfig?.display?.width_px || 3000,
          height: savedCanvasConfig?.print_file?.height_px || savedCanvasConfig?.display?.height_px || 4000,
          dpi: savedCanvasConfig?.print_file?.dpi || 300
        })
        console.log('Local state updated to match database:', {
          width: savedCanvasConfig?.print_file?.width_px,
          height: savedCanvasConfig?.print_file?.height_px,
          dpi: savedCanvasConfig?.print_file?.dpi
        })
      } else {
        throw new Error('No data returned from verification query')
      }

      // Clear message after 3 seconds
      setTimeout(() => setConfigSaveMessage(null), 3000)

    } catch (err: any) {
      console.error('Error saving canvas config:', err)
      setConfigSaveMessage(`Error saving configuration: ${err.message}`)
    } finally {
      setIsSavingConfig(false)
    }
  }

  const addMappingArea = async () => {
    if (!isAddingArea.view || !newAreaForm.name.trim() || !newAreaForm.path.trim()) {
      return
    }

    try {
      const areaId = newAreaForm.name.toLowerCase().replace(/\s+/g, '-')
      
      // Calculate bounds from path
      const bounds = calculatePathBounds(newAreaForm.path)
      
      const boundsJson = {
        x: bounds.x,
        y: bounds.y,
        width: bounds.width,
        height: bounds.height
      }

      const newArea = {
        product_id: product.id,
        area_id: areaId,
        area_name: newAreaForm.name,
        view_type: isAddingArea.view,
        svg_path: newAreaForm.path,
        is_active: true,
        sort_order: mappingAreas.length,
        display_bounds: boundsJson,
        print_bounds: boundsJson
      }

      const { error } = await supabase
        .from('product_mapping_areas')
        .insert(newArea)

      if (error) throw error

      // Reset form
      setNewAreaForm({ name: '', path: '' })
      setIsAddingArea({ view: null })
      
      // Reload areas
      loadMappingAreas()
    } catch (err) {
      console.error('Error adding mapping area:', err)
    }
  }

  const calculatePathBounds = (pathData: string) => {
    const coords = pathData.match(/[-+]?[0-9]*\.?[0-9]+/g)?.map(Number) || []
    
    if (coords.length < 2) {
      return { x: 0, y: 0, width: 0, height: 0 }
    }
    
    const xCoords = coords.filter((_, i) => i % 2 === 0)
    const yCoords = coords.filter((_, i) => i % 2 === 1)
    
    const minX = Math.min(...xCoords)
    const maxX = Math.max(...xCoords)
    const minY = Math.min(...yCoords)
    const maxY = Math.max(...yCoords)
    
    return {
      x: minX,
      y: minY,
      width: maxX - minX,
      height: maxY - minY
    }
  }

  const deleteArea = async (areaId: string, viewType: 'mockup' | 'printfile') => {
    try {
      const { error } = await supabase
        .from('product_mapping_areas')
        .delete()
        .eq('product_id', product.id)
        .eq('area_id', areaId)
        .eq('view_type', viewType)

      if (error) throw error
      loadMappingAreas()
    } catch (err) {
      console.error('Error deleting area:', err)
    }
  }

  const updateAreaBounds = (areaId: string, field: string, value: number) => {
    setMappingAreas(prev => prev.map(area =>
      area.id === areaId
        ? { ...area, [field]: value }
        : area
    ))
  }

  // Function to calculate bounds from SVG path
  const calculateBoundsFromSVGPath = (svgPath: string) => {
    try {
      // Create a temporary SVG element to calculate path bounds
      const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg')
      const path = document.createElementNS('http://www.w3.org/2000/svg', 'path')
      path.setAttribute('d', svgPath)
      svg.appendChild(path)
      document.body.appendChild(svg)

      const bbox = path.getBBox()
      document.body.removeChild(svg)

      return {
        x: Math.round(bbox.x),
        y: Math.round(bbox.y),
        width: Math.round(bbox.width),
        height: Math.round(bbox.height)
      }
    } catch (error) {
      console.error('Error calculating SVG bounds:', error)
      return { x: 0, y: 0, width: 200, height: 150 }
    }
  }

  const saveAreaBounds = async (area: MappingArea) => {
    try {
      // Calculate display bounds from the SVG path
      const displayBounds = calculateBoundsFromSVGPath(area.svg_path)

      const { error } = await supabase
        .from('product_mapping_areas')
        .update({
          display_bounds: displayBounds,
          print_bounds: displayBounds
        })
        .eq('id', area.id)

      if (error) throw error

      console.log('Auto-calculated and saved display bounds for area:', area.area_name, displayBounds)
      alert(`Display bounds auto-calculated and saved!\nArea: ${area.area_name}\nBounds: ${displayBounds.x}, ${displayBounds.y}, ${displayBounds.width}x${displayBounds.height}`)
      setSelectedArea(null)

      // Refresh the mapping areas
      loadMappingAreas()
    } catch (error) {
      console.error('Error saving area bounds:', error)
      alert('Failed to save area bounds')
    }
  }

  const autoCalculateAllBounds = async () => {
    try {
      let successCount = 0
      let errorCount = 0

      for (const area of mappingAreas) {
        try {
          const displayBounds = calculateBoundsFromSVGPath(area.svg_path)

          const { error } = await supabase
            .from('product_mapping_areas')
            .update({
              display_bounds: displayBounds,
              print_bounds: displayBounds
            })
            .eq('id', area.id)

          if (error) throw error

          console.log('Auto-calculated bounds for:', area.area_name, displayBounds)
          successCount++
        } catch (error) {
          console.error('Error calculating bounds for area:', area.area_name, error)
          errorCount++
        }
      }

      alert(`Auto-calculation complete!\nSuccess: ${successCount} areas\nErrors: ${errorCount} areas`)

      // Refresh the mapping areas
      loadMappingAreas()
    } catch (error) {
      console.error('Error in bulk calculation:', error)
      alert('Failed to auto-calculate bounds')
    }
  }

  const testDatabaseConnection = async () => {
    try {
      console.log('Testing database connection...')
      const { data, error } = await supabase
        .from('products')
        .select('id, name, canvas_config')
        .eq('id', product.id)
        .single()

      if (error) {
        console.error('Database test error:', error)
        return
      }

      console.log('Database test successful:', data)
      const canvasConfig = data.canvas_config as any
      const width = canvasConfig?.print_file?.width_px || canvasConfig?.display?.width_px || 'unknown'
      const height = canvasConfig?.print_file?.height_px || canvasConfig?.display?.height_px || 'unknown'
      const dpi = canvasConfig?.print_file?.dpi || 'unknown'
      alert(`Database test successful!\nProduct: ${data.name}\nCanvas: ${width}×${height}@${dpi}`)
    } catch (err) {
      console.error('Database test failed:', err)
      alert(`Database test failed: ${err instanceof Error ? err.message : String(err)}`)
    }
  }

  const testImageUrls = () => {
    console.log('Testing image URLs...')
    console.log('Current mockup image:', mockupImage)
    console.log('Current print file image:', printFileImage)

    if (mockupImage) {
      window.open(mockupImage, '_blank')
    }
    if (printFileImage) {
      window.open(printFileImage, '_blank')
    }
  }

  const mockupAreas = mappingAreas.filter(area => area.view_type === 'mockup')
  const printFileAreas = mappingAreas.filter(area => area.view_type === 'printfile')

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-7xl h-full max-h-[95vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">Visual Mapping Setup</h2>
            <p className="text-gray-600">{product.name}</p>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 text-2xl"
          >
            ×
          </button>
        </div>

        <div className="flex h-full">
          {/* Main Content */}
          <div className="flex-1 p-6 overflow-y-auto">
            {/* Canvas Configuration */}
            <div className="mb-6 bg-gray-50 p-4 rounded-lg">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Canvas Configuration</h3>
              <div className="grid grid-cols-3 gap-4 mb-4">
                <div>
                  <label className="block text-sm font-medium text-gray-900 mb-1">Width (px)</label>
                  <input
                    type="number"
                    value={canvasConfig.width}
                    onChange={(e) => setCanvasConfig(prev => ({ ...prev, width: parseInt(e.target.value) || 0 }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md bg-white text-gray-900 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-900 mb-1">Height (px)</label>
                  <input
                    type="number"
                    value={canvasConfig.height}
                    onChange={(e) => setCanvasConfig(prev => ({ ...prev, height: parseInt(e.target.value) || 0 }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md bg-white text-gray-900 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-900 mb-1">DPI</label>
                  <input
                    type="number"
                    value={canvasConfig.dpi}
                    onChange={(e) => setCanvasConfig(prev => ({ ...prev, dpi: parseInt(e.target.value) || 0 }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md bg-white text-gray-900 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
              </div>

              {/* Save Button and Feedback */}
              <div className="flex items-center justify-between">
                <div className="flex space-x-2">
                  <button
                    onClick={saveCanvasConfig}
                    disabled={isSavingConfig}
                    className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {isSavingConfig ? 'Saving...' : 'Save Canvas Configuration'}
                  </button>
                  <button
                    onClick={testDatabaseConnection}
                    className="px-3 py-2 bg-gray-500 text-white rounded hover:bg-gray-600 text-sm"
                  >
                    Test DB
                  </button>
                  <button
                    onClick={testImageUrls}
                    className="px-3 py-2 bg-purple-500 text-white rounded hover:bg-purple-600 text-sm"
                  >
                    Test Images
                  </button>
                </div>

                {configSaveMessage && (
                  <div className={`text-sm font-medium ${
                    configSaveMessage.includes('Error') ? 'text-red-600' : 'text-green-600'
                  }`}>
                    {configSaveMessage}
                  </div>
                )}
              </div>

              <p className="text-xs text-gray-500 mt-2">
                This configuration applies to both mockup and print file views. Make sure to save after making changes.
              </p>

              {/* Debug Info */}
              <div className="mt-2 text-xs text-gray-400">
                Product ID: {product.id} | Current: {canvasConfig.width}×{canvasConfig.height}@{canvasConfig.dpi}
              </div>
            </div>

            {/* Side by Side Previews */}
            <div className="grid grid-cols-2 gap-6 mb-6">
              {/* Mockup Preview */}
              <div className="border border-gray-300 rounded-lg p-4">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-gray-900">Mockup Preview</h3>
                  <button
                    onClick={() => setIsAddingArea({ view: 'mockup' })}
                    className="px-3 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700"
                  >
                    Add Area
                  </button>
                </div>
                
                <div className="relative bg-gray-100 rounded border-2 border-dashed border-gray-300" style={{ aspectRatio: `${canvasConfig.width}/${canvasConfig.height}` }}>
                  <svg
                    ref={mockupCanvasRef}
                    viewBox={`0 0 ${canvasConfig.width} ${canvasConfig.height}`}
                    className="w-full h-full"
                  >
                    {/* Background image */}
                    {mockupImage ? (
                      <image
                        href={mockupImage}
                        x="0"
                        y="0"
                        width={canvasConfig.width}
                        height={canvasConfig.height}
                        preserveAspectRatio="xMidYMid slice"
                        opacity="0.7"
                        onError={() => {
                          console.error('Failed to load mockup image:', mockupImage)
                          setMockupImage(null)
                        }}
                      />
                    ) : (
                      <rect
                        x="0"
                        y="0"
                        width={canvasConfig.width}
                        height={canvasConfig.height}
                        fill="#f3f4f6"
                        stroke="#d1d5db"
                        strokeWidth="2"
                        strokeDasharray="10,10"
                      />
                    )}

                    {/* Placeholder text when no image */}
                    {!mockupImage && (
                      <text
                        x={canvasConfig.width / 2}
                        y={canvasConfig.height / 2}
                        textAnchor="middle"
                        dominantBaseline="middle"
                        className="fill-gray-500"
                        style={{ fontSize: '48px' }}
                      >
                        Mockup Preview
                      </text>
                    )}
                    
                    {/* Mapping areas */}
                    {mockupAreas.map((area) => (
                      <g key={area.id}>
                        <path
                          d={area.svg_path}
                          fill="rgba(34, 197, 94, 0.3)"
                          stroke="#22c55e"
                          strokeWidth="4"
                          className="cursor-pointer hover:fill-opacity-50"
                          onClick={() => setSelectedArea(area.id)}
                        />
                        {(() => {
                          const bounds = getBounds(area)
                          return bounds.x !== 0 || bounds.y !== 0 ? (
                            <text
                              x={bounds.x + bounds.width / 2}
                              y={bounds.y + bounds.height / 2}
                              textAnchor="middle"
                              dominantBaseline="middle"
                              className="text-sm font-medium fill-gray-800 pointer-events-none"
                              style={{ fontSize: '24px' }}
                            >
                              {area.area_name}
                            </text>
                          ) : null
                        })()}
                      </g>
                    ))}
                  </svg>
                </div>
              </div>

              {/* Print File Preview */}
              <div className="border border-gray-300 rounded-lg p-4">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-gray-900">Print File Preview</h3>
                  <button
                    onClick={() => setIsAddingArea({ view: 'printfile' })}
                    className="px-3 py-1 bg-green-600 text-white rounded text-sm hover:bg-green-700"
                  >
                    Add Area
                  </button>
                </div>
                
                <div className="relative bg-gray-100 rounded border-2 border-dashed border-gray-300" style={{ aspectRatio: `${canvasConfig.width}/${canvasConfig.height}` }}>
                  <svg
                    ref={printFileCanvasRef}
                    viewBox={`0 0 ${canvasConfig.width} ${canvasConfig.height}`}
                    className="w-full h-full"
                  >
                    {/* Background image */}
                    {printFileImage ? (
                      <image
                        href={printFileImage}
                        x="0"
                        y="0"
                        width={canvasConfig.width}
                        height={canvasConfig.height}
                        preserveAspectRatio="xMidYMid slice"
                        opacity="0.7"
                        onError={() => {
                          console.error('Failed to load print file image:', printFileImage)
                          setPrintFileImage(null)
                        }}
                      />
                    ) : (
                      <rect
                        x="0"
                        y="0"
                        width={canvasConfig.width}
                        height={canvasConfig.height}
                        fill="#f3f4f6"
                        stroke="#d1d5db"
                        strokeWidth="2"
                        strokeDasharray="10,10"
                      />
                    )}

                    {/* Placeholder text when no image */}
                    {!printFileImage && (
                      <text
                        x={canvasConfig.width / 2}
                        y={canvasConfig.height / 2}
                        textAnchor="middle"
                        dominantBaseline="middle"
                        className="fill-gray-500"
                        style={{ fontSize: '48px' }}
                      >
                        Print File Preview
                      </text>
                    )}
                    
                    {/* Mapping areas */}
                    {printFileAreas.map((area) => (
                      <g key={area.id}>
                        <path
                          d={area.svg_path}
                          fill="rgba(34, 197, 94, 0.3)"
                          stroke="#22c55e"
                          strokeWidth="4"
                          className="cursor-pointer hover:fill-opacity-50"
                          onClick={() => setSelectedArea(area.id)}
                        />
                        {(() => {
                          const bounds = getBounds(area)
                          return bounds.x !== 0 || bounds.y !== 0 ? (
                            <text
                              x={bounds.x + bounds.width / 2}
                              y={bounds.y + bounds.height / 2}
                              textAnchor="middle"
                              dominantBaseline="middle"
                              className="text-sm font-medium fill-gray-800 pointer-events-none"
                              style={{ fontSize: '24px' }}
                            >
                              {area.area_name}
                            </text>
                          ) : null
                        })()}
                      </g>
                    ))}
                  </svg>
                </div>
              </div>
            </div>

            {/* Add Area Form */}
            {isAddingArea.view && (
              <div className="mb-6 bg-blue-50 border border-blue-200 rounded-lg p-4">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                  Add Area to {isAddingArea.view === 'mockup' ? 'Mockup' : 'Print File'} View
                </h3>
                <div className="grid grid-cols-1 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-900 mb-1">Area Name</label>
                    <input
                      type="text"
                      value={newAreaForm.name}
                      onChange={(e) => setNewAreaForm(prev => ({ ...prev, name: e.target.value }))}
                      placeholder="e.g., Front Design Area"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md bg-white text-gray-900 placeholder-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-900 mb-1">SVG Path</label>
                    <textarea
                      value={newAreaForm.path}
                      onChange={(e) => setNewAreaForm(prev => ({ ...prev, path: e.target.value }))}
                      placeholder="e.g., M 100,100 L 300,100 L 300,200 L 100,200 Z"
                      rows={3}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md bg-white text-gray-900 placeholder-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-mono text-sm"
                    />
                  </div>
                  <div className="flex space-x-3">
                    <button
                      onClick={addMappingArea}
                      disabled={!newAreaForm.name.trim() || !newAreaForm.path.trim()}
                      className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50"
                    >
                      Add Area
                    </button>
                    <button
                      onClick={() => setIsAddingArea({ view: null })}
                      className="px-4 py-2 bg-gray-300 text-gray-700 rounded hover:bg-gray-400"
                    >
                      Cancel
                    </button>
                  </div>
                </div>
              </div>
            )}

            {/* Existing Mapped Areas */}
            <div className="bg-gray-50 rounded-lg p-4">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-semibold text-gray-900">Existing Mapped Areas</h3>
                {mappingAreas.length > 0 && (
                  <button
                    onClick={autoCalculateAllBounds}
                    className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                  >
                    Auto-Calculate All Bounds
                  </button>
                )}
              </div>

              {mappingAreas.length === 0 ? (
                <p className="text-gray-500 text-center py-8">No mapping areas created yet. Add areas using the buttons above.</p>
              ) : (
                <div className="space-y-3">
                  {/* Group areas by area_id to show connections */}
                  {Array.from(new Set(mappingAreas.map(area => area.area_id))).map(areaId => {
                    const mockupArea = mappingAreas.find(area => area.area_id === areaId && area.view_type === 'mockup')
                    const printFileArea = mappingAreas.find(area => area.area_id === areaId && area.view_type === 'printfile')
                    const isConnected = mockupArea && printFileArea

                    return (
                      <div key={areaId} className={`border rounded-lg p-4 ${isConnected ? 'border-green-300 bg-green-50' : 'border-red-300 bg-red-50'}`}>
                        <div className="flex items-center justify-between mb-2">
                          <h4 className="font-medium text-gray-900">
                            {mockupArea?.area_name || printFileArea?.area_name || areaId}
                          </h4>
                          <div className={`px-2 py-1 rounded text-xs font-medium ${
                            isConnected
                              ? 'bg-green-100 text-green-800'
                              : 'bg-red-100 text-red-800'
                          }`}>
                            {isConnected ? 'Connected' : 'Incomplete'}
                          </div>
                        </div>

                        <div className="grid grid-cols-2 gap-4">
                          {/* Mockup Area */}
                          <div className="flex items-center justify-between">
                            <div>
                              <span className="text-sm font-medium text-gray-700">Mockup View:</span>
                              <span className={`ml-2 text-sm ${mockupArea ? 'text-green-600' : 'text-red-600'}`}>
                                {mockupArea ? '✓ Configured' : '✗ Missing'}
                              </span>
                            </div>
                            {mockupArea && (
                              <button
                                onClick={() => deleteArea(areaId, 'mockup')}
                                className="text-red-600 hover:text-red-800 text-sm"
                              >
                                Delete
                              </button>
                            )}
                          </div>

                          {/* Print File Area */}
                          <div className="flex items-center justify-between">
                            <div>
                              <span className="text-sm font-medium text-gray-700">Print File View:</span>
                              <span className={`ml-2 text-sm ${printFileArea ? 'text-green-600' : 'text-red-600'}`}>
                                {printFileArea ? '✓ Configured' : '✗ Missing'}
                              </span>
                            </div>
                            {printFileArea && (
                              <button
                                onClick={() => deleteArea(areaId, 'printfile')}
                                className="text-red-600 hover:text-red-800 text-sm"
                              >
                                Delete
                              </button>
                            )}
                          </div>
                        </div>

                        {!isConnected && (
                          <div className="mt-2 text-sm text-red-600">
                            ⚠ This area needs to be configured in both views to enable coordinate transformation.
                          </div>
                        )}
                      </div>
                    )
                  })}
                </div>
              )}
            </div>

            {/* Selected Area Form */}
            {selectedArea && (
              <div className="mt-6 p-4 border rounded-lg bg-blue-50">
                <h3 className="text-lg font-medium text-gray-900 mb-4">
                  Auto-Calculate Display Bounds
                </h3>
                {(() => {
                  const area = mappingAreas.find(a => a.id === selectedArea)
                  if (!area) return null

                  const calculatedBounds = calculateBoundsFromSVGPath(area.svg_path)

                  return (
                    <div className="space-y-4">
                      <div className="text-sm text-gray-600 mb-3">
                        Auto-calculating display bounds for: <strong>{area.area_name}</strong> ({area.view_type})
                      </div>

                      <div className="bg-white p-4 rounded border">
                        <h4 className="font-medium text-gray-900 mb-2">Calculated Bounds from SVG Path:</h4>
                        <div className="grid grid-cols-2 gap-4 text-sm">
                          <div>
                            <span className="font-medium text-gray-700">X Position:</span>
                            <span className="ml-2 text-black">{calculatedBounds.x}px</span>
                          </div>
                          <div>
                            <span className="font-medium text-gray-700">Y Position:</span>
                            <span className="ml-2 text-black">{calculatedBounds.y}px</span>
                          </div>
                          <div>
                            <span className="font-medium text-gray-700">Width:</span>
                            <span className="ml-2 text-black">{calculatedBounds.width}px</span>
                          </div>
                          <div>
                            <span className="font-medium text-gray-700">Height:</span>
                            <span className="ml-2 text-black">{calculatedBounds.height}px</span>
                          </div>
                        </div>
                      </div>

                      <div className="text-sm text-gray-600">
                        These bounds are automatically calculated from the SVG path shape and will be used for the debug visualization.
                      </div>

                      <div className="flex space-x-3">
                        <button
                          onClick={() => saveAreaBounds(area)}
                          className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700"
                        >
                          Save Auto-Calculated Bounds
                        </button>
                        <button
                          onClick={() => setSelectedArea(null)}
                          className="px-4 py-2 bg-gray-300 text-gray-700 rounded-lg hover:bg-gray-400"
                        >
                          Cancel
                        </button>
                      </div>
                    </div>
                  )
                })()}
              </div>
            )}

            {/* Actions */}
            <div className="mt-6 flex space-x-3">
              <button
                onClick={onSuccess}
                className="px-6 py-2 bg-green-600 text-white rounded hover:bg-green-700"
              >
                Complete Setup
              </button>
              <button
                onClick={onClose}
                className="px-6 py-2 bg-gray-300 text-gray-700 rounded hover:bg-gray-400"
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
