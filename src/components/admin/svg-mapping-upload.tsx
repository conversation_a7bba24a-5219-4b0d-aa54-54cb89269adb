'use client'

import { useState } from 'react'
import { createSupabaseBrowserClient } from '@/lib/supabase'
import { parseSVGMapping<PERSON>reas, extractSVGViewBox } from '@/lib/svg-parser'

interface SVGMappingUploadProps {
  productId: string
  viewType: 'mockup' | 'printfile'
  onUploadComplete: (svgConfig: any) => void
  onMappingAreasExtracted?: (areas: any[]) => void
}

export default function SVGMappingUpload({
  productId,
  viewType,
  onUploadComplete,
  onMappingAreasExtracted
}: SVGMappingUploadProps) {
  const [isUploading, setIsUploading] = useState(false)
  const [isExtracting, setIsExtracting] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState<string | null>(null)
  const [svgPreview, setSvgPreview] = useState<string | null>(null)
  const [extractedAreas, setExtractedAreas] = useState<any[]>([])

  const supabase = createSupabaseBrowserClient()

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    // Validate file type
    if (!file.type.includes('svg') && !file.name.endsWith('.svg')) {
      setError('Please upload an SVG file')
      return
    }

    setIsUploading(true)
    setError(null)
    setSuccess(null)

    try {
      // Read SVG content
      const svgContent = await file.text()
      setSvgPreview(svgContent)

      // Extract viewBox from SVG using our parser
      const viewBox = extractSVGViewBox(svgContent)
      const viewBoxString = `${viewBox.x} ${viewBox.y} ${viewBox.width} ${viewBox.height}`

      console.log('Storing SVG content in database...', { viewBox })

      // Save SVG mapping configuration directly in database
      const svgConfig = {
        product_id: productId,
        view_type: viewType,
        svg_file: {
          bucket: 'database',
          filePath: `svg-mapping/${productId}/${viewType}-${Date.now()}.svg`,
          fileName: file.name
        },
        svg_content: svgContent,
        svg_viewbox: viewBoxString
      }

      // Note: SVG config is stored in memory for this session
      // In a production app, you might want to store this in the products table

      // Parse and extract mapping areas
      const mappingAreas = parseSVGMappingAreas(svgContent)
      setExtractedAreas(mappingAreas)

      setSuccess(`SVG mapping file uploaded successfully for ${viewType} view! Found ${mappingAreas.length} mapping areas.`)
      onUploadComplete(svgConfig)

    } catch (err: any) {
      console.error('Error uploading SVG:', err)
      setError(`Failed to upload SVG: ${err.message}`)
    } finally {
      setIsUploading(false)
    }
  }

  const saveMappingAreasToDatabase = async () => {
    if (extractedAreas.length === 0) {
      setError('No mapping areas found. Please upload an SVG file first.')
      return
    }

    setIsExtracting(true)
    setError(null)

    try {
      // Convert parsed areas to database format
      const mappingAreas = extractedAreas.map((area, index) => {
        const boundsJson = {
          x: area.bounds.x,
          y: area.bounds.y,
          width: area.bounds.width,
          height: area.bounds.height
        }

        return {
          product_id: productId,
          area_id: area.id,
          area_name: area.name,
          view_type: viewType,
          svg_path: area.pathData,
          svg_element_id: area.id,
          svg_path_data: area.pathData,
          is_active: true,
          sort_order: index,
          display_bounds: boundsJson,
          print_bounds: boundsJson
        }
      })

      // Save mapping areas to database
      const { error } = await supabase
        .from('product_mapping_areas')
        .upsert(mappingAreas, {
          onConflict: 'product_id,area_id,view_type'
        })

      if (error) throw error

      setSuccess(`Successfully saved ${mappingAreas.length} mapping areas to database!`)

      if (onMappingAreasExtracted) {
        onMappingAreasExtracted(mappingAreas)
      }

      console.log(`Saved ${mappingAreas.length} mapping areas to database`)

    } catch (err: any) {
      console.error('Error saving mapping areas:', err)
      setError(`Failed to save mapping areas: ${err.message}`)
    } finally {
      setIsExtracting(false)
    }
  }

  return (
    <div className="space-y-4">
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Upload SVG Mapping File ({viewType === 'mockup' ? 'Mockup' : 'Print File'} View)
        </label>
        <input
          type="file"
          accept=".svg,image/svg+xml"
          onChange={handleFileUpload}
          disabled={isUploading}
          className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
        />
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 rounded p-3 text-red-700 text-sm">
          <strong>Upload Error:</strong> {error}
        </div>
      )}

      {success && (
        <div className="bg-green-50 border border-green-200 rounded p-3 text-green-700 text-sm">
          <strong>Success:</strong> {success}
        </div>
      )}

      {isUploading && (
        <div className="bg-blue-50 border border-blue-200 rounded p-3 text-blue-700 text-sm">
          <strong>Uploading:</strong> Processing SVG file...
        </div>
      )}

      {svgPreview && (
        <div className="space-y-4">
          <div>
            <h4 className="text-sm font-medium text-gray-700 mb-2">SVG Preview:</h4>
            <div
              className="border border-gray-300 rounded p-4 max-h-64 overflow-auto bg-white"
              dangerouslySetInnerHTML={{ __html: svgPreview }}
            />
          </div>

          {extractedAreas.length > 0 && (
            <div>
              <h4 className="text-sm font-medium text-gray-700 mb-2">
                Found {extractedAreas.length} Mapping Areas:
              </h4>
              <div className="bg-gray-50 rounded p-3 max-h-32 overflow-y-auto">
                {extractedAreas.map((area, index) => (
                  <div key={index} className="text-xs text-gray-600 mb-1">
                    <strong>{area.name}</strong> ({area.id}) -
                    {area.bounds.width.toFixed(0)}×{area.bounds.height.toFixed(0)}px
                  </div>
                ))}
              </div>

              <button
                onClick={saveMappingAreasToDatabase}
                disabled={isExtracting}
                className="mt-2 px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 disabled:opacity-50"
              >
                {isExtracting ? 'Saving...' : 'Save Mapping Areas to Database'}
              </button>
            </div>
          )}
        </div>
      )}
    </div>
  )
}
