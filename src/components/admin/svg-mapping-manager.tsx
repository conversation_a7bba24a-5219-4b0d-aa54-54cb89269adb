'use client'

import { useState, useEffect } from 'react'
import { createSupabaseBrowserClient } from '@/lib/supabase'
import SVGMappingUpload from './svg-mapping-upload'
import SVGMappingVisualizer from './svg-mapping-visualizer'
import ManualPathInput from './manual-path-input'

interface SVGMappingManagerProps {
  productId: string
  product: any
}

export default function SVGMappingManager({ productId, product }: SVGMappingManagerProps) {
  const [activeTab, setActiveTab] = useState<'mockup' | 'printfile'>('mockup')
  const [inputMode, setInputMode] = useState<'upload' | 'manual'>('manual')
  const [mappingStats, setMappingStats] = useState({
    mockup: { hasConfig: false, areaCount: 0 },
    printfile: { hasConfig: false, areaCount: 0 }
  })
  const [refreshKey, setRefreshKey] = useState(0)
  
  const supabase = createSupabaseBrowserClient()

  useEffect(() => {
    loadMappingStats()
  }, [productId, refreshKey])

  const loadMappingStats = async () => {
    try {
      // Count mapping areas
      const { data: areas } = await supabase
        .from('product_mapping_areas')
        .select('view_type')
        .eq('product_id', productId)
        .eq('is_active', true)

      const mockupAreas = areas?.filter(a => a.view_type === 'mockup').length || 0
      const printfileAreas = areas?.filter(a => a.view_type === 'printfile').length || 0

      // Assume config exists if there are areas
      const mockupConfig = mockupAreas > 0
      const printfileConfig = printfileAreas > 0

      setMappingStats({
        mockup: { hasConfig: mockupConfig, areaCount: mockupAreas },
        printfile: { hasConfig: printfileConfig, areaCount: printfileAreas }
      })
    } catch (err) {
      console.error('Error loading mapping stats:', err)
    }
  }

  const handleUploadComplete = () => {
    setRefreshKey(prev => prev + 1)
  }

  const handleMappingAreasExtracted = () => {
    setRefreshKey(prev => prev + 1)
  }

  const testCoordinateTransformation = async () => {
    try {
      // Test transformation between views
      const testElement = { x: 100, y: 100, width: 200, height: 200 }
      
      console.log('Testing coordinate transformation...')
      console.log('Original element (mockup):', testElement)
      
      // This would use the transformation function we created
      // const transformed = await transformDesignElement(supabase, productId, testElement, 'mockup', 'printfile')
      // console.log('Transformed element (print file):', transformed)
      
      alert('Check console for transformation test results')
    } catch (err: any) {
      console.error('Transformation test failed:', err)
      alert(`Transformation test failed: ${err.message}`)
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-xl font-semibold text-gray-900">SVG Mapping System</h2>
          <p className="text-sm text-gray-600">
            Upload SVG files to define complex mapping areas for {product.name}
          </p>
        </div>
        
        {mappingStats.mockup.hasConfig && mappingStats.printfile.hasConfig && (
          <button
            onClick={testCoordinateTransformation}
            className="px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700"
          >
            Test Transformation
          </button>
        )}
      </div>

      {/* Status Overview */}
      <div className="grid grid-cols-2 gap-4">
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <h3 className="font-medium text-blue-900">Mockup View</h3>
            <div className={`w-3 h-3 rounded-full ${mappingStats.mockup.hasConfig ? 'bg-green-500' : 'bg-gray-400'}`} />
          </div>
          <div className="mt-2 text-sm text-blue-700">
            {mappingStats.mockup.hasConfig ? (
              <>SVG uploaded • {mappingStats.mockup.areaCount} areas</>
            ) : (
              'No SVG uploaded'
            )}
          </div>
        </div>
        
        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <h3 className="font-medium text-green-900">Print File View</h3>
            <div className={`w-3 h-3 rounded-full ${mappingStats.printfile.hasConfig ? 'bg-green-500' : 'bg-gray-400'}`} />
          </div>
          <div className="mt-2 text-sm text-green-700">
            {mappingStats.printfile.hasConfig ? (
              <>SVG uploaded • {mappingStats.printfile.areaCount} areas</>
            ) : (
              'No SVG uploaded'
            )}
          </div>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          <button
            onClick={() => setActiveTab('mockup')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'mockup'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            Mockup View
            {mappingStats.mockup.areaCount > 0 && (
              <span className="ml-2 bg-blue-100 text-blue-600 py-0.5 px-2 rounded-full text-xs">
                {mappingStats.mockup.areaCount}
              </span>
            )}
          </button>
          <button
            onClick={() => setActiveTab('printfile')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'printfile'
                ? 'border-green-500 text-green-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            Print File View
            {mappingStats.printfile.areaCount > 0 && (
              <span className="ml-2 bg-green-100 text-green-600 py-0.5 px-2 rounded-full text-xs">
                {mappingStats.printfile.areaCount}
              </span>
            )}
          </button>
        </nav>
      </div>

      {/* Input Mode Selection */}
      <div className="bg-gray-50 p-4 rounded-lg">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Input Method</h3>
        <div className="flex space-x-4">
          <button
            onClick={() => setInputMode('manual')}
            className={`px-4 py-2 rounded-lg font-medium ${
              inputMode === 'manual'
                ? 'bg-blue-600 text-white'
                : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50'
            }`}
          >
            Manual Path Input
          </button>
          <button
            onClick={() => setInputMode('upload')}
            className={`px-4 py-2 rounded-lg font-medium ${
              inputMode === 'upload'
                ? 'bg-blue-600 text-white'
                : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50'
            }`}
          >
            SVG File Upload
          </button>
        </div>
        <p className="mt-2 text-sm text-gray-600">
          {inputMode === 'manual'
            ? 'Directly input SVG path data with custom area names.'
            : 'Upload SVG files to automatically extract mapping areas.'
          }
        </p>
      </div>

      {/* Tab Content */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Input Section */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium text-gray-900">
            {inputMode === 'manual' ? 'Add Mapping Area' : 'Upload SVG File'}
            ({activeTab === 'mockup' ? 'Mockup' : 'Print File'} View)
          </h3>

          {inputMode === 'manual' ? (
            <ManualPathInput
              productId={productId}
              viewType={activeTab}
              onPathAdded={handleMappingAreasExtracted}
            />
          ) : (
            <SVGMappingUpload
              productId={productId}
              viewType={activeTab}
              onUploadComplete={handleUploadComplete}
              onMappingAreasExtracted={handleMappingAreasExtracted}
            />
          )}
        </div>

        {/* Visualization Section */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium text-gray-900">
            {activeTab === 'mockup' ? 'Mockup' : 'Print File'} Mapping Areas
          </h3>
          
          <SVGMappingVisualizer
            key={`${activeTab}-${refreshKey}`}
            productId={productId}
            viewType={activeTab}
            onAreaSelect={(area) => {
              console.log('Selected area:', area)
            }}
          />
        </div>
      </div>

      {/* Instructions */}
      <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
        <h4 className="font-medium text-gray-900 mb-2">How to Use SVG Mapping:</h4>

        {inputMode === 'manual' ? (
          <ol className="list-decimal list-inside space-y-1 text-sm text-gray-600">
            <li>Use the <strong>Manual Path Input</strong> to directly add mapping areas</li>
            <li>Enter an <strong>Area ID</strong> (e.g., "front", "back", "logo")</li>
            <li>Provide a descriptive <strong>Area Name</strong></li>
            <li>Input the <strong>SVG path data</strong> (d attribute from SVG elements)</li>
            <li>Use the <strong>same Area ID</strong> in both mockup and print file views to link them</li>
            <li>Test coordinate transformation once both views have matching areas</li>
          </ol>
        ) : (
          <ol className="list-decimal list-inside space-y-1 text-sm text-gray-600">
            <li>Create SVG files with elements that have unique <code>id</code> attributes</li>
            <li>Use the same <code>id</code> values in both mockup and print file SVGs for corresponding areas</li>
            <li>Upload the SVG files using the upload sections above</li>
            <li>The system will automatically extract mapping areas from elements with IDs</li>
            <li>Test coordinate transformation once both views are configured</li>
          </ol>
        )}

        <div className="mt-3 p-3 bg-blue-50 rounded border border-blue-200">
          <p className="text-sm text-blue-700">
            <strong>Example:</strong> Create a mapping area with ID "front" in both mockup and print file views.
            The system will automatically link them for coordinate transformation between the two views.
          </p>
        </div>
      </div>
    </div>
  )
}
