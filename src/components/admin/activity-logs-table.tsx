'use client'

import { useState } from 'react'
import { format } from 'date-fns'

interface ActivityLog {
  id: string
  action: string
  resource_type: string
  resource_id: string | null
  details: any
  ip_address: unknown
  user_agent: string | null
  created_at: string | null
  users: {
    id: string
    email: string
    role: string | null
  } | null
  merchants: {
    id: string
    shop_name: string
    shop_domain: string
  } | null
}

interface ActivityLogsTableProps {
  logs: ActivityLog[]
}

export default function ActivityLogsTable({ logs }: ActivityLogsTableProps) {
  const [searchTerm, setSearchTerm] = useState('')
  const [levelFilter, setLevelFilter] = useState('all')
  const [sourceFilter, setSourceFilter] = useState('all')
  const [selectedLog, setSelectedLog] = useState<ActivityLog | null>(null)

  // Filter logs based on search and filters
  const filteredLogs = logs.filter(log => {
    const matchesSearch = searchTerm === '' || 
      log.action.toLowerCase().includes(searchTerm.toLowerCase()) ||
      log.resource_type.toLowerCase().includes(searchTerm.toLowerCase()) ||
      log.users?.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      log.merchants?.shop_domain.toLowerCase().includes(searchTerm.toLowerCase())

    const matchesLevel = levelFilter === 'all' || getLogLevel(log.action) === levelFilter
    const matchesSource = sourceFilter === 'all' || getLogSource(log.action) === sourceFilter

    return matchesSearch && matchesLevel && matchesSource
  })

  // Determine log level based on action
  function getLogLevel(action: string): string {
    if (action.includes('error') || action.includes('failed')) return 'error'
    if (action.includes('warning') || action.includes('warn')) return 'warning'
    if (action.includes('debug')) return 'debug'
    return 'info'
  }

  // Determine log source based on action
  function getLogSource(action: string): string {
    if (action.includes('shopify') || action.includes('webhook')) return 'webhooks'
    if (action.includes('auth') || action.includes('login') || action.includes('install')) return 'auth'
    if (action.includes('order')) return 'orders'
    if (action.includes('product')) return 'products'
    return 'api'
  }

  // Get badge color for log level
  function getLevelBadgeColor(level: string): string {
    switch (level) {
      case 'error': return 'bg-red-100 text-red-800'
      case 'warning': return 'bg-yellow-100 text-yellow-800'
      case 'debug': return 'bg-blue-100 text-blue-800'
      default: return 'bg-green-100 text-green-800'
    }
  }

  // Format log message
  function formatLogMessage(log: ActivityLog): { title: string; subtitle: string } {
    const details = log.details || {}
    
    switch (log.action) {
      case 'shopify_app_installed':
        return {
          title: 'Shopify App Installed',
          subtitle: `Store: ${details.shop_domain || 'Unknown'}`
        }
      case 'shopify_app_uninstalled':
        return {
          title: 'Shopify App Uninstalled',
          subtitle: `Store: ${details.shop_domain || 'Unknown'}`
        }
      case 'shopify_app_updated':
        return {
          title: 'Shopify App Updated',
          subtitle: `Store: ${details.shop_domain || 'Unknown'}`
        }
      case 'order_created':
        return {
          title: 'Order Created',
          subtitle: `Order: ${details.order_number || details.shopify_order_id || 'Unknown'}`
        }
      case 'order_status_changed':
        return {
          title: 'Order Status Changed',
          subtitle: `${details.old_status} → ${details.new_status}`
        }
      default:
        return {
          title: log.action.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
          subtitle: `Resource: ${log.resource_type}`
        }
    }
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200">
      {/* Header with filters */}
      <div className="px-6 py-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <h2 className="text-lg font-semibold text-gray-900">Activity Logs</h2>
          <div className="flex space-x-2">
            <input
              type="text"
              placeholder="Search logs..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="border border-gray-300 rounded-md px-3 py-2 text-sm text-black placeholder:text-gray-500"
            />
            <select
              value={levelFilter}
              onChange={(e) => setLevelFilter(e.target.value)}
              className="border border-gray-300 rounded-md px-3 py-2 text-sm text-black"
            >
              <option value="all">All Levels</option>
              <option value="error">Error</option>
              <option value="warning">Warning</option>
              <option value="info">Info</option>
              <option value="debug">Debug</option>
            </select>
            <select 
              value={sourceFilter}
              onChange={(e) => setSourceFilter(e.target.value)}
              className="border border-gray-300 rounded-md px-3 py-2 text-sm"
            >
              <option value="all">All Sources</option>
              <option value="api">API</option>
              <option value="webhooks">Webhooks</option>
              <option value="auth">Auth</option>
              <option value="orders">Orders</option>
              <option value="products">Products</option>
            </select>
            <button className="bg-blue-600 text-white px-4 py-2 rounded-md text-sm hover:bg-blue-700">
              Export
            </button>
          </div>
        </div>
      </div>

      {/* Table */}
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Timestamp
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Level
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Source
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Message
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                User/Store
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {filteredLogs.length === 0 ? (
              <tr>
                <td colSpan={6} className="px-6 py-12 text-center">
                  <div className="text-gray-500">
                    <p className="text-sm">No activity logs found</p>
                    {searchTerm && (
                      <p className="text-xs mt-1">Try adjusting your search or filters</p>
                    )}
                  </div>
                </td>
              </tr>
            ) : (
              filteredLogs.map((log) => {
                const level = getLogLevel(log.action)
                const source = getLogSource(log.action)
                const message = formatLogMessage(log)
                
                return (
                  <tr key={log.id}>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {log.created_at ? format(new Date(log.created_at), 'yyyy-MM-dd HH:mm:ss') : 'Unknown'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getLevelBadgeColor(level)}`}>
                        {level.charAt(0).toUpperCase() + level.slice(1)}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 capitalize">
                      {source}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">{message.title}</div>
                      <div className="text-sm text-gray-500">{message.subtitle}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {log.users?.email || log.merchants?.shop_domain || (typeof log.ip_address === 'string' ? log.ip_address : null) || 'System'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <button
                        onClick={() => setSelectedLog(log)}
                        className="text-blue-600 hover:text-blue-900"
                      >
                        View Details
                      </button>
                    </td>
                  </tr>
                )
              })
            )}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      <div className="px-6 py-4 border-t border-gray-200">
        <div className="flex items-center justify-between">
          <p className="text-sm text-gray-700">
            Showing {filteredLogs.length} of {logs.length} log entries
          </p>
          <div className="flex space-x-2">
            <button className="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500">
              Previous
            </button>
            <button className="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500">
              Next
            </button>
          </div>
        </div>
      </div>

      {/* Details Modal */}
      {selectedLog && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[80vh] overflow-y-auto">
            <div className="px-6 py-4 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold text-gray-900">Activity Log Details</h3>
                <button
                  onClick={() => setSelectedLog(null)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
            </div>

            <div className="px-6 py-4 space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700">Action</label>
                  <p className="mt-1 text-sm text-gray-900">{selectedLog.action}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Resource Type</label>
                  <p className="mt-1 text-sm text-gray-900">{selectedLog.resource_type}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Timestamp</label>
                  <p className="mt-1 text-sm text-gray-900">
                    {selectedLog.created_at ? format(new Date(selectedLog.created_at), 'yyyy-MM-dd HH:mm:ss') : 'Unknown'}
                  </p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Resource ID</label>
                  <p className="mt-1 text-sm text-gray-900">{selectedLog.resource_id || 'N/A'}</p>
                </div>
              </div>

              {selectedLog.users && (
                <div>
                  <label className="block text-sm font-medium text-gray-700">User</label>
                  <p className="mt-1 text-sm text-gray-900">
                    {selectedLog.users.email} ({selectedLog.users.role})
                  </p>
                </div>
              )}

              {selectedLog.merchants && (
                <div>
                  <label className="block text-sm font-medium text-gray-700">Merchant</label>
                  <p className="mt-1 text-sm text-gray-900">
                    {selectedLog.merchants.shop_name} ({selectedLog.merchants.shop_domain})
                  </p>
                </div>
              )}

              {selectedLog.user_agent && (
                <div>
                  <label className="block text-sm font-medium text-gray-700">User Agent</label>
                  <p className="mt-1 text-sm text-gray-900 break-all">{selectedLog.user_agent}</p>
                </div>
              )}

              <div>
                <label className="block text-sm font-medium text-gray-700">Details</label>
                <div className="mt-1 bg-gray-50 rounded-md p-3">
                  <pre className="text-sm text-gray-900 whitespace-pre-wrap">
                    {JSON.stringify(selectedLog.details, null, 2)}
                  </pre>
                </div>
              </div>
            </div>

            <div className="px-6 py-4 border-t border-gray-200">
              <button
                onClick={() => setSelectedLog(null)}
                className="bg-gray-600 text-white px-4 py-2 rounded-md text-sm hover:bg-gray-700"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
