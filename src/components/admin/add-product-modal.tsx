'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'

interface AddProductModalProps {
  isOpen: boolean
  onClose: () => void
  onSuccess: () => void
  categories: Array<{ id: string; name: string }>
}

export default function AddProductModal({ isOpen, onClose, onSuccess, categories }: AddProductModalProps) {
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState('')
  
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    basePrice: '',
    categoryId: '',
    sku: '',
    weight: '',
    materials: '',
    tags: '',
    careInstructions: '',
    dimensions: '',
    featured: false
  })

  const [files, setFiles] = useState({
    images: [] as File[],
    printFiles: [] as File[],
    mockupFiles: [] as File[],
    glbFiles: [] as File[],
    textureFiles: [] as File[],
    previewFiles: [] as File[]
  })

  const [variants, setVariants] = useState([
    {
      id: Date.now(),
      title: 'Default',
      price: '',
      compareAtPrice: '',
      sku: '',
      inventoryQuantity: '',
      option1: '',
      option2: '',
      option3: '',
      weight: ''
    }
  ])

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : value
    }))
  }

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>, fileType: 'images' | 'printFiles' | 'mockupFiles' | 'glbFiles' | 'textureFiles' | 'previewFiles') => {
    const selectedFiles = Array.from(e.target.files || [])
    console.log(`Selected ${selectedFiles.length} ${fileType}:`, selectedFiles.map(f => f.name))
    setFiles(prev => ({
      ...prev,
      [fileType]: selectedFiles
    }))
  }

  const addVariant = () => {
    setVariants(prev => [...prev, {
      id: Date.now(),
      title: `Variant ${prev.length + 1}`,
      price: formData.basePrice,
      compareAtPrice: '',
      sku: '',
      inventoryQuantity: '0',
      option1: '',
      option2: '',
      option3: '',
      weight: formData.weight
    }])
  }

  const removeVariant = (id: number) => {
    if (variants.length > 1) {
      setVariants(prev => prev.filter(v => v.id !== id))
    }
  }

  const updateVariant = (id: number, field: string, value: string) => {
    setVariants(prev => prev.map(v =>
      v.id === id ? { ...v, [field]: value } : v
    ))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    setError('')

    try {
      const submitFormData = new FormData()
      
      // Add text fields
      Object.entries(formData).forEach(([key, value]) => {
        if (key === 'materials' || key === 'tags') {
          // Convert comma-separated strings to arrays
          const stringValue = typeof value === 'string' ? value : ''
          const arrayValue = stringValue ? stringValue.split(',').map(item => item.trim()).filter(Boolean) : []
          submitFormData.append(key, JSON.stringify(arrayValue))
        } else if (key === 'dimensions') {
          // Parse dimensions JSON
          try {
            const stringValue = typeof value === 'string' ? value : ''
            const dimensionsObj = stringValue ? JSON.parse(stringValue) : null
            submitFormData.append(key, JSON.stringify(dimensionsObj))
          } catch {
            submitFormData.append(key, JSON.stringify(null))
          }
        } else {
          submitFormData.append(key, value.toString())
        }
      })

      // Add files
      files.images.forEach((file, index) => {
        submitFormData.append(`images${index}`, file)
      })

      files.printFiles.forEach((file, index) => {
        submitFormData.append(`printFiles${index}`, file)
      })

      files.mockupFiles.forEach((file, index) => {
        submitFormData.append(`mockupFiles${index}`, file)
      })

      files.glbFiles.forEach((file, index) => {
        submitFormData.append(`glbFiles${index}`, file)
      })

      files.textureFiles.forEach((file, index) => {
        submitFormData.append(`textureFiles${index}`, file)
      })

      files.previewFiles.forEach((file, index) => {
        submitFormData.append(`previewFiles${index}`, file)
      })

      // Add variants data
      submitFormData.append('variants', JSON.stringify(variants))

      const response = await fetch('/api/admin/products', {
        method: 'POST',
        body: submitFormData
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || 'Failed to create product')
      }

      onSuccess()
      onClose()
      
      // Reset form
      setFormData({
        name: '',
        description: '',
        basePrice: '',
        categoryId: '',
        sku: '',
        weight: '',
        materials: '',
        tags: '',
        careInstructions: '',
        dimensions: '',
        featured: false
      })
      setFiles({ images: [], printFiles: [], mockupFiles: [], glbFiles: [], textureFiles: [], previewFiles: [] })
      setVariants([{
        id: Date.now(),
        title: 'Default',
        price: '',
        compareAtPrice: '',
        sku: '',
        inventoryQuantity: '',
        option1: '',
        option2: '',
        option3: '',
        weight: ''
      }])

    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred')
    } finally {
      setIsLoading(false)
    }
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold text-gray-900">Add New Product</h3>
            <button 
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
              disabled={isLoading}
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>

        <form onSubmit={handleSubmit} className="px-6 py-4 space-y-6">
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-md p-3">
              <p className="text-sm text-red-600">{error}</p>
            </div>
          )}

          {/* Basic Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Product Name *
              </label>
              <Input
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                required
                placeholder="e.g., Custom T-Shirt"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                SKU
              </label>
              <Input
                name="sku"
                value={formData.sku}
                onChange={handleInputChange}
                placeholder="e.g., TSH-001"
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Description
            </label>
            <textarea
              name="description"
              value={formData.description}
              onChange={handleInputChange}
              rows={3}
              className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm text-black placeholder:text-gray-500"
              placeholder="Product description..."
            />
          </div>

          {/* Pricing and Category */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Base Price * ($)
              </label>
              <Input
                name="basePrice"
                type="number"
                step="0.01"
                value={formData.basePrice}
                onChange={handleInputChange}
                required
                placeholder="19.99"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Category
              </label>
              <select
                name="categoryId"
                value={formData.categoryId}
                onChange={handleInputChange}
                className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm text-black"
              >
                <option value="">Select Category</option>
                {categories.map(category => (
                  <option key={category.id} value={category.id}>
                    {category.name}
                  </option>
                ))}
              </select>
            </div>
          </div>

          {/* Physical Properties */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Weight (lbs)
              </label>
              <Input
                name="weight"
                type="number"
                step="0.01"
                value={formData.weight}
                onChange={handleInputChange}
                placeholder="0.5"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Dimensions (JSON)
              </label>
              <Input
                name="dimensions"
                value={formData.dimensions}
                onChange={handleInputChange}
                placeholder='{"width": 20, "height": 28, "unit": "inches"}'
              />
            </div>
          </div>

          {/* Materials and Tags */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Materials (comma-separated)
              </label>
              <Input
                name="materials"
                value={formData.materials}
                onChange={handleInputChange}
                placeholder="100% Cotton, Polyester"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Tags (comma-separated)
              </label>
              <Input
                name="tags"
                value={formData.tags}
                onChange={handleInputChange}
                placeholder="cotton, basic, unisex"
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Care Instructions
            </label>
            <textarea
              name="careInstructions"
              value={formData.careInstructions}
              onChange={handleInputChange}
              rows={2}
              className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm text-black placeholder:text-gray-500"
              placeholder="Machine wash cold, tumble dry low"
            />
          </div>

          {/* File Uploads */}
          <div className="space-y-4">
            <h4 className="text-md font-medium text-gray-900">File Uploads</h4>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Product Images
              </label>
              <input
                type="file"
                multiple
                accept="image/*"
                onChange={(e) => handleFileChange(e, 'images')}
                className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm text-gray-900 bg-white"
              />
              <p className="text-xs text-gray-500 mt-1">Upload product photos for display</p>
              {files.images.length > 0 && (
                <div className="mt-2">
                  <p className="text-sm text-green-600">{files.images.length} image(s) selected:</p>
                  <ul className="text-xs text-gray-600 ml-4">
                    {files.images.map((file, index) => (
                      <li key={index}>• {file.name} ({(file.size / 1024 / 1024).toFixed(2)} MB)</li>
                    ))}
                  </ul>
                </div>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Print Files *
              </label>
              <input
                type="file"
                multiple
                accept=".pdf,.svg,.png,.jpg,.jpeg"
                onChange={(e) => handleFileChange(e, 'printFiles')}
                className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm text-gray-900 bg-white"
              />
              <p className="text-xs text-gray-500 mt-1">Upload print-ready files for customizer (PDF, SVG, PNG, JPG)</p>
              {files.printFiles.length > 0 && (
                <div className="mt-2">
                  <p className="text-sm text-green-600">{files.printFiles.length} print file(s) selected:</p>
                  <ul className="text-xs text-gray-600 ml-4">
                    {files.printFiles.map((file, index) => (
                      <li key={index}>• {file.name} ({(file.size / 1024 / 1024).toFixed(2)} MB)</li>
                    ))}
                  </ul>
                </div>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Mockup Files *
              </label>
              <input
                type="file"
                multiple
                accept="image/*"
                onChange={(e) => handleFileChange(e, 'mockupFiles')}
                className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm text-gray-900 bg-white"
              />
              <p className="text-xs text-gray-500 mt-1">Upload mockup images for customizer preview</p>
              {files.mockupFiles.length > 0 && (
                <div className="mt-2">
                  <p className="text-sm text-green-600">{files.mockupFiles.length} mockup file(s) selected:</p>
                  <ul className="text-xs text-gray-600 ml-4">
                    {files.mockupFiles.map((file, index) => (
                      <li key={index}>• {file.name} ({(file.size / 1024 / 1024).toFixed(2)} MB)</li>
                    ))}
                  </ul>
                </div>
              )}
            </div>

            {/* 3D GLB Files */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                3D GLB Files
              </label>
              <input
                type="file"
                multiple
                accept=".glb,.gltf"
                onChange={(e) => handleFileChange(e, 'glbFiles')}
                className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm text-gray-900 bg-white"
              />
              <p className="text-xs text-gray-500 mt-1">Upload GLB/GLTF files for 3D customizer (multiple scenes/views)</p>
              {files.glbFiles.length > 0 && (
                <div className="mt-2">
                  <p className="text-sm text-green-600">{files.glbFiles.length} GLB file(s) selected:</p>
                  <ul className="text-xs text-gray-600 ml-4">
                    {files.glbFiles.map((file, index) => (
                      <li key={index}>• {file.name} ({(file.size / 1024 / 1024).toFixed(2)} MB)</li>
                    ))}
                  </ul>
                </div>
              )}
            </div>

            {/* Texture Files */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Texture Files
              </label>
              <input
                type="file"
                multiple
                accept=".png,.jpg,.jpeg"
                onChange={(e) => handleFileChange(e, 'textureFiles')}
                className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm text-gray-900 bg-white"
              />
              <p className="text-xs text-gray-500 mt-1">Upload texture files for 3D models (PNG, JPG)</p>
              {files.textureFiles.length > 0 && (
                <div className="mt-2">
                  <p className="text-sm text-green-600">{files.textureFiles.length} texture file(s) selected:</p>
                  <ul className="text-xs text-gray-600 ml-4">
                    {files.textureFiles.map((file, index) => (
                      <li key={index}>• {file.name} ({(file.size / 1024 / 1024).toFixed(2)} MB)</li>
                    ))}
                  </ul>
                </div>
              )}
            </div>

            {/* Preview Files */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                3D Preview Images
              </label>
              <input
                type="file"
                multiple
                accept="image/*"
                onChange={(e) => handleFileChange(e, 'previewFiles')}
                className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm text-gray-900 bg-white"
              />
              <p className="text-xs text-gray-500 mt-1">Upload preview images for 3D scenes (matching GLB files)</p>
              {files.previewFiles.length > 0 && (
                <div className="mt-2">
                  <p className="text-sm text-green-600">{files.previewFiles.length} preview file(s) selected:</p>
                  <ul className="text-xs text-gray-600 ml-4">
                    {files.previewFiles.map((file, index) => (
                      <li key={index}>• {file.name} ({(file.size / 1024 / 1024).toFixed(2)} MB)</li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          </div>

          {/* Product Variants */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h4 className="text-md font-medium text-gray-900">Product Variants</h4>
              <button
                type="button"
                onClick={addVariant}
                className="bg-green-600 text-white px-3 py-1 rounded-md text-sm hover:bg-green-700"
              >
                Add Variant
              </button>
            </div>

            {variants.map((variant, index) => (
              <div key={variant.id} className="border border-gray-200 rounded-lg p-4 space-y-3">
                <div className="flex items-center justify-between">
                  <h5 className="text-sm font-medium text-gray-800">Variant {index + 1}</h5>
                  {variants.length > 1 && (
                    <button
                      type="button"
                      onClick={() => removeVariant(variant.id)}
                      className="text-red-600 hover:text-red-800 text-sm"
                    >
                      Remove
                    </button>
                  )}
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Variant Title
                    </label>
                    <Input
                      value={variant.title}
                      onChange={(e) => updateVariant(variant.id, 'title', e.target.value)}
                      placeholder="e.g., Small Red"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Price ($)
                    </label>
                    <Input
                      type="number"
                      step="0.01"
                      value={variant.price}
                      onChange={(e) => updateVariant(variant.id, 'price', e.target.value)}
                      placeholder="19.99"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Compare at Price ($)
                    </label>
                    <Input
                      type="number"
                      step="0.01"
                      value={variant.compareAtPrice}
                      onChange={(e) => updateVariant(variant.id, 'compareAtPrice', e.target.value)}
                      placeholder="24.99"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      SKU
                    </label>
                    <Input
                      value={variant.sku}
                      onChange={(e) => updateVariant(variant.id, 'sku', e.target.value)}
                      placeholder="TSH-001-SM-RED"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Inventory Quantity
                    </label>
                    <Input
                      type="number"
                      value={variant.inventoryQuantity}
                      onChange={(e) => updateVariant(variant.id, 'inventoryQuantity', e.target.value)}
                      placeholder="100"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Weight (lbs)
                    </label>
                    <Input
                      type="number"
                      step="0.01"
                      value={variant.weight}
                      onChange={(e) => updateVariant(variant.id, 'weight', e.target.value)}
                      placeholder="0.5"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Option 1 (e.g., Size)
                    </label>
                    <Input
                      value={variant.option1}
                      onChange={(e) => updateVariant(variant.id, 'option1', e.target.value)}
                      placeholder="Small"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Option 2 (e.g., Color)
                    </label>
                    <Input
                      value={variant.option2}
                      onChange={(e) => updateVariant(variant.id, 'option2', e.target.value)}
                      placeholder="Red"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Option 3 (e.g., Material)
                    </label>
                    <Input
                      value={variant.option3}
                      onChange={(e) => updateVariant(variant.id, 'option3', e.target.value)}
                      placeholder="Cotton"
                    />
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Options */}
          <div className="flex items-center">
            <input
              type="checkbox"
              name="featured"
              checked={formData.featured}
              onChange={handleInputChange}
              className="h-4 w-4 text-blue-600 border-gray-300 rounded"
            />
            <label className="ml-2 text-sm text-gray-700">
              Featured Product
            </label>
          </div>

          {/* Actions */}
          <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              disabled={isLoading}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isLoading}
            >
              {isLoading ? 'Creating...' : 'Create Product'}
            </Button>
          </div>
        </form>
      </div>
    </div>
  )
}
