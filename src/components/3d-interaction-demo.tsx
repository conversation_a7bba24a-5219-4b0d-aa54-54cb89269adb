/**
 * 3D Interaction Demo Component
 * Demonstrates the complete 3D interaction system
 */

'use client';

import React, { useState, useCallback } from 'react';
import { ThreeDInteractionManager } from '@/lib/3d-interaction-manager';
import { DesignElement } from '@/lib/fabric-threejs-bridge';
import { DesignAreaUV } from '@/lib/uv-extractor';
import { runIntegrationTests } from '@/tests/test-runner';
import Simple3DViewer from '@/components/Simple3DViewer';

interface DemoProps {
  glbUrl: string;
}

export default function ThreeDInteractionDemo({ glbUrl }: DemoProps) {
  // State
  const [interactionManager, setInteractionManager] = useState<ThreeDInteractionManager | null>(null);
  const [designElements, setDesignElements] = useState<DesignElement[]>([]);
  const [selectedElement, setSelectedElement] = useState<string | null>(null);
  const [selectedDesignArea, setSelectedDesignArea] = useState<DesignAreaUV | null>(null);
  const [designAreas, setDesignAreas] = useState<DesignAreaUV[]>([]);
  const [testResults, setTestResults] = useState<any>(null);
  const [isRunningTests, setIsRunningTests] = useState(false);

  // Handle interaction manager ready
  const handleInteractionReady = useCallback((manager: ThreeDInteractionManager) => {
    console.log('🎮 Demo: Interaction manager ready');
    setInteractionManager(manager);
  }, []);

  // Handle design areas extracted
  const handleDesignAreasExtracted = useCallback((areas: DesignAreaUV[]) => {
    console.log('🎮 Demo: Design areas extracted', areas);
    setDesignAreas(areas);
    if (areas.length > 0) {
      setSelectedDesignArea(areas[0]);
    }
  }, []);

  // Handle mesh click
  const handleMeshClick = useCallback((meshName: string) => {
    console.log('🎮 Demo: Mesh clicked', meshName);
    const area = designAreas.find(a => a.areaName === meshName);
    if (area) {
      setSelectedDesignArea(area);
    }
  }, [designAreas]);

  // Add text element
  const addTextElement = useCallback(() => {
    if (!selectedDesignArea) {
      alert('Please select a design area first');
      return;
    }

    const element: DesignElement = {
      id: `text_${Date.now()}`,
      type: 'text',
      x: 256, // Center of 512x512 canvas
      y: 256,
      width: 200,
      height: 50,
      rotation: 0,
      zIndex: designElements.length,
      data: {
        text: 'Sample Text',
        fontSize: 24,
        color: '#000000',
        fontFamily: 'Arial'
      }
    };

    setDesignElements(prev => [...prev, element]);
    setSelectedElement(element.id);
  }, [selectedDesignArea, designElements.length]);

  // Add image element
  const addImageElement = useCallback(() => {
    if (!selectedDesignArea) {
      alert('Please select a design area first');
      return;
    }

    // Create a simple colored rectangle as demo image
    const canvas = document.createElement('canvas');
    canvas.width = 100;
    canvas.height = 100;
    const ctx = canvas.getContext('2d');
    if (ctx) {
      ctx.fillStyle = '#ff6b6b';
      ctx.fillRect(0, 0, 100, 100);
      ctx.fillStyle = '#ffffff';
      ctx.font = '20px Arial';
      ctx.textAlign = 'center';
      ctx.fillText('IMG', 50, 55);
    }

    const element: DesignElement = {
      id: `image_${Date.now()}`,
      type: 'image',
      x: 206, // Center of 512x512 canvas minus half width
      y: 206,
      width: 100,
      height: 100,
      rotation: 0,
      zIndex: designElements.length,
      data: {
        src: canvas.toDataURL()
      }
    };

    setDesignElements(prev => [...prev, element]);
    setSelectedElement(element.id);
  }, [selectedDesignArea, designElements.length]);

  // Delete selected element
  const deleteSelectedElement = useCallback(() => {
    if (!selectedElement) return;

    setDesignElements(prev => prev.filter(el => el.id !== selectedElement));
    setSelectedElement(null);
  }, [selectedElement]);

  // Clear all elements
  const clearAllElements = useCallback(() => {
    setDesignElements([]);
    setSelectedElement(null);
  }, []);

  // Run integration tests
  const runTests = useCallback(async () => {
    setIsRunningTests(true);
    try {
      const results = await runIntegrationTests();
      setTestResults(results);
    } catch (error) {
      console.error('Test error:', error);
      setTestResults({ passed: 0, failed: 1, results: [{ test: 'Test execution', passed: false, error: 'Failed to run tests' }] });
    } finally {
      setIsRunningTests(false);
    }
  }, []);

  // Update element
  const updateElement = useCallback((id: string, updates: Partial<DesignElement>) => {
    setDesignElements(prev => prev.map(el => el.id === id ? { ...el, ...updates } : el));
  }, []);

  return (
    <div className="w-full h-screen bg-gray-100 flex flex-col">
      {/* Header */}
      <div className="bg-white shadow-sm border-b p-4">
        <h1 className="text-2xl font-bold text-gray-800">3D Interaction System Demo</h1>
        <p className="text-gray-600 mt-1">
          Interactive 3D design elements with Canva-like functionality
        </p>
      </div>

      <div className="flex-1 flex">
        {/* Left Panel - Controls */}
        <div className="w-80 bg-white shadow-sm border-r p-4 overflow-y-auto">
          <div className="space-y-6">
            {/* Design Area Selection */}
            <div>
              <h3 className="text-lg font-semibold mb-3">Design Areas</h3>
              <div className="space-y-2">
                {designAreas.map(area => (
                  <button
                    key={area.areaName}
                    onClick={() => setSelectedDesignArea(area)}
                    className={`w-full p-2 text-left rounded border ${
                      selectedDesignArea?.areaName === area.areaName
                        ? 'bg-blue-100 border-blue-300'
                        : 'bg-gray-50 border-gray-200 hover:bg-gray-100'
                    }`}
                  >
                    {area.areaName}
                  </button>
                ))}
              </div>
            </div>

            {/* Element Controls */}
            <div>
              <h3 className="text-lg font-semibold mb-3">Add Elements</h3>
              <div className="space-y-2">
                <button
                  onClick={addTextElement}
                  disabled={!selectedDesignArea}
                  className="w-full p-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:bg-gray-300"
                >
                  Add Text
                </button>
                <button
                  onClick={addImageElement}
                  disabled={!selectedDesignArea}
                  className="w-full p-2 bg-green-500 text-white rounded hover:bg-green-600 disabled:bg-gray-300"
                >
                  Add Image
                </button>
              </div>
            </div>

            {/* Element Management */}
            <div>
              <h3 className="text-lg font-semibold mb-3">Element Management</h3>
              <div className="space-y-2">
                <button
                  onClick={deleteSelectedElement}
                  disabled={!selectedElement}
                  className="w-full p-2 bg-red-500 text-white rounded hover:bg-red-600 disabled:bg-gray-300"
                >
                  Delete Selected
                </button>
                <button
                  onClick={clearAllElements}
                  disabled={designElements.length === 0}
                  className="w-full p-2 bg-orange-500 text-white rounded hover:bg-orange-600 disabled:bg-gray-300"
                >
                  Clear All
                </button>
              </div>
            </div>

            {/* Element List */}
            <div>
              <h3 className="text-lg font-semibold mb-3">Elements ({designElements.length})</h3>
              <div className="space-y-1 max-h-40 overflow-y-auto">
                {designElements.map(element => (
                  <div
                    key={element.id}
                    onClick={() => setSelectedElement(element.id)}
                    className={`p-2 text-sm rounded cursor-pointer ${
                      selectedElement === element.id
                        ? 'bg-blue-100 border border-blue-300'
                        : 'bg-gray-50 hover:bg-gray-100'
                    }`}
                  >
                    <div className="font-medium">{element.type.toUpperCase()}</div>
                    <div className="text-gray-500 text-xs">
                      {element.type === 'text' ? element.data.text : element.id}
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Testing */}
            <div>
              <h3 className="text-lg font-semibold mb-3">Integration Tests</h3>
              <button
                onClick={runTests}
                disabled={isRunningTests}
                className="w-full p-2 bg-purple-500 text-white rounded hover:bg-purple-600 disabled:bg-gray-300"
              >
                {isRunningTests ? 'Running Tests...' : 'Run Tests'}
              </button>
              
              {testResults && (
                <div className="mt-3 p-3 bg-gray-50 rounded text-sm">
                  <div className="font-medium mb-2">
                    Test Results: {testResults.passed} passed, {testResults.failed} failed
                  </div>
                  <div className="space-y-1 max-h-32 overflow-y-auto">
                    {testResults.results.map((result: any, index: number) => (
                      <div
                        key={index}
                        className={`text-xs ${result.passed ? 'text-green-600' : 'text-red-600'}`}
                      >
                        {result.passed ? '✅' : '❌'} {result.test}
                        {result.error && <div className="text-red-500 ml-4">{result.error}</div>}
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>

            {/* Instructions */}
            <div>
              <h3 className="text-lg font-semibold mb-3">Instructions</h3>
              <div className="text-sm text-gray-600 space-y-2">
                <p>1. Click on design areas (front, back, sleeves) to select them</p>
                <p>2. Add text or image elements to the selected area</p>
                <p>3. Click on elements in the 3D view to select them</p>
                <p>4. Drag elements directly on the 3D model</p>
                <p>5. Use transform handles to rotate and scale</p>
                <p>6. Run integration tests to verify functionality</p>
              </div>
            </div>
          </div>
        </div>

        {/* Right Panel - 3D Viewer */}
        <div className="flex-1 flex items-center justify-center p-4">
          <div className="bg-white rounded-lg shadow-lg p-4">
            <Simple3DViewer
              glbUrl={glbUrl}
              width={600}
              height={600}
              responsive={false}
              showWireframe={false}
              onDesignAreasExtracted={handleDesignAreasExtracted}
              onMeshClick={handleMeshClick}
              designElements={designElements}
              selectedElement={selectedElement}
              onSelectElement={setSelectedElement}
              onUpdateElement={updateElement}
              selectedDesignArea={selectedDesignArea?.areaName}
              enable3DInteraction={true}
              interactionConfig={{
                enableHoverEffects: true,
                enableTransformHandles: true,
                enableVisualSelectors: true,
                enableSurfaceDragging: true,
                debugMode: false
              }}
              onInteractionReady={handleInteractionReady}
              enablePerformanceOptimization={true}
              performanceConfig={{
                maxFPS: 60,
                enableFrustumCulling: true,
                enableLOD: false,
                textureCompressionLevel: 1
              }}
            />
          </div>
        </div>
      </div>
    </div>
  );
}
