'use client'

import { useEffect } from 'react'

export default function SafariViewportFix() {
  useEffect(() => {
    // Only run on client side
    if (typeof window === 'undefined') return

    // Detect if we're on iPhone Safari
    const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent)
    const isSafari = /Safari/.test(navigator.userAgent) && !/Chrome/.test(navigator.userAgent)
    const isIOSSafari = isIOS && isSafari

    if (!isIOSSafari) return

    // Function to apply safe area fixes
    const applySafeAreaFix = () => {
      // Get the actual safe area inset values
      const safeAreaBottom = getComputedStyle(document.documentElement)
        .getPropertyValue('--safe-area-inset-bottom') || 
        getComputedStyle(document.documentElement)
        .getPropertyValue('env(safe-area-inset-bottom)') ||
        '0px'

      // If safe area inset is available, use it
      if (safeAreaBottom && safeAreaBottom !== '0px') {
        document.documentElement.style.setProperty('--calculated-safe-area-bottom', safeAreaBottom)
      } else {
        // Fallback: detect viewport height changes to estimate safe area
        const viewportHeight = window.visualViewport?.height || window.innerHeight
        const documentHeight = document.documentElement.clientHeight
        
        // If there's a significant difference, we likely have browser UI
        const heightDifference = Math.abs(documentHeight - viewportHeight)

        if (heightDifference > 50) {
          // Estimate safe area based on height difference, but ensure minimum
          const estimatedSafeArea = Math.max(heightDifference, 60) // Minimum 60px
          document.documentElement.style.setProperty('--calculated-safe-area-bottom', `${estimatedSafeArea}px`)
        } else {
          // Increased default safe area for iPhone - more generous padding
          const defaultSafeArea = window.innerHeight > 800 ? '60px' : '50px' // Larger phones get more padding
          document.documentElement.style.setProperty('--calculated-safe-area-bottom', defaultSafeArea)
        }
      }

      // Apply the fix to body
      document.body.style.paddingBottom = `var(--calculated-safe-area-bottom, 3rem)`

      // Add a class to indicate safe area is active
      document.documentElement.classList.add('ios-safari-safe-area-active')

      // Apply safe area to all main containers and pages
      const mainContainers = document.querySelectorAll('main, .min-h-screen, [class*="min-h-screen"]')
      mainContainers.forEach(container => {
        if (container instanceof HTMLElement) {
          container.style.paddingBottom = `var(--calculated-safe-area-bottom, 3rem)`
        }
      })

      // Apply to elements with safe area classes
      const safeAreaElements = document.querySelectorAll('.pb-safe-bottom, .mb-safe-bottom')
      safeAreaElements.forEach(element => {
        if (element instanceof HTMLElement) {
          if (element.classList.contains('pb-safe-bottom')) {
            element.style.paddingBottom = `var(--calculated-safe-area-bottom, 3rem)`
          }
          if (element.classList.contains('mb-safe-bottom')) {
            element.style.marginBottom = `var(--calculated-safe-area-bottom, 3rem)`
          }
        }
      })
    }

    // Function to handle viewport changes
    const handleViewportChange = () => {
      // Debounce the fix application
      clearTimeout(window.safeAreaTimeout)
      window.safeAreaTimeout = setTimeout(applySafeAreaFix, 100)
    }

    // Apply initial fix
    applySafeAreaFix()

    // Listen for viewport changes
    if (window.visualViewport) {
      window.visualViewport.addEventListener('resize', handleViewportChange)
      window.visualViewport.addEventListener('scroll', handleViewportChange)
    }

    // Listen for orientation changes
    window.addEventListener('orientationchange', () => {
      setTimeout(applySafeAreaFix, 500) // Delay to allow orientation to complete
    })

    // Listen for window resize
    window.addEventListener('resize', handleViewportChange)

    // Cleanup
    return () => {
      if (window.visualViewport) {
        window.visualViewport.removeEventListener('resize', handleViewportChange)
        window.visualViewport.removeEventListener('scroll', handleViewportChange)
      }
      window.removeEventListener('orientationchange', applySafeAreaFix)
      window.removeEventListener('resize', handleViewportChange)
      clearTimeout(window.safeAreaTimeout)
    }
  }, [])

  return null // This component doesn't render anything
}

// Extend Window interface for TypeScript
declare global {
  interface Window {
    safeAreaTimeout: NodeJS.Timeout
  }
}
