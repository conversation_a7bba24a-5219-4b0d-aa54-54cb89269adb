'use client'

import { usePathname } from 'next/navigation'
import { Head<PERSON> } from './header'
import { Footer } from './footer'

interface ConditionalLayoutProps {
  children: React.ReactNode
}

export default function ConditionalLayout({ children }: ConditionalLayoutProps) {
  const pathname = usePathname()
  
  // Hide header/footer for admin, dashboard, customizer, and embedded shopify pages
  const hideHeaderFooter =
    pathname.startsWith('/admin') ||
    pathname.startsWith('/dashboard') ||
    pathname.startsWith('/customizer') ||
    pathname.startsWith('/shopify')

  if (hideHeaderFooter) {
    return <>{children}</>
  }

  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      <main className="flex-1 pb-safe-bottom">
        {children}
      </main>
      <Footer />
    </div>
  )
}
