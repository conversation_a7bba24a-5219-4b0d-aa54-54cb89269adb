'use client'

import Link from 'next/link'
import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Menu, X, User, Settings, LogOut } from 'lucide-react'
import { cn } from '@/utils/cn'
import { useAuth } from '@/components/providers/auth-provider'

export function Header() {
  const { user, userProfile, signOut } = useAuth()
  const [isMenuOpen, setIsMenuOpen] = useState(false)

  const navigation = [
    { name: 'Home', href: '/' },
    { name: 'Catalog', href: '/catalog' },
    { name: 'Customizer', href: '/customizer' },
    { name: 'About', href: '/about' },
  ]

  const userNavigation = userProfile
    ? [
        {
          name: 'Dashboard',
          href: userProfile.role === 'super_admin' ? '/admin' : '/dashboard'
        },
        { name: 'Settings', href: '/settings' },
        { name: 'Sign out', href: '#', onClick: signOut },
      ]
    : [
        { name: 'Sign in', href: '/auth/signin' },
        { name: 'Sign up', href: '/auth/signup' },
      ]

  return (
    <header className="sticky top-0 z-50 w-full border-b border-gray-200 bg-white/80 backdrop-blur-md">
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div className="flex h-16 items-center justify-between">
          {/* Logo */}
          <div className="flex items-center">
            <Link href="/" className="flex items-center space-x-2">
              <div className="h-8 w-8 rounded-lg bg-gradient-to-br from-blue-600 to-purple-600"></div>
              <span className="text-xl font-semibold text-gray-900">
                Biypod Customizer
              </span>
            </Link>
          </div>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-8">
            {navigation.map((item) => (
              <Link
                key={item.name}
                href={item.href}
                className="text-sm font-medium text-gray-700 hover:text-gray-900 transition-colors"
              >
                {item.name}
              </Link>
            ))}
          </nav>

          {/* User Menu */}
          <div className="hidden md:flex items-center space-x-4">
            {userProfile ? (
              <div className="flex items-center space-x-2">
                <span className="text-sm text-gray-700">{userProfile.email}</span>
                <div className="flex items-center space-x-1">
                  {userNavigation.map((item) => (
                    item.onClick ? (
                      <Button
                        key={item.name}
                        variant="ghost"
                        size="sm"
                        onClick={item.onClick}
                      >
                        {item.name === 'Dashboard' && <User className="h-4 w-4 mr-1" />}
                        {item.name === 'Settings' && <Settings className="h-4 w-4 mr-1" />}
                        {item.name === 'Sign out' && <LogOut className="h-4 w-4 mr-1" />}
                        {item.name}
                      </Button>
                    ) : (
                      <Link key={item.name} href={item.href}>
                        <Button variant="ghost" size="sm">
                          {item.name === 'Dashboard' && <User className="h-4 w-4 mr-1" />}
                          {item.name === 'Settings' && <Settings className="h-4 w-4 mr-1" />}
                          {item.name === 'Sign out' && <LogOut className="h-4 w-4 mr-1" />}
                          {item.name}
                        </Button>
                      </Link>
                    )
                  ))}
                </div>
              </div>
            ) : (
              <div className="flex items-center space-x-2">
                <Link href="/auth/signin">
                  <Button variant="ghost">Sign in</Button>
                </Link>
                <Link href="/auth/signup">
                  <Button variant="apple">Get Started</Button>
                </Link>
              </div>
            )}
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setIsMenuOpen(!isMenuOpen)}
            >
              {isMenuOpen ? (
                <X className="h-6 w-6" />
              ) : (
                <Menu className="h-6 w-6" />
              )}
            </Button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="md:hidden">
            <div className="px-2 pt-2 pb-3 space-y-1 sm:px-3 border-t border-gray-200">
              {navigation.map((item) => (
                <Link
                  key={item.name}
                  href={item.href}
                  className="block px-3 py-2 text-base font-medium text-gray-700 hover:text-gray-900 hover:bg-gray-50 rounded-md"
                  onClick={() => setIsMenuOpen(false)}
                >
                  {item.name}
                </Link>
              ))}
              <div className="border-t border-gray-200 pt-4">
                {userNavigation.map((item) => (
                  item.onClick ? (
                    <button
                      key={item.name}
                      onClick={() => {
                        item.onClick?.()
                        setIsMenuOpen(false)
                      }}
                      className="block w-full text-left px-3 py-2 text-base font-medium text-gray-700 hover:text-gray-900 hover:bg-gray-50 rounded-md"
                    >
                      {item.name}
                    </button>
                  ) : (
                    <Link
                      key={item.name}
                      href={item.href}
                      className="block px-3 py-2 text-base font-medium text-gray-700 hover:text-gray-900 hover:bg-gray-50 rounded-md"
                      onClick={() => setIsMenuOpen(false)}
                    >
                      {item.name}
                    </Link>
                  )
                ))}
              </div>
            </div>
          </div>
        )}
      </div>
    </header>
  )
}
