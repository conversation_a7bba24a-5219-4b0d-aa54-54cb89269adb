/**
 * SVG Mapping Engine for Product Customizer
 * Handles coordinate transformation between mockup and print file views
 * Print file is the source of truth at 300 DPI
 */

export interface ProductCanvasConfig {
  print_file: {
    width_px: number      // e.g., 3000px (SOURCE OF TRUTH)
    height_px: number     // e.g., 4000px (SOURCE OF TRUTH)
    dpi: number          // Always 300 DPI
    width_inches: number  // calculated: width_px / 300
    height_inches: number // calculated: height_px / 300
  }
  display: {
    width_px: number      // e.g., 800px (scaled for UI)
    height_px: number     // e.g., 1067px (scaled for UI)
    scale_factor: number  // calculated: display_width / print_width
  }
}

export interface MappingArea {
  id: string
  area_id: string
  area_name: string
  view_type: 'mockup' | 'printfile'
  svg_path: string
  display_bounds: {
    x: number
    y: number
    width: number
    height: number
  }
  print_bounds: {
    x: number
    y: number
    width: number
    height: number
  }
  transform_matrix?: number[] // 6-element transformation matrix [a, b, c, d, e, f]
}

export interface DesignElementPosition {
  x: number
  y: number
  width: number
  height: number
  rotation: number
  area_id: string
}

export interface TransformationResult {
  x: number
  y: number
  width: number
  height: number
  rotation: number
  area_id: string
  success: boolean
  error?: string
}

/**
 * SVG Mapping Engine Class
 */
export class SVGMappingEngine {
  private mockupAreas: Map<string, MappingArea> = new Map()
  private printFileAreas: Map<string, MappingArea> = new Map()
  private canvasConfig: ProductCanvasConfig

  constructor(mappingAreas: MappingArea[], canvasConfig: ProductCanvasConfig) {
    this.canvasConfig = canvasConfig
    this.loadMappingAreas(mappingAreas)
  }

  /**
   * Load mapping areas into the engine
   */
  private loadMappingAreas(areas: MappingArea[]) {
    this.mockupAreas.clear()
    this.printFileAreas.clear()

    areas.forEach(area => {
      if (area.view_type === 'mockup') {
        this.mockupAreas.set(area.area_id, area)
      } else if (area.view_type === 'printfile') {
        this.printFileAreas.set(area.area_id, area)
      }
    })
  }

  /**
   * Transform coordinates from mockup to print file view
   * This transforms from display coordinates to high-res print coordinates
   */
  transformMockupToPrintFile(element: DesignElementPosition): TransformationResult {
    const mockupArea = this.mockupAreas.get(element.area_id)
    const printFileArea = this.printFileAreas.get(element.area_id)

    if (!mockupArea || !printFileArea) {
      return {
        ...element,
        success: false,
        error: `Mapping area '${element.area_id}' not found`
      }
    }

    try {
      // Step 1: Convert display coordinates to relative coordinates within mockup area
      const relativeX = (element.x - mockupArea.display_bounds.x) / mockupArea.display_bounds.width
      const relativeY = (element.y - mockupArea.display_bounds.y) / mockupArea.display_bounds.height
      const relativeWidth = element.width / mockupArea.display_bounds.width
      const relativeHeight = element.height / mockupArea.display_bounds.height

      // Step 2: Apply relative coordinates to print file area (high-res)
      const printX = printFileArea.print_bounds.x + (relativeX * printFileArea.print_bounds.width)
      const printY = printFileArea.print_bounds.y + (relativeY * printFileArea.print_bounds.height)
      const printWidth = relativeWidth * printFileArea.print_bounds.width
      const printHeight = relativeHeight * printFileArea.print_bounds.height

      // Step 3: Apply transformation matrix if available
      let finalX = printX
      let finalY = printY

      if (printFileArea.transform_matrix) {
        const transformed = this.applyTransformMatrix(printX, printY, printFileArea.transform_matrix)
        finalX = transformed.x
        finalY = transformed.y
      }

      return {
        x: finalX,
        y: finalY,
        width: printWidth,
        height: printHeight,
        rotation: element.rotation, // Rotation typically stays the same
        area_id: element.area_id,
        success: true
      }
    } catch (error) {
      return {
        ...element,
        success: false,
        error: `Transformation failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      }
    }
  }

  /**
   * Transform coordinates from print file to mockup view
   * This transforms from high-res print coordinates to display coordinates
   */
  transformPrintFileToMockup(element: DesignElementPosition): TransformationResult {
    const mockupArea = this.mockupAreas.get(element.area_id)
    const printFileArea = this.printFileAreas.get(element.area_id)

    if (!mockupArea || !printFileArea) {
      return {
        ...element,
        success: false,
        error: `Mapping area '${element.area_id}' not found`
      }
    }

    try {
      let sourceX = element.x
      let sourceY = element.y

      // Step 1: Apply inverse transformation matrix if available
      if (printFileArea.transform_matrix) {
        const inverted = this.invertTransformMatrix(printFileArea.transform_matrix)
        if (inverted) {
          const transformed = this.applyTransformMatrix(sourceX, sourceY, inverted)
          sourceX = transformed.x
          sourceY = transformed.y
        }
      }

      // Step 2: Convert from print coordinates to relative coordinates
      const relativeX = (sourceX - printFileArea.print_bounds.x) / printFileArea.print_bounds.width
      const relativeY = (sourceY - printFileArea.print_bounds.y) / printFileArea.print_bounds.height
      const relativeWidth = element.width / printFileArea.print_bounds.width
      const relativeHeight = element.height / printFileArea.print_bounds.height

      // Step 3: Apply to mockup display coordinates
      const displayX = mockupArea.display_bounds.x + (relativeX * mockupArea.display_bounds.width)
      const displayY = mockupArea.display_bounds.y + (relativeY * mockupArea.display_bounds.height)
      const displayWidth = relativeWidth * mockupArea.display_bounds.width
      const displayHeight = relativeHeight * mockupArea.display_bounds.height

      return {
        x: displayX,
        y: displayY,
        width: displayWidth,
        height: displayHeight,
        rotation: element.rotation,
        area_id: element.area_id,
        success: true
      }
    } catch (error) {
      return {
        ...element,
        success: false,
        error: `Transformation failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      }
    }
  }

  /**
   * Check if a point is within a specific area using bounding box
   * For complex SVG shapes, this could be enhanced with proper point-in-polygon detection
   */
  isPointInArea(x: number, y: number, areaId: string, viewType: 'mockup' | 'printfile'): boolean {
    const areas = viewType === 'mockup' ? this.mockupAreas : this.printFileAreas
    const area = areas.get(areaId)
    
    if (!area) return false

    const bounds = viewType === 'mockup' ? area.display_bounds : area.print_bounds
    return (
      x >= bounds.x &&
      x <= bounds.x + bounds.width &&
      y >= bounds.y &&
      y <= bounds.y + bounds.height
    )
  }

  /**
   * Get all available areas for a view type
   */
  getAreas(viewType: 'mockup' | 'printfile'): MappingArea[] {
    const areas = viewType === 'mockup' ? this.mockupAreas : this.printFileAreas
    return Array.from(areas.values())
  }

  /**
   * Find which area a point belongs to
   */
  findAreaForPoint(x: number, y: number, viewType: 'mockup' | 'printfile'): MappingArea | null {
    const areas = this.getAreas(viewType)
    return areas.find(area => this.isPointInArea(x, y, area.area_id, viewType)) || null
  }

  /**
   * Apply 2D transformation matrix
   */
  private applyTransformMatrix(x: number, y: number, matrix: number[]): { x: number; y: number } {
    if (matrix.length !== 6) {
      throw new Error('Transform matrix must have 6 elements')
    }

    const [a, b, c, d, e, f] = matrix
    return {
      x: a * x + c * y + e,
      y: b * x + d * y + f
    }
  }

  /**
   * Invert a 2D transformation matrix
   */
  private invertTransformMatrix(matrix: number[]): number[] | null {
    if (matrix.length !== 6) return null

    const [a, b, c, d, e, f] = matrix
    const det = a * d - b * c

    if (Math.abs(det) < 1e-10) return null // Matrix is not invertible

    return [
      d / det,
      -b / det,
      -c / det,
      a / det,
      (c * f - d * e) / det,
      (b * e - a * f) / det
    ]
  }
}

/**
 * Utility function to create SVG path from bounding box
 */
export function createRectanglePath(bounds: { x: number; y: number; width: number; height: number }): string {
  return `M${bounds.x},${bounds.y} L${bounds.x + bounds.width},${bounds.y} L${bounds.x + bounds.width},${bounds.y + bounds.height} L${bounds.x},${bounds.y + bounds.height} Z`
}

/**
 * Parse SVG path to get bounding box (simplified implementation)
 * For production, consider using a proper SVG path parser library for complex shapes
 */
export function getSVGPathBounds(path: string): { x: number; y: number; width: number; height: number } {
  const numbers = path.match(/[\d.-]+/g)?.map(Number) || []
  
  if (numbers.length < 4) {
    return { x: 0, y: 0, width: 100, height: 100 }
  }

  const xs = numbers.filter((_, i) => i % 2 === 0)
  const ys = numbers.filter((_, i) => i % 2 === 1)

  const minX = Math.min(...xs)
  const maxX = Math.max(...xs)
  const minY = Math.min(...ys)
  const maxY = Math.max(...ys)

  return {
    x: minX,
    y: minY,
    width: maxX - minX,
    height: maxY - minY
  }
}

/**
 * Calculate display dimensions from print configuration
 */
export function calculateDisplayDimensions(printConfig: {
  print_width_px: number
  print_height_px: number
  print_dpi: number
}, maxDisplayWidth = 800, maxDisplayHeight = 600): {
  display_width: number
  display_height: number
  scale_factor: number
} {
  const printAspectRatio = printConfig.print_width_px / printConfig.print_height_px
  
  let displayWidth = maxDisplayWidth
  let displayHeight = maxDisplayWidth / printAspectRatio
  
  if (displayHeight > maxDisplayHeight) {
    displayHeight = maxDisplayHeight
    displayWidth = maxDisplayHeight * printAspectRatio
  }
  
  const scaleFactor = displayWidth / printConfig.print_width_px
  
  return {
    display_width: displayWidth,
    display_height: displayHeight,
    scale_factor: scaleFactor
  }
}
