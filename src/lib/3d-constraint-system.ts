/**
 * 3D Constraint System
 * Implements boundaries and constraints for element movement within design areas
 */

import * as THREE from 'three';
import { Element3DPosition } from './3d-element-mapping';
import { DesignAreaUV } from './uv-extractor';

export interface ConstraintBounds {
  minU: number;
  maxU: number;
  minV: number;
  maxV: number;
  padding: number;
}

export interface ElementConstraints {
  elementId: string;
  bounds: ConstraintBounds;
  allowRotation: boolean;
  allowScaling: boolean;
  minScale: number;
  maxScale: number;
  snapToGrid: boolean;
  gridSize: number;
  maintainAspectRatio: boolean;
}

export interface ConstraintViolation {
  type: 'bounds' | 'scale' | 'rotation' | 'overlap';
  elementId: string;
  description: string;
  severity: 'warning' | 'error';
  suggestedFix?: Partial<Element3DPosition>;
}

export class ThreeDConstraintSystem {
  private elementConstraints: Map<string, ElementConstraints> = new Map();
  private designAreaBounds: Map<string, ConstraintBounds> = new Map();
  private globalConstraints: {
    preventOverlap: boolean;
    overlapTolerance: number;
    enforceDesignAreaBounds: boolean;
    allowNegativeSpace: boolean;
  } = {
    preventOverlap: true,
    overlapTolerance: 0.01,
    enforceDesignAreaBounds: true,
    allowNegativeSpace: false
  };

  constructor() {
    console.log('🔒 3D Constraint System initialized');
  }

  /**
   * Register design area bounds
   */
  registerDesignArea(designArea: DesignAreaUV): void {
    const bounds: ConstraintBounds = {
      minU: designArea.mapping.boundingBox.min[0],
      maxU: designArea.mapping.boundingBox.max[0],
      minV: designArea.mapping.boundingBox.min[1],
      maxV: designArea.mapping.boundingBox.max[1],
      padding: 0.05 // 5% padding from edges
    };

    this.designAreaBounds.set(designArea.areaName, bounds);
    console.log(`🔒 Registered design area bounds: ${designArea.areaName}`, bounds);
  }

  /**
   * Set constraints for a specific element
   */
  setElementConstraints(elementId: string, constraints: Partial<ElementConstraints>): void {
    const existing = this.elementConstraints.get(elementId) || {
      elementId,
      bounds: { minU: 0, maxU: 1, minV: 0, maxV: 1, padding: 0.05 },
      allowRotation: true,
      allowScaling: true,
      minScale: 0.1,
      maxScale: 5.0,
      snapToGrid: false,
      gridSize: 0.1,
      maintainAspectRatio: false
    };

    const updated = { ...existing, ...constraints };
    this.elementConstraints.set(elementId, updated);
    
    console.log(`🔒 Set constraints for element ${elementId}`, updated);
  }

  /**
   * Validate element position against constraints
   */
  validatePosition(
    elementId: string,
    position: Element3DPosition,
    designAreaName: string
  ): ConstraintViolation[] {
    const violations: ConstraintViolation[] = [];
    const constraints = this.elementConstraints.get(elementId);
    const areaBounds = this.designAreaBounds.get(designAreaName);

    // Check design area bounds
    if (this.globalConstraints.enforceDesignAreaBounds && areaBounds) {
      const boundsViolations = this.checkBoundsConstraints(
        elementId, position, areaBounds
      );
      violations.push(...boundsViolations);
    }

    // Check element-specific constraints
    if (constraints) {
      const elementViolations = this.checkElementConstraints(
        elementId, position, constraints
      );
      violations.push(...elementViolations);
    }

    // Check overlap constraints
    if (this.globalConstraints.preventOverlap) {
      const overlapViolations = this.checkOverlapConstraints(
        elementId, position, designAreaName
      );
      violations.push(...overlapViolations);
    }

    return violations;
  }

  /**
   * Apply constraints to a position (returns corrected position)
   */
  applyConstraints(
    elementId: string,
    position: Element3DPosition,
    designAreaName: string
  ): Element3DPosition {
    const correctedPosition = { ...position };
    const constraints = this.elementConstraints.get(elementId);
    const areaBounds = this.designAreaBounds.get(designAreaName);

    // Apply bounds constraints
    if (areaBounds) {
      this.applyBoundsConstraints(correctedPosition, areaBounds);
    }

    // Apply element constraints
    if (constraints) {
      this.applyElementConstraints(correctedPosition, constraints);
    }

    return correctedPosition;
  }

  /**
   * Check bounds constraints
   */
  private checkBoundsConstraints(
    elementId: string,
    position: Element3DPosition,
    bounds: ConstraintBounds
  ): ConstraintViolation[] {
    const violations: ConstraintViolation[] = [];
    const elementBounds = position.bounds;

    // Check if element extends beyond design area bounds
    if (elementBounds.min.u < bounds.minU + bounds.padding) {
      violations.push({
        type: 'bounds',
        elementId,
        description: 'Element extends beyond left boundary',
        severity: 'error',
        suggestedFix: {
          uvCoordinates: {
            u: bounds.minU + bounds.padding + (elementBounds.max.u - elementBounds.min.u) / 2,
            v: position.uvCoordinates.v
          }
        }
      });
    }

    if (elementBounds.max.u > bounds.maxU - bounds.padding) {
      violations.push({
        type: 'bounds',
        elementId,
        description: 'Element extends beyond right boundary',
        severity: 'error',
        suggestedFix: {
          uvCoordinates: {
            u: bounds.maxU - bounds.padding - (elementBounds.max.u - elementBounds.min.u) / 2,
            v: position.uvCoordinates.v
          }
        }
      });
    }

    if (elementBounds.min.v < bounds.minV + bounds.padding) {
      violations.push({
        type: 'bounds',
        elementId,
        description: 'Element extends beyond top boundary',
        severity: 'error',
        suggestedFix: {
          uvCoordinates: {
            u: position.uvCoordinates.u,
            v: bounds.minV + bounds.padding + (elementBounds.max.v - elementBounds.min.v) / 2
          }
        }
      });
    }

    if (elementBounds.max.v > bounds.maxV - bounds.padding) {
      violations.push({
        type: 'bounds',
        elementId,
        description: 'Element extends beyond bottom boundary',
        severity: 'error',
        suggestedFix: {
          uvCoordinates: {
            u: position.uvCoordinates.u,
            v: bounds.maxV - bounds.padding - (elementBounds.max.v - elementBounds.min.v) / 2
          }
        }
      });
    }

    return violations;
  }

  /**
   * Check element-specific constraints
   */
  private checkElementConstraints(
    elementId: string,
    position: Element3DPosition,
    constraints: ElementConstraints
  ): ConstraintViolation[] {
    const violations: ConstraintViolation[] = [];
    const transform = position.transform;

    // Check scale constraints
    if (transform.scaleX < constraints.minScale || transform.scaleX > constraints.maxScale) {
      violations.push({
        type: 'scale',
        elementId,
        description: `Scale X (${transform.scaleX.toFixed(2)}) outside allowed range [${constraints.minScale}, ${constraints.maxScale}]`,
        severity: 'warning',
        suggestedFix: {
          transform: {
            ...transform,
            scaleX: Math.max(constraints.minScale, Math.min(constraints.maxScale, transform.scaleX))
          }
        }
      });
    }

    if (transform.scaleY < constraints.minScale || transform.scaleY > constraints.maxScale) {
      violations.push({
        type: 'scale',
        elementId,
        description: `Scale Y (${transform.scaleY.toFixed(2)}) outside allowed range [${constraints.minScale}, ${constraints.maxScale}]`,
        severity: 'warning',
        suggestedFix: {
          transform: {
            ...transform,
            scaleY: Math.max(constraints.minScale, Math.min(constraints.maxScale, transform.scaleY))
          }
        }
      });
    }

    // Check rotation constraints
    if (!constraints.allowRotation && transform.rotation !== 0) {
      violations.push({
        type: 'rotation',
        elementId,
        description: 'Rotation not allowed for this element',
        severity: 'error',
        suggestedFix: {
          transform: {
            ...transform,
            rotation: 0
          }
        }
      });
    }

    return violations;
  }

  /**
   * Check overlap constraints
   */
  private checkOverlapConstraints(
    elementId: string,
    position: Element3DPosition,
    designAreaName: string
  ): ConstraintViolation[] {
    const violations: ConstraintViolation[] = [];
    
    // This would check against other elements in the same design area
    // For now, we'll return empty array as this requires access to all elements
    // In a full implementation, this would be integrated with the element mapper
    
    return violations;
  }

  /**
   * Apply bounds constraints to position
   */
  private applyBoundsConstraints(
    position: Element3DPosition,
    bounds: ConstraintBounds
  ): void {
    const elementBounds = position.bounds;
    const elementWidth = elementBounds.max.u - elementBounds.min.u;
    const elementHeight = elementBounds.max.v - elementBounds.min.v;

    // Constrain U coordinate
    const minAllowedU = bounds.minU + bounds.padding + elementWidth / 2;
    const maxAllowedU = bounds.maxU - bounds.padding - elementWidth / 2;
    position.uvCoordinates.u = Math.max(minAllowedU, Math.min(maxAllowedU, position.uvCoordinates.u));

    // Constrain V coordinate
    const minAllowedV = bounds.minV + bounds.padding + elementHeight / 2;
    const maxAllowedV = bounds.maxV - bounds.padding - elementHeight / 2;
    position.uvCoordinates.v = Math.max(minAllowedV, Math.min(maxAllowedV, position.uvCoordinates.v));

    // Update element bounds based on new position
    this.updateElementBounds(position, elementWidth, elementHeight);
  }

  /**
   * Apply element-specific constraints
   */
  private applyElementConstraints(
    position: Element3DPosition,
    constraints: ElementConstraints
  ): void {
    const transform = position.transform;

    // Apply scale constraints
    transform.scaleX = Math.max(constraints.minScale, Math.min(constraints.maxScale, transform.scaleX));
    transform.scaleY = Math.max(constraints.minScale, Math.min(constraints.maxScale, transform.scaleY));

    // Apply aspect ratio constraint
    if (constraints.maintainAspectRatio) {
      const avgScale = (transform.scaleX + transform.scaleY) / 2;
      transform.scaleX = avgScale;
      transform.scaleY = avgScale;
    }

    // Apply rotation constraint
    if (!constraints.allowRotation) {
      transform.rotation = 0;
    }

    // Apply grid snapping
    if (constraints.snapToGrid) {
      const gridSize = constraints.gridSize;
      position.uvCoordinates.u = Math.round(position.uvCoordinates.u / gridSize) * gridSize;
      position.uvCoordinates.v = Math.round(position.uvCoordinates.v / gridSize) * gridSize;
    }
  }

  /**
   * Update element bounds based on position and size
   */
  private updateElementBounds(
    position: Element3DPosition,
    width: number,
    height: number
  ): void {
    position.bounds = {
      min: {
        u: position.uvCoordinates.u - width / 2,
        v: position.uvCoordinates.v - height / 2
      },
      max: {
        u: position.uvCoordinates.u + width / 2,
        v: position.uvCoordinates.v + height / 2
      }
    };
  }

  /**
   * Get constraint violations for all elements in a design area
   */
  validateDesignArea(
    elements: Element3DPosition[],
    designAreaName: string
  ): ConstraintViolation[] {
    const allViolations: ConstraintViolation[] = [];

    elements.forEach(element => {
      const violations = this.validatePosition(element.elementId, element, designAreaName);
      allViolations.push(...violations);
    });

    return allViolations;
  }

  /**
   * Update global constraints
   */
  updateGlobalConstraints(constraints: Partial<typeof this.globalConstraints>): void {
    Object.assign(this.globalConstraints, constraints);
    console.log('🔒 Updated global constraints', this.globalConstraints);
  }

  /**
   * Remove constraints for an element
   */
  removeElementConstraints(elementId: string): void {
    this.elementConstraints.delete(elementId);
    console.log(`🔒 Removed constraints for element ${elementId}`);
  }

  /**
   * Get constraints for an element
   */
  getElementConstraints(elementId: string): ElementConstraints | null {
    return this.elementConstraints.get(elementId) || null;
  }

  /**
   * Get design area bounds
   */
  getDesignAreaBounds(designAreaName: string): ConstraintBounds | null {
    return this.designAreaBounds.get(designAreaName) || null;
  }

  /**
   * Check if position is valid (no violations)
   */
  isPositionValid(
    elementId: string,
    position: Element3DPosition,
    designAreaName: string
  ): boolean {
    const violations = this.validatePosition(elementId, position, designAreaName);
    return violations.filter(v => v.severity === 'error').length === 0;
  }

  /**
   * Get suggested position fix for violations
   */
  getSuggestedFix(
    elementId: string,
    position: Element3DPosition,
    designAreaName: string
  ): Element3DPosition | null {
    const violations = this.validatePosition(elementId, position, designAreaName);
    const errorViolations = violations.filter(v => v.severity === 'error' && v.suggestedFix);

    if (errorViolations.length === 0) return null;

    // Apply all suggested fixes
    const fixedPosition = { ...position };
    errorViolations.forEach(violation => {
      if (violation.suggestedFix) {
        Object.assign(fixedPosition, violation.suggestedFix);
      }
    });

    return fixedPosition;
  }

  /**
   * Clear all constraints
   */
  clear(): void {
    this.elementConstraints.clear();
    this.designAreaBounds.clear();
    console.log('🔒 Cleared all constraints');
  }

  /**
   * Get constraint statistics
   */
  getStats(): {
    elementConstraints: number;
    designAreas: number;
    globalConstraints: {
      preventOverlap: boolean;
      overlapTolerance: number;
      enforceDesignAreaBounds: boolean;
      allowNegativeSpace: boolean;
    };
  } {
    return {
      elementConstraints: this.elementConstraints.size,
      designAreas: this.designAreaBounds.size,
      globalConstraints: { ...this.globalConstraints }
    };
  }
}
