import { createClient } from '@supabase/supabase-js'
import { createBrowserClient } from '@supabase/ssr'
import type { Database } from './database.types'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

// Client-side Supabase client
export const supabase = createClient<Database>(supabaseUrl, supabaseAnonKey)

// Singleton instance to prevent multiple GoTrueClient instances
let supabaseBrowserClient: ReturnType<typeof createBrowserClient<Database>> | null = null

// Browser client for client components
export const createSupabaseBrowserClient = () => {
  if (!supabaseBrowserClient) {
    supabaseBrowserClient = createBrowserClient<Database>(supabaseUrl, supabaseAnonKey)
  }
  return supabaseBrowserClient
}
