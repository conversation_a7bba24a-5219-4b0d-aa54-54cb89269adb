/**
 * 3D Transform Handles
 * Handles interactive transform operations (move, rotate, scale) for design elements in 3D space
 */

import * as THREE from 'three';
import { Element3DPosition, ThreeDElementMapper } from './3d-element-mapping';
import { ThreeDVisualSelectors } from './3d-visual-selectors';

export interface TransformOperation {
  type: 'move' | 'rotate' | 'scale' | 'resize';
  elementId: string;
  startPosition: Element3DPosition;
  currentPosition: Element3DPosition;
  delta: {
    x: number;
    y: number;
    rotation: number;
    scaleX: number;
    scaleY: number;
  };
}

export interface TransformConstraints {
  minScale: number;
  maxScale: number;
  snapToGrid: boolean;
  snapAngle: number; // degrees
  constrainToDesignArea: boolean;
}

export class ThreeDTransformHandles {
  private elementMapper: ThreeDElementMapper;
  private visualSelectors: ThreeDVisualSelectors;
  private camera: THREE.Camera;
  private canvas: HTMLElement;
  private raycaster: THREE.Raycaster;
  private mouse: THREE.Vector2;
  
  // Transform state
  private activeTransform: TransformOperation | null = null;
  private transformStartMouse = new THREE.Vector2();
  private transformCurrentMouse = new THREE.Vector2();
  private isTransforming = false;
  
  // Constraints
  private constraints: TransformConstraints = {
    minScale: 0.1,
    maxScale: 5.0,
    snapToGrid: false,
    snapAngle: 15, // degrees
    constrainToDesignArea: true
  };
  
  // Callbacks
  private onTransformStart?: (operation: TransformOperation) => void;
  private onTransformUpdate?: (operation: TransformOperation) => void;
  private onTransformEnd?: (operation: TransformOperation) => void;

  constructor(
    elementMapper: ThreeDElementMapper,
    visualSelectors: ThreeDVisualSelectors,
    camera: THREE.Camera,
    canvas: HTMLElement
  ) {
    this.elementMapper = elementMapper;
    this.visualSelectors = visualSelectors;
    this.camera = camera;
    this.canvas = canvas;
    this.raycaster = new THREE.Raycaster();
    this.mouse = new THREE.Vector2();

    this.setupEventListeners();
  }

  /**
   * Setup event listeners for transform operations
   */
  private setupEventListeners(): void {
    this.canvas.addEventListener('mousedown', this.handleMouseDown.bind(this));
    this.canvas.addEventListener('mousemove', this.handleMouseMove.bind(this));
    this.canvas.addEventListener('mouseup', this.handleMouseUp.bind(this));
    
    // Touch events
    this.canvas.addEventListener('touchstart', this.handleTouchStart.bind(this));
    this.canvas.addEventListener('touchmove', this.handleTouchMove.bind(this));
    this.canvas.addEventListener('touchend', this.handleTouchEnd.bind(this));
  }

  /**
   * Handle mouse down for transform operations
   */
  private handleMouseDown(event: MouseEvent): void {
    const handleInfo = this.getHandleAtPosition(event.clientX, event.clientY);
    
    if (handleInfo) {
      this.startTransform(handleInfo.elementId, handleInfo.handleType, event.clientX, event.clientY);
      event.preventDefault();
      event.stopPropagation();
    }
  }

  /**
   * Handle mouse move for transform operations
   */
  private handleMouseMove(event: MouseEvent): void {
    if (this.isTransforming && this.activeTransform) {
      this.updateTransform(event.clientX, event.clientY);
      event.preventDefault();
    }
  }

  /**
   * Handle mouse up for transform operations
   */
  private handleMouseUp(event: MouseEvent): void {
    if (this.isTransforming) {
      this.endTransform();
      event.preventDefault();
    }
  }

  /**
   * Start a transform operation
   */
  private startTransform(elementId: string, handleType: string, screenX: number, screenY: number): void {
    const position = this.elementMapper.getElementPosition(elementId);
    if (!position) return;

    this.transformStartMouse.set(screenX, screenY);
    this.transformCurrentMouse.set(screenX, screenY);
    
    this.activeTransform = {
      type: this.getTransformType(handleType),
      elementId,
      startPosition: { ...position },
      currentPosition: { ...position },
      delta: {
        x: 0,
        y: 0,
        rotation: 0,
        scaleX: 1,
        scaleY: 1
      }
    };
    
    this.isTransforming = true;
    
    // Update interaction state
    this.elementMapper.updateInteractionState(elementId, {
      isTransforming: true,
      transformType: this.activeTransform.type
    });
    
    // Trigger callback
    if (this.onTransformStart) {
      this.onTransformStart(this.activeTransform);
    }
    
    console.log(`🔧 Transform started: ${this.activeTransform.type} on ${elementId}`);
  }

  /**
   * Update transform operation
   */
  private updateTransform(screenX: number, screenY: number): void {
    if (!this.activeTransform) return;

    this.transformCurrentMouse.set(screenX, screenY);
    
    const deltaX = screenX - this.transformStartMouse.x;
    const deltaY = screenY - this.transformStartMouse.y;
    
    switch (this.activeTransform.type) {
      case 'move':
        this.updateMoveTransform(deltaX, deltaY);
        break;
      case 'rotate':
        this.updateRotateTransform(deltaX, deltaY);
        break;
      case 'scale':
        this.updateScaleTransform(deltaX, deltaY);
        break;
      case 'resize':
        this.updateResizeTransform(deltaX, deltaY);
        break;
    }
    
    // Apply constraints
    this.applyConstraints();
    
    // Update element position
    this.elementMapper.updateElementPosition(
      this.activeTransform.elementId,
      this.activeTransform.currentPosition
    );
    
    // Trigger callback
    if (this.onTransformUpdate) {
      this.onTransformUpdate(this.activeTransform);
    }
  }

  /**
   * Update move transform
   */
  private updateMoveTransform(deltaX: number, deltaY: number): void {
    if (!this.activeTransform) return;

    // Convert screen delta to world delta
    const worldDelta = this.screenToWorldDelta(deltaX, deltaY);
    
    this.activeTransform.delta.x = worldDelta.x;
    this.activeTransform.delta.y = worldDelta.y;
    
    // Update current position
    this.activeTransform.currentPosition.worldPosition = 
      this.activeTransform.startPosition.worldPosition.clone().add(worldDelta);
    
    // Update UV coordinates based on new world position
    this.updateUVFromWorldPosition();
  }

  /**
   * Update rotate transform
   */
  private updateRotateTransform(deltaX: number, deltaY: number): void {
    if (!this.activeTransform) return;

    // Calculate rotation based on mouse movement
    const rotationSensitivity = 0.5; // degrees per pixel
    const rotation = deltaX * rotationSensitivity;
    
    this.activeTransform.delta.rotation = rotation;
    this.activeTransform.currentPosition.transform.rotation = 
      this.activeTransform.startPosition.transform.rotation + rotation;
  }

  /**
   * Update scale transform (proportional)
   */
  private updateScaleTransform(deltaX: number, deltaY: number): void {
    if (!this.activeTransform) return;

    // Calculate scale based on distance from center
    const scaleSensitivity = 0.01;
    const avgDelta = (deltaX + deltaY) / 2;
    const scaleChange = 1 + (avgDelta * scaleSensitivity);
    
    this.activeTransform.delta.scaleX = scaleChange;
    this.activeTransform.delta.scaleY = scaleChange;
    
    this.activeTransform.currentPosition.transform.scaleX = 
      this.activeTransform.startPosition.transform.scaleX * scaleChange;
    this.activeTransform.currentPosition.transform.scaleY = 
      this.activeTransform.startPosition.transform.scaleY * scaleChange;
  }

  /**
   * Update resize transform (non-proportional)
   */
  private updateResizeTransform(deltaX: number, deltaY: number): void {
    if (!this.activeTransform) return;

    const resizeSensitivity = 0.01;
    const scaleXChange = 1 + (deltaX * resizeSensitivity);
    const scaleYChange = 1 + (deltaY * resizeSensitivity);
    
    this.activeTransform.delta.scaleX = scaleXChange;
    this.activeTransform.delta.scaleY = scaleYChange;
    
    this.activeTransform.currentPosition.transform.scaleX = 
      this.activeTransform.startPosition.transform.scaleX * scaleXChange;
    this.activeTransform.currentPosition.transform.scaleY = 
      this.activeTransform.startPosition.transform.scaleY * scaleYChange;
  }

  /**
   * Convert screen delta to world delta
   */
  private screenToWorldDelta(deltaX: number, deltaY: number): THREE.Vector3 {
    // This is a simplified conversion
    // In practice, you'd need to project screen coordinates to the mesh surface
    const sensitivity = 0.001;
    return new THREE.Vector3(deltaX * sensitivity, -deltaY * sensitivity, 0);
  }

  /**
   * Update UV coordinates from world position
   */
  private updateUVFromWorldPosition(): void {
    if (!this.activeTransform) return;

    // This would involve reverse mapping from world position to UV coordinates
    // For now, we'll keep the UV coordinates unchanged during move operations
    // In a full implementation, you'd calculate the new UV position based on the world position
  }

  /**
   * Apply transform constraints
   */
  private applyConstraints(): void {
    if (!this.activeTransform) return;

    const transform = this.activeTransform.currentPosition.transform;
    
    // Apply scale constraints
    transform.scaleX = Math.max(this.constraints.minScale, 
      Math.min(this.constraints.maxScale, transform.scaleX));
    transform.scaleY = Math.max(this.constraints.minScale, 
      Math.min(this.constraints.maxScale, transform.scaleY));
    
    // Apply rotation snapping
    if (this.constraints.snapAngle > 0) {
      const snapAngleRad = THREE.MathUtils.degToRad(this.constraints.snapAngle);
      transform.rotation = Math.round(transform.rotation / snapAngleRad) * snapAngleRad;
    }
    
    // Apply design area constraints
    if (this.constraints.constrainToDesignArea) {
      this.constrainToDesignArea();
    }
  }

  /**
   * Constrain element to design area bounds
   */
  private constrainToDesignArea(): void {
    if (!this.activeTransform) return;

    // This would implement bounds checking against the design area
    // For now, we'll skip this implementation
  }

  /**
   * End transform operation
   */
  private endTransform(): void {
    if (!this.activeTransform) return;

    const elementId = this.activeTransform.elementId;
    
    // Update interaction state
    this.elementMapper.updateInteractionState(elementId, {
      isTransforming: false,
      transformType: undefined
    });
    
    // Trigger callback
    if (this.onTransformEnd) {
      this.onTransformEnd(this.activeTransform);
    }
    
    console.log(`🔧 Transform ended: ${this.activeTransform.type} on ${elementId}`);
    
    this.activeTransform = null;
    this.isTransforming = false;
  }

  /**
   * Get handle at screen position
   */
  private getHandleAtPosition(screenX: number, screenY: number): {
    elementId: string;
    handleType: string;
  } | null {
    // This would implement raycasting to detect handle clicks
    // For now, we'll return null - this needs to be integrated with the visual selectors
    return this.visualSelectors.getHandleAtPosition(screenX, screenY, this.canvas);
  }

  /**
   * Get transform type from handle type
   */
  private getTransformType(handleType: string): 'move' | 'rotate' | 'scale' | 'resize' {
    if (handleType === 'move') return 'move';
    if (handleType === 'rotate') return 'rotate';
    if (handleType.startsWith('scale-corner')) return 'scale';
    if (handleType.startsWith('scale-edge')) return 'resize';
    return 'move'; // default
  }

  /**
   * Touch event handlers
   */
  private handleTouchStart(event: TouchEvent): void {
    if (event.touches.length === 1) {
      const touch = event.touches[0];
      this.handleMouseDown({
        clientX: touch.clientX,
        clientY: touch.clientY,
        preventDefault: () => event.preventDefault(),
        stopPropagation: () => event.stopPropagation()
      } as any);
    }
  }

  private handleTouchMove(event: TouchEvent): void {
    if (event.touches.length === 1) {
      const touch = event.touches[0];
      this.handleMouseMove({
        clientX: touch.clientX,
        clientY: touch.clientY,
        preventDefault: () => event.preventDefault()
      } as any);
    }
  }

  private handleTouchEnd(event: TouchEvent): void {
    this.handleMouseUp({
      preventDefault: () => event.preventDefault()
    } as any);
  }

  /**
   * Set transform constraints
   */
  setConstraints(constraints: Partial<TransformConstraints>): void {
    Object.assign(this.constraints, constraints);
  }

  /**
   * Set transform callbacks
   */
  setCallbacks(callbacks: {
    onTransformStart?: (operation: TransformOperation) => void;
    onTransformUpdate?: (operation: TransformOperation) => void;
    onTransformEnd?: (operation: TransformOperation) => void;
  }): void {
    Object.assign(this, callbacks);
  }

  /**
   * Cancel current transform
   */
  cancelTransform(): void {
    if (this.activeTransform) {
      const elementId = this.activeTransform.elementId;
      
      // Restore original position
      this.elementMapper.updateElementPosition(
        elementId,
        this.activeTransform.startPosition
      );
      
      // Update interaction state
      this.elementMapper.updateInteractionState(elementId, {
        isTransforming: false,
        transformType: undefined
      });
      
      this.activeTransform = null;
      this.isTransforming = false;
    }
  }

  /**
   * Check if currently transforming
   */
  isCurrentlyTransforming(): boolean {
    return this.isTransforming;
  }

  /**
   * Get current transform operation
   */
  getCurrentTransform(): TransformOperation | null {
    return this.activeTransform;
  }

  /**
   * Dispose of transform handles
   */
  dispose(): void {
    this.canvas.removeEventListener('mousedown', this.handleMouseDown);
    this.canvas.removeEventListener('mousemove', this.handleMouseMove);
    this.canvas.removeEventListener('mouseup', this.handleMouseUp);
    this.canvas.removeEventListener('touchstart', this.handleTouchStart);
    this.canvas.removeEventListener('touchmove', this.handleTouchMove);
    this.canvas.removeEventListener('touchend', this.handleTouchEnd);
    
    this.cancelTransform();
  }
}
