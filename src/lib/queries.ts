import { createSupabaseServerClient } from './supabase-server'
import type { Database } from './database.types'

type Tables = Database['public']['Tables']
type Product = Tables['products']['Row']
type ProductWithImages = Product & {
  product_images: Tables['product_images']['Row'][]
  product_categories: Tables['product_categories']['Row'] | null
  customization_options: Tables['customization_options']['Row'][]
}

// Product queries
export async function getProducts(options?: {
  search?: string
  categoryId?: string
  limit?: number
  offset?: number
}) {
  const supabase = await createSupabaseServerClient()
  
  let query = supabase
    .from('products')
    .select(`
      *,
      product_images(*),
      product_categories(*),
      customization_options(*)
    `)
    .eq('is_active', true)
    .order('featured', { ascending: false })
    .order('sort_order', { ascending: true })

  if (options?.search) {
    query = query.or(`name.ilike.%${options.search}%,description.ilike.%${options.search}%`)
  }

  if (options?.categoryId) {
    query = query.eq('category_id', options.categoryId)
  }

  if (options?.limit) {
    query = query.limit(options.limit)
  }

  if (options?.offset) {
    query = query.range(options.offset, (options.offset + (options.limit || 20)) - 1)
  }

  const { data, error } = await query

  if (error) {
    throw new Error(`Failed to fetch products: ${error.message}`)
  }

  return data as ProductWithImages[]
}

export async function getProductBySlug(slug: string) {
  const supabase = await createSupabaseServerClient()
  
  const { data, error } = await supabase
    .from('products')
    .select(`
      *,
      product_images(*),
      product_categories(*),
      customization_options(*)
    `)
    .eq('slug', slug)
    .eq('is_active', true)
    .single()

  if (error) {
    throw new Error(`Failed to fetch product: ${error.message}`)
  }

  return data as ProductWithImages
}

export async function getProductCategories() {
  const supabase = await createSupabaseServerClient()

  const { data, error } = await supabase
    .from('product_categories')
    .select('*')
    .eq('is_active', true)
    .order('sort_order', { ascending: true })

  if (error) {
    throw new Error(`Failed to fetch categories: ${error.message}`)
  }

  return data
}

// User queries
export async function getUserProfile(userId: string) {
  const supabase = await createSupabaseServerClient()

  const { data, error } = await supabase
    .from('users')
    .select(`
      *,
      user_profiles(*)
    `)
    .eq('id', userId)
    .single()

  if (error) {
    throw new Error(`Failed to fetch user profile: ${error.message}`)
  }

  return data
}

export async function getMerchantByUserId(userId: string) {
  const supabase = await createSupabaseServerClient()
  
  const { data, error } = await supabase
    .from('merchants')
    .select('*')
    .eq('user_id', userId)
    .eq('is_active', true)
    .single()

  if (error) {
    if (error.code === 'PGRST116') {
      return null // No merchant found
    }
    throw new Error(`Failed to fetch merchant: ${error.message}`)
  }

  return data
}

// Merchant product queries
export async function getMerchantProducts(merchantId: string, options?: {
  published?: boolean
  limit?: number
  offset?: number
}) {
  const supabase = await createSupabaseServerClient()
  
  let query = supabase
    .from('merchant_products')
    .select(`
      *,
      products(
        *,
        product_images(*),
        product_categories(*),
        customization_options(*)
      )
    `)
    .eq('merchant_id', merchantId)

  if (options?.published !== undefined) {
    query = query.eq('is_published', options.published)
  }

  if (options?.limit) {
    query = query.limit(options.limit)
  }

  if (options?.offset) {
    query = query.range(options.offset, (options.offset + (options.limit || 20)) - 1)
  }

  const { data, error } = await query

  if (error) {
    throw new Error(`Failed to fetch merchant products: ${error.message}`)
  }

  return data
}

// Order queries
export async function getOrders(options?: {
  merchantId?: string
  customerEmail?: string
  status?: string
  limit?: number
  offset?: number
}) {
  const supabase = await createSupabaseServerClient()
  
  let query = supabase
    .from('orders')
    .select(`
      *,
      order_items(
        *,
        products(*),
        designs(*)
      )
    `)
    .order('created_at', { ascending: false })

  if (options?.merchantId) {
    query = query.eq('merchant_id', options.merchantId)
  }

  if (options?.customerEmail) {
    query = query.eq('customer_email', options.customerEmail)
  }

  if (options?.status) {
    query = query.eq('status', options.status as any)
  }

  if (options?.limit) {
    query = query.limit(options.limit)
  }

  if (options?.offset) {
    query = query.range(options.offset, (options.offset + (options.limit || 20)) - 1)
  }

  const { data, error } = await query

  if (error) {
    throw new Error(`Failed to fetch orders: ${error.message}`)
  }

  return data
}

export async function getOrderById(orderId: string) {
  const supabase = await createSupabaseServerClient()
  
  const { data, error } = await supabase
    .from('orders')
    .select(`
      *,
      order_items(
        *,
        products(*),
        designs(*)
      ),
      merchants(
        shop_name,
        shop_domain
      )
    `)
    .eq('id', orderId)
    .single()

  if (error) {
    throw new Error(`Failed to fetch order: ${error.message}`)
  }

  return data
}

// Design queries
export async function getDesigns(options?: {
  merchantId?: string
  customerEmail?: string
  productId?: string
  isPublic?: boolean
  limit?: number
  offset?: number
}) {
  const supabase = await createSupabaseServerClient()
  
  let query = supabase
    .from('designs')
    .select(`
      *,
      products(*),
      merchants(shop_name)
    `)
    .order('created_at', { ascending: false })

  if (options?.merchantId) {
    query = query.eq('merchant_id', options.merchantId)
  }

  if (options?.customerEmail) {
    query = query.eq('customer_email', options.customerEmail)
  }

  if (options?.productId) {
    query = query.eq('product_id', options.productId)
  }

  if (options?.isPublic !== undefined) {
    query = query.eq('is_public', options.isPublic)
  }

  if (options?.limit) {
    query = query.limit(options.limit)
  }

  if (options?.offset) {
    query = query.range(options.offset, (options.offset + (options.limit || 20)) - 1)
  }

  const { data, error } = await query

  if (error) {
    throw new Error(`Failed to fetch designs: ${error.message}`)
  }

  return data
}

// Ticket queries
export async function getTickets(options?: {
  userId?: string
  merchantId?: string
  status?: string
  priority?: string
  limit?: number
  offset?: number
}) {
  const supabase = await createSupabaseServerClient()
  
  let query = supabase
    .from('tickets')
    .select(`
      *,
      users!tickets_user_id_fkey(email),
      merchants(shop_name),
      ticket_messages(
        *,
        users(email)
      )
    `)
    .order('created_at', { ascending: false })

  if (options?.userId) {
    query = query.eq('user_id', options.userId)
  }

  if (options?.merchantId) {
    query = query.eq('merchant_id', options.merchantId)
  }

  if (options?.status) {
    query = query.eq('status', options.status as any)
  }

  if (options?.priority) {
    query = query.eq('priority', options.priority as any)
  }

  if (options?.limit) {
    query = query.limit(options.limit)
  }

  if (options?.offset) {
    query = query.range(options.offset, (options.offset + (options.limit || 20)) - 1)
  }

  const { data, error } = await query

  if (error) {
    throw new Error(`Failed to fetch tickets: ${error.message}`)
  }

  return data
}

// Statistics queries
export async function getAdminStats() {
  const supabase = await createSupabaseServerClient()

  const { data, error } = await supabase.rpc('get_admin_stats')

  if (error) {
    throw new Error(`Failed to fetch admin stats: ${error.message}`)
  }

  return data
}

export async function getMerchantStats(merchantId: string) {
  const supabase = await createSupabaseServerClient()
  
  const { data, error } = await supabase.rpc('get_merchant_stats', {
    merchant_id: merchantId
  })

  if (error) {
    throw new Error(`Failed to fetch merchant stats: ${error.message}`)
  }

  return data
}
