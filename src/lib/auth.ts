import { createSupabaseServerClient } from './supabase-server'
import { redirect } from 'next/navigation'
import type { Database } from './database.types'

type User = Database['public']['Tables']['users']['Row']
type UserProfile = Database['public']['Tables']['user_profiles']['Row']
type Merchant = Database['public']['Tables']['merchants']['Row']

export interface AuthUser extends User {
  user_profiles?: UserProfile
  merchants?: Merchant
}

// Get current authenticated user
export async function getUser(): Promise<AuthUser | null> {
  const supabase = await createSupabaseServerClient()

  const { data: { user: authUser }, error: authError } = await supabase.auth.getUser()

  if (authError || !authUser) {
    return null
  }

  const { data: user, error } = await supabase
    .from('users')
    .select(`
      *,
      user_profiles(*),
      merchants(*)
    `)
    .eq('id', authUser.id)
    .single()

  if (error) {
    console.error('Error fetching user:', error)
    return null
  }

  // Transform the data to match AuthUser type
  const authUserData: AuthUser = {
    ...user,
    user_profiles: user.user_profiles || undefined,
    merchants: user.merchants?.[0] || undefined
  }

  return authUserData
}

// Check if user has specific role
export async function hasRole(role: Database['public']['Enums']['user_role']): Promise<boolean> {
  const user = await getUser()
  return user?.role === role
}

// Check if user is super admin
export async function isSuperAdmin(): Promise<boolean> {
  return await hasRole('super_admin')
}

// Check if user is merchant
export async function isMerchant(): Promise<boolean> {
  return await hasRole('merchant')
}

// Check if user is customer
export async function isCustomer(): Promise<boolean> {
  return await hasRole('customer')
}

// Get merchant data for current user
export async function getCurrentMerchant(): Promise<Merchant | null> {
  const user = await getUser()
  
  if (!user || user.role !== 'merchant') {
    return null
  }

  // If user has merchants array from the join, return the first one
  if (user.merchants && Array.isArray(user.merchants) && user.merchants.length > 0) {
    return user.merchants[0]
  }

  // Otherwise fetch merchant data separately
  const supabase = await createSupabaseServerClient()
  const { data: merchant, error } = await supabase
    .from('merchants')
    .select('*')
    .eq('user_id', user.id)
    .eq('is_active', true)
    .single()

  if (error) {
    console.error('Error fetching merchant:', error)
    return null
  }

  return merchant
}

// Require authentication - redirect to login if not authenticated
export async function requireAuth(redirectTo?: string): Promise<AuthUser> {
  const user = await getUser()
  
  if (!user) {
    const redirectUrl = redirectTo ? `?redirect=${encodeURIComponent(redirectTo)}` : ''
    redirect(`/auth/signin${redirectUrl}`)
  }
  
  return user
}

// Require specific role - redirect if user doesn't have required role
export async function requireRole(
  role: Database['public']['Enums']['user_role'],
  redirectTo?: string
): Promise<AuthUser> {
  const user = await requireAuth(redirectTo)
  
  if (user.role !== role) {
    // Redirect based on user's actual role
    switch (user.role) {
      case 'super_admin':
        redirect('/admin')
      case 'merchant':
        redirect('/dashboard')
      case 'customer':
        redirect('/')
      default:
        redirect('/auth/signin')
    }
  }
  
  return user
}

// Require super admin access
export async function requireSuperAdmin(redirectTo?: string): Promise<AuthUser> {
  return await requireRole('super_admin', redirectTo)
}

// Require merchant access
export async function requireMerchant(redirectTo?: string): Promise<AuthUser> {
  return await requireRole('merchant', redirectTo)
}

// Sign out user
export async function signOut() {
  const supabase = await createSupabaseServerClient()
  await supabase.auth.signOut()
  redirect('/auth/signin')
}

// Create user profile after registration
export async function createUserProfile(
  userId: string,
  email: string,
  role: Database['public']['Enums']['user_role'] = 'customer',
  profileData?: Partial<UserProfile>
) {
  const supabase = await createSupabaseServerClient()
  
  // Create user record
  const { error: userError } = await supabase
    .from('users')
    .insert({
      id: userId,
      email,
      role
    })

  if (userError) {
    throw new Error(`Failed to create user: ${userError.message}`)
  }

  // Create user profile
  const { error: profileError } = await supabase
    .from('user_profiles')
    .insert({
      user_id: userId,
      ...profileData
    })

  if (profileError) {
    throw new Error(`Failed to create user profile: ${profileError.message}`)
  }
}

// Update user profile
export async function updateUserProfile(
  userId: string,
  profileData: Partial<UserProfile>
) {
  const supabase = await createSupabaseServerClient()
  
  const { error } = await supabase
    .from('user_profiles')
    .update(profileData)
    .eq('user_id', userId)

  if (error) {
    throw new Error(`Failed to update user profile: ${error.message}`)
  }
}

// Create merchant account
export async function createMerchantAccount(
  userId: string,
  merchantData: {
    shop_domain: string
    shop_name: string
    access_token: string
    subscription_tier?: Database['public']['Enums']['subscription_tier']
    settings?: any
  }
) {
  const supabase = await createSupabaseServerClient()
  
  // Update user role to merchant
  const { error: userError } = await supabase
    .from('users')
    .update({ role: 'merchant' })
    .eq('id', userId)

  if (userError) {
    throw new Error(`Failed to update user role: ${userError.message}`)
  }

  // Create merchant record
  const { error: merchantError } = await supabase
    .from('merchants')
    .insert({
      user_id: userId,
      shop_domain: merchantData.shop_domain,
      shop_name: merchantData.shop_name,
      access_token: merchantData.access_token,
      subscription_tier: merchantData.subscription_tier || 'free',
      subscription_status: 'trialing',
      settings: merchantData.settings || {
        customizer_enabled: true,
        auto_fulfill: false
      }
    })

  if (merchantError) {
    throw new Error(`Failed to create merchant account: ${merchantError.message}`)
  }
}

// Validate Shopify webhook
export function validateShopifyWebhook(
  body: string,
  signature: string,
  secret: string
): boolean {
  const crypto = require('crypto')
  const hmac = crypto.createHmac('sha256', secret)
  hmac.update(body, 'utf8')
  const hash = hmac.digest('base64')
  
  return hash === signature
}

// Generate secure random string for tokens
export function generateSecureToken(length: number = 32): string {
  const crypto = require('crypto')
  return crypto.randomBytes(length).toString('hex')
}

// Log user activity
export async function logActivity(
  action: string,
  resourceType: string,
  resourceId?: string,
  details?: any,
  userId?: string,
  merchantId?: string
) {
  try {
    const supabase = await createSupabaseServerClient()

    // Try to get user context if not provided
    let finalUserId = userId
    let finalMerchantId = merchantId

    if (!finalUserId) {
      const user = await getUser()
      finalUserId = user?.id
    }

    if (!finalMerchantId && finalUserId) {
      const merchant = await getCurrentMerchant()
      finalMerchantId = merchant?.id
    }

    // Log the activity even if no user context (for system events)
    console.log('Calling log_activity with params:', {
      p_action: action,
      p_resource_type: resourceType,
      p_user_id: finalUserId || '',
      p_merchant_id: finalMerchantId || '',
      p_resource_id: resourceId || '',
      p_details: details || {}
    })

    const { data, error } = await supabase.rpc('log_activity', {
      p_action: action,
      p_resource_type: resourceType,
      p_user_id: finalUserId || '',
      p_merchant_id: finalMerchantId || '',
      p_resource_id: resourceId || '',
      p_details: details || {}
    })

    if (error) {
      console.error('Failed to log activity:', error)
      console.error('Error details:', JSON.stringify(error, null, 2))
    } else {
      console.log('Activity logged successfully:', { action, resourceType, resourceId, logId: data })
    }
  } catch (error) {
    console.error('Error in logActivity:', error)
  }
}
