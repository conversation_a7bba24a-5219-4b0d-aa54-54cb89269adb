/**
 * 3D Performance Optimizer
 * Optimizes performance for smooth real-time 3D interactions
 */

import * as THREE from 'three';

export interface PerformanceConfig {
  maxFPS: number;
  enableFrustumCulling: boolean;
  enableLOD: boolean;
  enableInstancedRendering: boolean;
  textureCompressionLevel: number;
  shadowMapSize: number;
  antialiasingLevel: number;
  enableOcclusion: boolean;
}

export interface PerformanceMetrics {
  fps: number;
  frameTime: number;
  drawCalls: number;
  triangles: number;
  memoryUsage: number;
  lastUpdate: number;
}

export class ThreeDPerformanceOptimizer {
  private renderer: THREE.WebGLRenderer;
  private scene: THREE.Scene;
  private camera: THREE.Camera;
  
  // Performance tracking
  private metrics: PerformanceMetrics = {
    fps: 0,
    frameTime: 0,
    drawCalls: 0,
    triangles: 0,
    memoryUsage: 0,
    lastUpdate: 0
  };
  
  private frameCount = 0;
  private lastTime = 0;
  private fpsUpdateInterval = 1000; // Update FPS every second
  
  // Configuration
  private config: PerformanceConfig = {
    maxFPS: 60,
    enableFrustumCulling: true,
    enableLOD: false,
    enableInstancedRendering: false,
    textureCompressionLevel: 1,
    shadowMapSize: 1024,
    antialiasingLevel: 1,
    enableOcclusion: false
  };
  
  // Optimization state
  private isOptimized = false;
  private lodObjects: Map<THREE.Object3D, THREE.LOD> = new Map();
  private textureCache: Map<string, THREE.Texture> = new Map();
  private geometryCache: Map<string, THREE.BufferGeometry> = new Map();

  constructor(renderer: THREE.WebGLRenderer, scene: THREE.Scene, camera: THREE.Camera) {
    this.renderer = renderer;
    this.scene = scene;
    this.camera = camera;
    
    this.initializeOptimizations();
  }

  /**
   * Initialize performance optimizations
   */
  private initializeOptimizations(): void {
    console.log('⚡ Initializing 3D performance optimizations...');
    
    // Optimize renderer settings
    this.optimizeRenderer();
    
    // Enable frustum culling
    if (this.config.enableFrustumCulling) {
      this.enableFrustumCulling();
    }
    
    // Setup performance monitoring
    this.setupPerformanceMonitoring();
    
    this.isOptimized = true;
    console.log('⚡ 3D performance optimizations initialized');
  }

  /**
   * Optimize renderer settings
   */
  private optimizeRenderer(): void {
    const gl = this.renderer.getContext();
    
    // Enable hardware acceleration features
    this.renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2)); // Limit pixel ratio
    this.renderer.outputColorSpace = THREE.SRGBColorSpace;
    
    // Optimize shadow settings
    this.renderer.shadowMap.enabled = false; // Disable shadows for better performance
    this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
    
    // Optimize rendering settings
    this.renderer.sortObjects = true;
    this.renderer.autoClear = true;
    this.renderer.autoClearColor = true;
    this.renderer.autoClearDepth = true;
    this.renderer.autoClearStencil = true;
    
    // Enable extensions for better performance
    if (gl.getExtension('OES_vertex_array_object')) {
      console.log('⚡ VAO extension enabled');
    }
    
    if (gl.getExtension('WEBGL_compressed_texture_s3tc')) {
      console.log('⚡ Texture compression enabled');
    }
  }

  /**
   * Enable frustum culling optimization
   */
  private enableFrustumCulling(): void {
    this.scene.traverse((object) => {
      if (object instanceof THREE.Mesh) {
        object.frustumCulled = true;
      }
    });
  }

  /**
   * Setup performance monitoring
   */
  private setupPerformanceMonitoring(): void {
    this.lastTime = performance.now();
    
    // Start monitoring loop
    this.monitorPerformance();
  }

  /**
   * Monitor performance metrics
   */
  private monitorPerformance(): void {
    const currentTime = performance.now();
    const deltaTime = currentTime - this.lastTime;
    
    this.frameCount++;
    
    // Update FPS every second
    if (deltaTime >= this.fpsUpdateInterval) {
      this.metrics.fps = Math.round((this.frameCount * 1000) / deltaTime);
      this.metrics.frameTime = deltaTime / this.frameCount;
      this.frameCount = 0;
      this.lastTime = currentTime;
      
      // Update other metrics
      this.updateRenderMetrics();
      this.updateMemoryMetrics();
      
      this.metrics.lastUpdate = currentTime;
    }
    
    // Continue monitoring
    requestAnimationFrame(() => this.monitorPerformance());
  }

  /**
   * Update rendering metrics
   */
  private updateRenderMetrics(): void {
    const info = this.renderer.info;
    this.metrics.drawCalls = info.render.calls;
    this.metrics.triangles = info.render.triangles;
  }

  /**
   * Update memory metrics
   */
  private updateMemoryMetrics(): void {
    const info = this.renderer.info;
    this.metrics.memoryUsage = info.memory.geometries + info.memory.textures;
  }

  /**
   * Optimize textures for better performance
   */
  optimizeTextures(textures: THREE.Texture[]): void {
    textures.forEach(texture => {
      // Reduce texture size if needed
      if (texture.image && texture.image.width > 1024) {
        this.resizeTexture(texture, 1024);
      }
      
      // Enable mipmaps for better performance
      texture.generateMipmaps = true;
      texture.minFilter = THREE.LinearMipmapLinearFilter;
      texture.magFilter = THREE.LinearFilter;
      
      // Use appropriate texture format
      texture.format = THREE.RGBAFormat;
      texture.type = THREE.UnsignedByteType;
      
      // Enable texture compression if available
      if (this.renderer.capabilities.isWebGL2) {
        texture.internalFormat = 'RGBA8';
      }
    });
  }

  /**
   * Resize texture to target size
   */
  private resizeTexture(texture: THREE.Texture, targetSize: number): void {
    if (!texture.image) return;
    
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    if (!ctx) return;
    
    canvas.width = targetSize;
    canvas.height = targetSize;
    
    ctx.drawImage(texture.image, 0, 0, targetSize, targetSize);
    texture.image = canvas;
    texture.needsUpdate = true;
  }

  /**
   * Optimize geometries for better performance
   */
  optimizeGeometries(geometries: THREE.BufferGeometry[]): void {
    geometries.forEach(geometry => {
      // Merge vertices optimization removed - not available in current Three.js version
      
      // Compute vertex normals if missing
      if (!geometry.attributes.normal) {
        geometry.computeVertexNormals();
      }
      
      // Compute bounding box and sphere for frustum culling
      geometry.computeBoundingBox();
      geometry.computeBoundingSphere();
      
      // Dispose of unnecessary attributes
      if (geometry.attributes.uv2) {
        geometry.deleteAttribute('uv2');
      }
    });
  }

  /**
   * Create Level of Detail (LOD) objects
   */
  createLOD(object: THREE.Object3D, distances: number[]): THREE.LOD {
    const lod = new THREE.LOD();
    
    // Add original object at closest distance
    lod.addLevel(object, distances[0] || 0);
    
    // Create simplified versions for further distances
    distances.slice(1).forEach((distance, index) => {
      const simplifiedObject = this.createSimplifiedObject(object, (index + 1) * 0.5);
      lod.addLevel(simplifiedObject, distance);
    });
    
    this.lodObjects.set(object, lod);
    return lod;
  }

  /**
   * Create simplified version of object
   */
  private createSimplifiedObject(object: THREE.Object3D, simplificationFactor: number): THREE.Object3D {
    const simplified = object.clone();
    
    // Simplify geometry if it's a mesh
    if (simplified instanceof THREE.Mesh && simplified.geometry instanceof THREE.BufferGeometry) {
      const geometry = simplified.geometry;
      
      // Reduce vertex count (simplified implementation)
      const positions = geometry.attributes.position;
      if (positions && positions.count > 100) {
        const reducedCount = Math.floor(positions.count * simplificationFactor);
        const newPositions = new Float32Array(reducedCount * 3);
        
        for (let i = 0; i < reducedCount; i++) {
          const sourceIndex = Math.floor((i / reducedCount) * positions.count);
          newPositions[i * 3] = positions.getX(sourceIndex);
          newPositions[i * 3 + 1] = positions.getY(sourceIndex);
          newPositions[i * 3 + 2] = positions.getZ(sourceIndex);
        }
        
        geometry.setAttribute('position', new THREE.BufferAttribute(newPositions, 3));
      }
    }
    
    return simplified;
  }

  /**
   * Enable instanced rendering for repeated objects
   */
  enableInstancedRendering(objects: THREE.Mesh[]): THREE.InstancedMesh | null {
    if (objects.length < 2) return null;
    
    const baseObject = objects[0];
    const geometry = baseObject.geometry;
    const material = baseObject.material;
    
    // Create instanced mesh
    const instancedMesh = new THREE.InstancedMesh(geometry, material, objects.length);
    
    // Set instance matrices
    objects.forEach((object, index) => {
      instancedMesh.setMatrixAt(index, object.matrix);
    });
    
    instancedMesh.instanceMatrix.needsUpdate = true;
    
    return instancedMesh;
  }

  /**
   * Throttle function calls for performance
   */
  throttle<T extends (...args: any[]) => any>(
    func: T,
    limit: number
  ): (...args: Parameters<T>) => void {
    let inThrottle: boolean;
    return function(this: any, ...args: Parameters<T>) {
      if (!inThrottle) {
        func.apply(this, args);
        inThrottle = true;
        setTimeout(() => inThrottle = false, limit);
      }
    };
  }

  /**
   * Debounce function calls for performance
   */
  debounce<T extends (...args: any[]) => any>(
    func: T,
    delay: number
  ): (...args: Parameters<T>) => void {
    let timeoutId: NodeJS.Timeout;
    return function(this: any, ...args: Parameters<T>) {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => func.apply(this, args), delay);
    };
  }

  /**
   * Update configuration
   */
  updateConfig(config: Partial<PerformanceConfig>): void {
    Object.assign(this.config, config);
    
    // Re-apply optimizations if needed
    if (this.isOptimized) {
      this.optimizeRenderer();
    }
  }

  /**
   * Get current performance metrics
   */
  getMetrics(): PerformanceMetrics {
    return { ...this.metrics };
  }

  /**
   * Get performance recommendations
   */
  getRecommendations(): string[] {
    const recommendations: string[] = [];
    
    if (this.metrics.fps < 30) {
      recommendations.push('Consider reducing texture quality or mesh complexity');
    }
    
    if (this.metrics.drawCalls > 100) {
      recommendations.push('Consider using instanced rendering for repeated objects');
    }
    
    if (this.metrics.triangles > 100000) {
      recommendations.push('Consider using Level of Detail (LOD) for complex models');
    }
    
    if (this.metrics.memoryUsage > 100) {
      recommendations.push('Consider optimizing textures and geometries');
    }
    
    return recommendations;
  }

  /**
   * Dispose of optimizer resources
   */
  dispose(): void {
    this.textureCache.clear();
    this.geometryCache.clear();
    this.lodObjects.clear();
    
    console.log('⚡ Performance optimizer disposed');
  }
}
