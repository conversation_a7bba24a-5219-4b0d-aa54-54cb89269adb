/**
 * UV Coordinate Extractor
 * Extracts UV mapping data from GLB design areas for texture application
 */

import * as THREE from 'three';

export interface UVMapping {
  name: string;
  uvCoordinates: Float32Array;
  vertices: Float32Array;
  indices: Uint16Array | Uint32Array;
  boundingBox: {
    min: [number, number];
    max: [number, number];
  };
  textureSize: {
    width: number;
    height: number;
  };
  center: [number, number];
}

export interface DesignAreaUV {
  areaName: string;
  mapping: UVMapping;
  mesh: THREE.Mesh;
  material: THREE.Material;
  isDesignArea: boolean;
}

export class UVExtractor {
  /**
   * Extract UV mappings from all meshes in a GLTF scene
   */
  static extractFromScene(scene: THREE.Scene): DesignAreaUV[] {
    const designAreas: DesignAreaUV[] = [];

    scene.traverse((object) => {
      if (object instanceof THREE.Mesh) {
        const mesh = object as THREE.Mesh;
        const geometry = mesh.geometry;

        // Check if this is a design area
        const isDesignArea = this.isDesignArea(mesh.name);

        if (geometry.attributes.uv && isDesignArea) {
          const uvMapping = this.extractUVMapping(mesh);
          if (uvMapping) {
            designAreas.push({
              areaName: mesh.name,
              mapping: uvMapping,
              mesh,
              material: Array.isArray(mesh.material) ? mesh.material[0] : mesh.material,
              isDesignArea
            });
          }
        }
      }
    });

    console.log(`🎨 Extracted UV mappings for ${designAreas.length} design areas:`,
      designAreas.map(area => area.areaName));

    return designAreas;
  }

  /**
   * Extract UV mapping data from a specific mesh
   */
  static extractUVMapping(mesh: THREE.Mesh): UVMapping | null {
    const geometry = mesh.geometry;
    const uvAttribute = geometry.attributes.uv;
    const positionAttribute = geometry.attributes.position;

    if (!uvAttribute || !positionAttribute) {
      console.warn(`⚠️ Mesh ${mesh.name} missing UV or position attributes`);
      return null;
    }

    const uvCoordinates = uvAttribute.array as Float32Array;
    const vertices = positionAttribute.array as Float32Array;
    const indices = geometry.index?.array as Uint16Array | Uint32Array;

    // Calculate UV bounding box
    const uvBounds = this.calculateUVBounds(uvCoordinates);

    // Determine optimal texture size based on UV density
    const textureSize = this.calculateOptimalTextureSize(uvCoordinates, vertices);

    return {
      name: mesh.name,
      uvCoordinates,
      vertices,
      indices: indices || new Uint16Array(0),
      boundingBox: uvBounds,
      textureSize,
      center: [
        (uvBounds.min[0] + uvBounds.max[0]) / 2,
        (uvBounds.min[1] + uvBounds.max[1]) / 2
      ]
    };
  }

  /**
   * Calculate UV coordinate bounding box
   */
  static calculateUVBounds(uvCoordinates: Float32Array): { min: [number, number]; max: [number, number] } {
    let minU = Infinity, minV = Infinity;
    let maxU = -Infinity, maxV = -Infinity;

    for (let i = 0; i < uvCoordinates.length; i += 2) {
      const u = uvCoordinates[i];
      const v = uvCoordinates[i + 1];

      minU = Math.min(minU, u);
      maxU = Math.max(maxU, u);
      minV = Math.min(minV, v);
      maxV = Math.max(maxV, v);
    }

    return {
      min: [minU, minV],
      max: [maxU, maxV]
    };
  }

  /**
   * Calculate optimal texture size based on UV density and mesh complexity
   */
  static calculateOptimalTextureSize(uvCoordinates: Float32Array, vertices: Float32Array): { width: number; height: number } {
    const vertexCount = vertices.length / 3;
    const uvBounds = this.calculateUVBounds(uvCoordinates);

    // Calculate UV area
    const uvWidth = uvBounds.max[0] - uvBounds.min[0];
    const uvHeight = uvBounds.max[1] - uvBounds.min[1];
    const uvArea = uvWidth * uvHeight;

    // Base texture size on vertex density and UV area
    let baseSize = 512; // Default size

    if (vertexCount > 1000) baseSize = 1024;
    if (vertexCount > 2000) baseSize = 2048;
    if (uvArea > 0.5) baseSize *= 1.5; // Larger UV area needs higher resolution

    // Ensure power of 2 and reasonable limits
    const width = Math.min(2048, Math.max(256, Math.pow(2, Math.ceil(Math.log2(baseSize)))));
    const height = width; // Square textures for simplicity

    return { width, height };
  }

  /**
   * Check if a mesh name indicates a design area
   */
  static isDesignArea(name: string): boolean {
    const designAreaPatterns = [
      'front', 'back', 'left_sleeve', 'right_sleeve', 'sleeve',
      'collar', 'chest', 'pocket', 'side', 'panel'
    ];

    const lowerName = name.toLowerCase();
    return designAreaPatterns.some(pattern => lowerName.includes(pattern));
  }

  /**
   * Convert UV coordinates to canvas pixel coordinates
   */
  static uvToCanvasCoordinates(
    u: number,
    v: number,
    canvasWidth: number,
    canvasHeight: number,
    uvBounds?: { min: [number, number]; max: [number, number] }
  ): [number, number] {
    // If UV bounds provided, normalize to that range
    let normalizedU = u;
    let normalizedV = v;

    if (uvBounds) {
      normalizedU = (u - uvBounds.min[0]) / (uvBounds.max[0] - uvBounds.min[0]);
      normalizedV = (v - uvBounds.min[1]) / (uvBounds.max[1] - uvBounds.min[1]);
    }

    // Convert to canvas coordinates (flip V for canvas coordinate system)
    const x = normalizedU * canvasWidth;
    const y = (1 - normalizedV) * canvasHeight;

    return [x, y];
  }

  /**
   * Convert canvas coordinates back to UV coordinates
   */
  static canvasToUVCoordinates(
    x: number,
    y: number,
    canvasWidth: number,
    canvasHeight: number,
    uvBounds?: { min: [number, number]; max: [number, number] }
  ): [number, number] {
    // Normalize canvas coordinates
    let u = x / canvasWidth;
    let v = 1 - (y / canvasHeight); // Flip V back

    // If UV bounds provided, scale to that range
    if (uvBounds) {
      u = uvBounds.min[0] + u * (uvBounds.max[0] - uvBounds.min[0]);
      v = uvBounds.min[1] + v * (uvBounds.max[1] - uvBounds.min[1]);
    }

    return [u, v];
  }
}

export default UVExtractor;