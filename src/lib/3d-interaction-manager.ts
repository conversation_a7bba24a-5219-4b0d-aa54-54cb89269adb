/**
 * 3D Interaction Manager
 * Main coordinator for all 3D design element interactions
 */

import * as THREE from 'three';
import { fabric } from 'fabric';
import { ThreeDElementMapper, Element3DPosition, InteractionState } from './3d-element-mapping';
import { ThreeDRaycaster, RaycastHit } from './3d-raycasting';
import { FabricThreeJSBridge, DesignElement } from './fabric-threejs-bridge';
import { ThreeDVisualSelectors } from './3d-visual-selectors';
import { ThreeDTransformHandles, TransformOperation } from './3d-transform-handles';
import { ThreeDHoverEffects } from './3d-hover-effects';
import { DesignAreaUV } from './uv-extractor';

export interface InteractionManagerConfig {
  enableHoverEffects: boolean;
  enableTransformHandles: boolean;
  enableVisualSelectors: boolean;
  enableSurfaceDragging: boolean;
  debugMode: boolean;
}

export interface InteractionCallbacks {
  onElementSelect?: (elementId: string | null) => void;
  onElementUpdate?: (elementId: string, element: DesignElement) => void;
  onCanvasUpdate?: (canvas: HTMLCanvasElement) => void;
  onDesignAreaSelect?: (areaName: string) => void;
}

export class ThreeDInteractionManager {
  // Core systems
  private elementMapper!: ThreeDElementMapper;
  private raycaster!: ThreeDRaycaster;
  private bridge!: FabricThreeJSBridge;
  private visualSelectors!: ThreeDVisualSelectors;
  private transformHandles!: ThreeDTransformHandles;
  private hoverEffects!: ThreeDHoverEffects;

  // Three.js components
  private scene!: THREE.Scene;
  private camera!: THREE.Camera;
  private canvas!: HTMLElement;

  // Fabric.js canvas
  private fabricCanvas!: fabric.Canvas;
  
  // State
  private selectedElementId: string | null = null;
  private hoveredElementId: string | null = null;
  private currentDesignArea: DesignAreaUV | null = null;
  private isInitialized = false;
  
  // Configuration
  private config: InteractionManagerConfig = {
    enableHoverEffects: true,
    enableTransformHandles: true,
    enableVisualSelectors: true,
    enableSurfaceDragging: true,
    debugMode: false
  };
  
  // Callbacks
  private callbacks: InteractionCallbacks = {};

  constructor(
    scene: THREE.Scene,
    camera: THREE.Camera,
    canvas: HTMLElement,
    fabricCanvasElement: HTMLCanvasElement
  ) {
    this.scene = scene;
    this.camera = camera;
    this.canvas = canvas;
    
    // Create Fabric.js canvas
    this.fabricCanvas = new fabric.Canvas(fabricCanvasElement, {
      width: 512,
      height: 512,
      backgroundColor: 'transparent'
    });
    
    // Initialize systems
    this.initializeSystems();
  }

  /**
   * Initialize all interaction systems
   */
  private initializeSystems(): void {
    console.log('🎮 Initializing 3D Interaction Manager...');
    
    // Create core systems
    this.elementMapper = new ThreeDElementMapper();
    this.raycaster = new ThreeDRaycaster(this.camera, this.scene, this.canvas, this.elementMapper);
    this.bridge = new FabricThreeJSBridge(this.fabricCanvas, this.elementMapper, this.raycaster);
    
    // Create visual systems
    this.visualSelectors = new ThreeDVisualSelectors(this.scene, this.camera);
    this.transformHandles = new ThreeDTransformHandles(
      this.elementMapper, 
      this.visualSelectors, 
      this.camera, 
      this.canvas
    );
    this.hoverEffects = new ThreeDHoverEffects(this.scene);
    
    // Setup callbacks
    this.setupCallbacks();
    
    this.isInitialized = true;
    console.log('🎮 3D Interaction Manager initialized successfully');
  }

  /**
   * Setup callbacks between systems
   */
  private setupCallbacks(): void {
    // Raycaster callbacks
    this.raycaster.setCallbacks({
      onElementClick: this.handleElementClick.bind(this),
      onElementHover: this.handleElementHover.bind(this),
      onElementDragStart: this.handleElementDragStart.bind(this),
      onElementDrag: this.handleElementDrag.bind(this),
      onElementDragEnd: this.handleElementDragEnd.bind(this),
      onMeshClick: this.handleMeshClick.bind(this)
    });
    
    // Bridge callbacks
    this.bridge.setSyncCallbacks({
      onElementUpdate: this.handleElementUpdate.bind(this),
      onElementSelect: this.handleElementSelect.bind(this),
      onCanvasUpdate: this.handleCanvasUpdate.bind(this)
    });
    
    // Transform handle callbacks
    this.transformHandles.setCallbacks({
      onTransformStart: this.handleTransformStart.bind(this),
      onTransformUpdate: this.handleTransformUpdate.bind(this),
      onTransformEnd: this.handleTransformEnd.bind(this)
    });
  }

  /**
   * Register a design area
   */
  registerDesignArea(designArea: DesignAreaUV): void {
    if (!this.isInitialized) {
      console.warn('🎮 Interaction Manager not initialized');
      return;
    }
    
    this.bridge.registerDesignArea(designArea);
    console.log(`🎮 Registered design area: ${designArea.areaName}`);
  }

  /**
   * Set the current active design area
   */
  setCurrentDesignArea(designArea: DesignAreaUV): void {
    this.currentDesignArea = designArea;
    this.bridge.setCurrentDesignArea(designArea.areaName);
    
    if (this.callbacks.onDesignAreaSelect) {
      this.callbacks.onDesignAreaSelect(designArea.areaName);
    }
    
    console.log(`🎮 Set current design area: ${designArea.areaName}`);
  }

  /**
   * Add a design element
   */
  addElement(element: DesignElement): void {
    if (!this.isInitialized || !this.currentDesignArea) {
      console.warn('🎮 Cannot add element: Manager not initialized or no design area selected');
      return;
    }
    
    this.bridge.addElement(element);
    this.updateElementVisuals(element.id);
    
    console.log(`🎮 Added element: ${element.id}`);
  }

  /**
   * Update a design element
   */
  updateElement(elementId: string, updates: Partial<DesignElement>): void {
    this.bridge.updateElement(elementId, updates);
    this.updateElementVisuals(elementId);
  }

  /**
   * Remove a design element
   */
  removeElement(elementId: string): void {
    this.bridge.removeElement(elementId);
    this.removeElementVisuals(elementId);
    
    if (this.selectedElementId === elementId) {
      this.selectedElementId = null;
    }
    if (this.hoveredElementId === elementId) {
      this.hoveredElementId = null;
    }
  }

  /**
   * Select an element
   */
  selectElement(elementId: string | null): void {
    // Clear previous selection
    if (this.selectedElementId) {
      this.elementMapper.updateInteractionState(this.selectedElementId, {
        isSelected: false
      });
      this.updateElementVisuals(this.selectedElementId);
    }
    
    // Set new selection
    this.selectedElementId = elementId;
    
    if (elementId) {
      this.elementMapper.updateInteractionState(elementId, {
        isSelected: true
      });
      this.updateElementVisuals(elementId);
    }
    
    if (this.callbacks.onElementSelect) {
      this.callbacks.onElementSelect(elementId);
    }
  }

  /**
   * Update visual effects for an element
   */
  private updateElementVisuals(elementId: string): void {
    const position = this.elementMapper.getElementPosition(elementId);
    const interactionState = this.elementMapper.getInteractionState(elementId);
    
    if (!position || !interactionState) return;
    
    // Update visual selectors
    if (this.config.enableVisualSelectors) {
      this.visualSelectors.updateSelector(elementId, position, interactionState);
    }
    
    // Update hover effects
    if (this.config.enableHoverEffects) {
      this.hoverEffects.updateHoverEffect(elementId, position, interactionState);
    }
  }

  /**
   * Remove visual effects for an element
   */
  private removeElementVisuals(elementId: string): void {
    this.visualSelectors.removeSelector(elementId);
    this.hoverEffects.removeHoverEffect(elementId);
  }

  /**
   * Handle element click
   */
  private handleElementClick(elementId: string, hit: RaycastHit): void {
    this.selectElement(elementId);
    console.log(`🎮 Element clicked: ${elementId}`);
  }

  /**
   * Handle element hover
   */
  private handleElementHover(elementId: string | null, hit: RaycastHit | null): void {
    // Clear previous hover
    if (this.hoveredElementId && this.hoveredElementId !== elementId) {
      this.elementMapper.updateInteractionState(this.hoveredElementId, {
        isHovered: false
      });
      this.updateElementVisuals(this.hoveredElementId);
    }
    
    // Set new hover
    this.hoveredElementId = elementId;
    
    if (elementId) {
      this.elementMapper.updateInteractionState(elementId, {
        isHovered: true
      });
      this.updateElementVisuals(elementId);
    }
  }

  /**
   * Handle element drag start
   */
  private handleElementDragStart(elementId: string, hit: RaycastHit): void {
    this.elementMapper.updateInteractionState(elementId, {
      isDragging: true
    });
    console.log(`🎮 Element drag start: ${elementId}`);
  }

  /**
   * Handle element drag
   */
  private handleElementDrag(elementId: string, deltaX: number, deltaY: number): void {
    // This will be handled by the bridge's 3D drag implementation
    console.log(`🎮 Element drag: ${elementId}, delta: ${deltaX}, ${deltaY}`);
  }

  /**
   * Handle element drag end
   */
  private handleElementDragEnd(elementId: string): void {
    this.elementMapper.updateInteractionState(elementId, {
      isDragging: false
    });
    console.log(`🎮 Element drag end: ${elementId}`);
  }

  /**
   * Handle mesh click (for adding new elements)
   */
  private handleMeshClick(meshName: string, worldPosition: THREE.Vector3): void {
    console.log(`🎮 Mesh clicked: ${meshName}`, worldPosition);
    
    // Find the design area for this mesh
    const designAreas = Array.from(this.elementMapper['designAreas'].values());
    const designArea = designAreas.find(area => area.areaName === meshName);
    
    if (designArea) {
      this.setCurrentDesignArea(designArea);
    }
  }

  /**
   * Handle element update from bridge
   */
  private handleElementUpdate(elementId: string, element: DesignElement): void {
    this.updateElementVisuals(elementId);
    
    if (this.callbacks.onElementUpdate) {
      this.callbacks.onElementUpdate(elementId, element);
    }
  }

  /**
   * Handle element selection from bridge
   */
  private handleElementSelect(elementId: string | null): void {
    this.selectElement(elementId);
  }

  /**
   * Handle canvas update from bridge
   */
  private handleCanvasUpdate(canvas: HTMLCanvasElement): void {
    if (this.callbacks.onCanvasUpdate) {
      this.callbacks.onCanvasUpdate(canvas);
    }
  }

  /**
   * Handle transform operations
   */
  private handleTransformStart(operation: TransformOperation): void {
    console.log(`🎮 Transform start: ${operation.type} on ${operation.elementId}`);
  }

  private handleTransformUpdate(operation: TransformOperation): void {
    // Update the element through the bridge
    const element = this.bridge.getElement(operation.elementId);
    if (element) {
      const updates: Partial<DesignElement> = {
        x: operation.currentPosition.uvCoordinates.u * 512, // Convert UV to canvas coords
        y: operation.currentPosition.uvCoordinates.v * 512,
        rotation: operation.currentPosition.transform.rotation
      };
      
      this.bridge.updateElement(operation.elementId, updates);
    }
  }

  private handleTransformEnd(operation: TransformOperation): void {
    console.log(`🎮 Transform end: ${operation.type} on ${operation.elementId}`);
  }

  /**
   * Update configuration
   */
  updateConfig(config: Partial<InteractionManagerConfig>): void {
    Object.assign(this.config, config);
    
    // Update visual system visibility
    this.visualSelectors.setVisible(this.config.enableVisualSelectors);
    this.hoverEffects.setVisible(this.config.enableHoverEffects);
  }

  /**
   * Set callbacks
   */
  setCallbacks(callbacks: InteractionCallbacks): void {
    Object.assign(this.callbacks, callbacks);
  }

  /**
   * Get current state
   */
  getState(): {
    selectedElementId: string | null;
    hoveredElementId: string | null;
    currentDesignArea: string | null;
    elementCount: number;
  } {
    return {
      selectedElementId: this.selectedElementId,
      hoveredElementId: this.hoveredElementId,
      currentDesignArea: this.currentDesignArea?.areaName || null,
      elementCount: this.bridge.getAllElements().length
    };
  }

  /**
   * Get all elements
   */
  getAllElements(): DesignElement[] {
    return this.bridge.getAllElements();
  }

  /**
   * Clear all elements
   */
  clear(): void {
    this.bridge.clear();
    this.visualSelectors.clear();
    this.hoverEffects.clear();
    this.selectedElementId = null;
    this.hoveredElementId = null;
  }

  /**
   * Dispose of the interaction manager
   */
  dispose(): void {
    if (this.isInitialized) {
      this.raycaster.dispose();
      this.bridge.dispose();
      this.transformHandles.dispose();
      this.hoverEffects.dispose();
      this.fabricCanvas.dispose();
      
      this.isInitialized = false;
      console.log('🎮 3D Interaction Manager disposed');
    }
  }

  /**
   * Get Fabric canvas for external access
   */
  getFabricCanvas(): fabric.Canvas {
    return this.fabricCanvas;
  }

  /**
   * Enable/disable debug mode
   */
  setDebugMode(enabled: boolean): void {
    this.config.debugMode = enabled;
    console.log(`🎮 Debug mode ${enabled ? 'enabled' : 'disabled'}`);
  }
}
