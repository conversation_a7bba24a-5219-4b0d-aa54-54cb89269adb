// SVG Parser utility for extracting mapping areas from uploaded SVG files

export interface SVGMappingArea {
  id: string
  name: string
  pathData: string
  bounds: {
    x: number
    y: number
    width: number
    height: number
  }
}

export interface SVGViewBox {
  x: number
  y: number
  width: number
  height: number
}

/**
 * Parse SVG content and extract mapping areas
 */
export function parseSVGMappingAreas(svgContent: string): SVGMappingArea[] {
  const parser = new DOMParser()
  const svgDoc = parser.parseFromString(svgContent, 'image/svg+xml')
  
  // Check for parsing errors
  const parserError = svgDoc.querySelector('parsererror')
  if (parserError) {
    throw new Error('Invalid SVG content')
  }

  const mappingAreas: SVGMappingArea[] = []
  
  // Find all elements with IDs (these will be mapping areas)
  const elementsWithIds = svgDoc.querySelectorAll('[id]')
  
  elementsWithIds.forEach((element) => {
    const id = element.getAttribute('id')
    if (!id) return

    // Skip certain system IDs
    if (id.startsWith('_') || id.includes('defs') || id.includes('gradient')) {
      return
    }

    const pathData = extractPathFromElement(element)
    if (!pathData) return

    const bounds = calculatePathBounds(pathData)
    
    mappingAreas.push({
      id,
      name: formatAreaName(id),
      pathData,
      bounds
    })
  })

  return mappingAreas
}

/**
 * Extract SVG viewBox
 */
export function extractSVGViewBox(svgContent: string): SVGViewBox {
  const viewBoxMatch = svgContent.match(/viewBox="([^"]*)"/)
  
  if (viewBoxMatch) {
    const [x, y, width, height] = viewBoxMatch[1].split(/\s+/).map(Number)
    return { x, y, width, height }
  }
  
  // Fallback: try to get width/height attributes
  const widthMatch = svgContent.match(/width="([^"]*)"/)
  const heightMatch = svgContent.match(/height="([^"]*)"/)
  
  const width = widthMatch ? parseFloat(widthMatch[1]) : 100
  const height = heightMatch ? parseFloat(heightMatch[1]) : 100
  
  return { x: 0, y: 0, width, height }
}

/**
 * Extract path data from various SVG elements
 */
function extractPathFromElement(element: Element): string | null {
  const tagName = element.tagName.toLowerCase()
  
  switch (tagName) {
    case 'path':
      return element.getAttribute('d') || null
      
    case 'rect':
      return rectToPath(element)
      
    case 'circle':
      return circleToPath(element)
      
    case 'ellipse':
      return ellipseToPath(element)
      
    case 'polygon':
      return polygonToPath(element)
      
    case 'polyline':
      return polylineToPath(element)
      
    default:
      return null
  }
}

/**
 * Convert rectangle to path
 */
function rectToPath(rect: Element): string {
  const x = parseFloat(rect.getAttribute('x') || '0')
  const y = parseFloat(rect.getAttribute('y') || '0')
  const width = parseFloat(rect.getAttribute('width') || '0')
  const height = parseFloat(rect.getAttribute('height') || '0')
  
  return `M ${x},${y} L ${x + width},${y} L ${x + width},${y + height} L ${x},${y + height} Z`
}

/**
 * Convert circle to path
 */
function circleToPath(circle: Element): string {
  const cx = parseFloat(circle.getAttribute('cx') || '0')
  const cy = parseFloat(circle.getAttribute('cy') || '0')
  const r = parseFloat(circle.getAttribute('r') || '0')
  
  return `M ${cx - r},${cy} A ${r},${r} 0 1,1 ${cx + r},${cy} A ${r},${r} 0 1,1 ${cx - r},${cy} Z`
}

/**
 * Convert ellipse to path
 */
function ellipseToPath(ellipse: Element): string {
  const cx = parseFloat(ellipse.getAttribute('cx') || '0')
  const cy = parseFloat(ellipse.getAttribute('cy') || '0')
  const rx = parseFloat(ellipse.getAttribute('rx') || '0')
  const ry = parseFloat(ellipse.getAttribute('ry') || '0')
  
  return `M ${cx - rx},${cy} A ${rx},${ry} 0 1,1 ${cx + rx},${cy} A ${rx},${ry} 0 1,1 ${cx - rx},${cy} Z`
}

/**
 * Convert polygon to path
 */
function polygonToPath(polygon: Element): string {
  const points = polygon.getAttribute('points') || ''
  const coords = points.trim().split(/\s+|,/).filter(Boolean)
  
  if (coords.length < 4) return ''
  
  let path = `M ${coords[0]},${coords[1]}`
  
  for (let i = 2; i < coords.length; i += 2) {
    path += ` L ${coords[i]},${coords[i + 1]}`
  }
  
  return path + ' Z'
}

/**
 * Convert polyline to path
 */
function polylineToPath(polyline: Element): string {
  const points = polyline.getAttribute('points') || ''
  const coords = points.trim().split(/\s+|,/).filter(Boolean)
  
  if (coords.length < 4) return ''
  
  let path = `M ${coords[0]},${coords[1]}`
  
  for (let i = 2; i < coords.length; i += 2) {
    path += ` L ${coords[i]},${coords[i + 1]}`
  }
  
  return path
}

/**
 * Calculate bounding box of a path
 */
function calculatePathBounds(pathData: string): { x: number; y: number; width: number; height: number } {
  // Simple bounds calculation - extract all coordinate pairs
  const coords = pathData.match(/[-+]?[0-9]*\.?[0-9]+/g)?.map(Number) || []
  
  if (coords.length < 2) {
    return { x: 0, y: 0, width: 0, height: 0 }
  }
  
  const xCoords = coords.filter((_, i) => i % 2 === 0)
  const yCoords = coords.filter((_, i) => i % 2 === 1)
  
  const minX = Math.min(...xCoords)
  const maxX = Math.max(...xCoords)
  const minY = Math.min(...yCoords)
  const maxY = Math.max(...yCoords)
  
  return {
    x: minX,
    y: minY,
    width: maxX - minX,
    height: maxY - minY
  }
}

/**
 * Format area ID to readable name
 */
function formatAreaName(id: string): string {
  return id
    .replace(/[-_]/g, ' ')
    .replace(/\b\w/g, l => l.toUpperCase())
    .trim()
}

/**
 * Transform coordinates between different coordinate systems
 */
export function transformCoordinates(
  x: number,
  y: number,
  fromViewBox: SVGViewBox,
  toViewBox: SVGViewBox
): { x: number; y: number } {
  // Normalize coordinates to 0-1 range
  const normalizedX = (x - fromViewBox.x) / fromViewBox.width
  const normalizedY = (y - fromViewBox.y) / fromViewBox.height
  
  // Transform to target coordinate system
  const targetX = toViewBox.x + (normalizedX * toViewBox.width)
  const targetY = toViewBox.y + (normalizedY * toViewBox.height)
  
  return { x: targetX, y: targetY }
}

/**
 * Transform an entire SVG path between coordinate systems
 */
export function transformSVGPath(
  pathData: string,
  fromViewBox: SVGViewBox,
  toViewBox: SVGViewBox
): string {
  // Extract all coordinate pairs from the path
  return pathData.replace(/(-?[0-9]*\.?[0-9]+),(-?[0-9]*\.?[0-9]+)/g, (match, x, y) => {
    const transformed = transformCoordinates(
      parseFloat(x),
      parseFloat(y),
      fromViewBox,
      toViewBox
    )
    return `${transformed.x},${transformed.y}`
  })
}

/**
 * Get mapping areas for a product and view type
 */
export async function getMappingAreasForProduct(
  supabase: any,
  productId: string,
  viewType: 'mockup' | 'printfile'
): Promise<any[]> {
  const { data, error } = await supabase
    .from('product_mapping_areas')
    .select('*')
    .eq('product_id', productId)
    .eq('view_type', viewType)
    .eq('is_active', true)
    .order('sort_order')

  if (error) throw error
  return data || []
}

/**
 * Get SVG configuration for a product and view type
 */
export async function getSVGConfigForProduct(
  supabase: any,
  productId: string,
  viewType: 'mockup' | 'printfile'
): Promise<any | null> {
  const { data, error } = await supabase
    .from('svg_mapping_configs')
    .select('*')
    .eq('product_id', productId)
    .eq('view_type', viewType)
    .single()

  if (error && error.code !== 'PGRST116') throw error
  return data
}

/**
 * Transform design element coordinates between mockup and print file views
 */
export async function transformDesignElement(
  supabase: any,
  productId: string,
  element: { x: number; y: number; width: number; height: number },
  fromView: 'mockup' | 'printfile',
  toView: 'mockup' | 'printfile'
): Promise<{ x: number; y: number; width: number; height: number }> {
  if (fromView === toView) return element

  // Get SVG configs for both views
  const fromConfig = await getSVGConfigForProduct(supabase, productId, fromView)
  const toConfig = await getSVGConfigForProduct(supabase, productId, toView)

  if (!fromConfig || !toConfig) {
    throw new Error(`Missing SVG configuration for ${fromView} or ${toView} view`)
  }

  // Parse viewBoxes
  const fromViewBox = parseViewBox(fromConfig.svg_viewbox)
  const toViewBox = parseViewBox(toConfig.svg_viewbox)

  // Transform coordinates
  const topLeft = transformCoordinates(element.x, element.y, fromViewBox, toViewBox)
  const bottomRight = transformCoordinates(
    element.x + element.width,
    element.y + element.height,
    fromViewBox,
    toViewBox
  )

  return {
    x: topLeft.x,
    y: topLeft.y,
    width: bottomRight.x - topLeft.x,
    height: bottomRight.y - topLeft.y
  }
}

/**
 * Parse viewBox string to SVGViewBox object
 */
function parseViewBox(viewBoxString: string): SVGViewBox {
  const [x, y, width, height] = viewBoxString.split(/\s+/).map(Number)
  return { x, y, width, height }
}
