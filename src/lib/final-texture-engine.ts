/**
 * Final Texture Engine - Clean implementation without caching issues
 */

import * as THREE from 'three';
import { DesignAreaUV } from './uv-extractor';

export class FinalTextureEngine {
  private renderer: THREE.WebGLRenderer;
  private originalMaterials: Map<string, THREE.Material> = new Map();

  constructor(renderer: THREE.WebGLRenderer) {
    this.renderer = renderer;
  }

  /**
   * Apply design to a mesh - Clean implementation
   */
  applyDesignToMesh(designArea: DesignAreaUV, canvas: HTMLCanvasElement): void {
    const mesh = designArea.mesh;
    const meshName = designArea.areaName;
    
    console.log(`🎨 FINAL ENGINE: Applying design to ${meshName}`);

    // Store original material if not already stored
    if (!this.originalMaterials.has(meshName)) {
      this.originalMaterials.set(meshName, mesh.material as THREE.Material);
    }

    // Check if canvas has content
    const ctx = canvas.getContext('2d')!;
    const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
    let hasContent = false;
    let nonWhitePixels = 0;

    // Check for non-white pixels
    for (let i = 0; i < imageData.data.length; i += 4) {
      const r = imageData.data[i];
      const g = imageData.data[i + 1];
      const b = imageData.data[i + 2];
      if (r !== 255 || g !== 255 || b !== 255) {
        hasContent = true;
        nonWhitePixels++;
      }
    }

    console.log(`🔍 FINAL ENGINE: Canvas analysis for ${meshName}:`, {
      canvasSize: `${canvas.width}x${canvas.height}`,
      totalPixels: imageData.data.length / 4,
      nonWhitePixels,
      hasContent
    });

    if (!hasContent) {
      // Restore original material
      const originalMaterial = this.originalMaterials.get(meshName);
      if (originalMaterial) {
        mesh.material = originalMaterial;
        console.log(`🔄 FINAL ENGINE: Restored original material for ${meshName} (no content)`);
      }
      return;
    }

    // Dispose old material if it exists and is not the original
    const currentMaterial = mesh.material as THREE.Material;
    if (currentMaterial && currentMaterial !== this.originalMaterials.get(meshName)) {
      if (currentMaterial instanceof THREE.MeshBasicMaterial && currentMaterial.map) {
        currentMaterial.map.dispose();
      }
      currentMaterial.dispose();
    }

    // Create texture canvas using proper UV mapping
    const mapping = designArea.mapping;
    const textureCanvas = document.createElement('canvas');
    textureCanvas.width = mapping.textureSize.width;
    textureCanvas.height = mapping.textureSize.height;
    const textureCtx = textureCanvas.getContext('2d')!;

    // Fill with white background for clean texture
    textureCtx.fillStyle = '#FFFFFF';
    textureCtx.fillRect(0, 0, textureCanvas.width, textureCanvas.height);

    // Calculate where to place the design within the UV bounds
    const uvBounds = mapping.boundingBox;
    const uvWidth = uvBounds.max[0] - uvBounds.min[0];
    const uvHeight = uvBounds.max[1] - uvBounds.min[1];

    // Handle negative or invalid UV bounds by centering the design
    let x, y, width, height;

    if (uvWidth <= 0 || uvHeight <= 0 || uvBounds.min[0] < 0 || uvBounds.min[1] < 0) {
      // UV bounds are invalid, place design in center of texture
      console.log(`⚠️ FINAL ENGINE: Invalid UV bounds for ${meshName}, centering design`);
      console.log(`🔍 FINAL ENGINE: UV bounds details:`, {
        min: [uvBounds.min[0], uvBounds.min[1]],
        max: [uvBounds.max[0], uvBounds.max[1]],
        width: uvWidth,
        height: uvHeight,
        minX: uvBounds.min[0],
        minY: uvBounds.min[1],
        condition: `width: ${uvWidth} <= 0? ${uvWidth <= 0}, height: ${uvHeight} <= 0? ${uvHeight <= 0}, minX < 0? ${uvBounds.min[0] < 0}, minY < 0? ${uvBounds.min[1] < 0}`
      });
      width = Math.min(textureCanvas.width * 0.6, canvas.width);
      height = Math.min(textureCanvas.height * 0.6, canvas.height);
      x = (textureCanvas.width - width) / 2;
      y = (textureCanvas.height - height) / 2;
      console.log(`🔍 FINAL ENGINE: Centering design at:`, { x, y, width, height });
    } else {
      // Use UV bounds but ensure they're within texture space
      const normalizedMinX = Math.max(0, Math.min(1, uvBounds.min[0]));
      const normalizedMinY = Math.max(0, Math.min(1, uvBounds.min[1]));
      const normalizedMaxX = Math.max(0, Math.min(1, uvBounds.max[0]));
      const normalizedMaxY = Math.max(0, Math.min(1, uvBounds.max[1]));

      x = normalizedMinX * textureCanvas.width;
      y = (1 - normalizedMaxY) * textureCanvas.height; // Flip Y for texture coordinates
      width = (normalizedMaxX - normalizedMinX) * textureCanvas.width;
      height = (normalizedMaxY - normalizedMinY) * textureCanvas.height;

      console.log(`✅ FINAL ENGINE: Using actual UV bounds for ${meshName}:`, {
        originalUV: { min: [uvBounds.min[0], uvBounds.min[1]], max: [uvBounds.max[0], uvBounds.max[1]] },
        normalizedUV: { minX: normalizedMinX, minY: normalizedMinY, maxX: normalizedMaxX, maxY: normalizedMaxY },
        textureCoords: { x, y, width, height },
        textureSize: `${textureCanvas.width}x${textureCanvas.height}`
      });
    }

    console.log(`🎯 FINAL ENGINE: UV mapping for ${meshName}:`, {
      originalUvBounds: mapping.boundingBox,
      normalizedBounds: uvWidth <= 0 || uvHeight <= 0 || uvBounds.min[0] < 0 || uvBounds.min[1] < 0 ? 'CENTERED' : {
        minX: Math.max(0, Math.min(1, uvBounds.min[0])),
        minY: Math.max(0, Math.min(1, uvBounds.min[1])),
        maxX: Math.max(0, Math.min(1, uvBounds.max[0])),
        maxY: Math.max(0, Math.min(1, uvBounds.max[1]))
      },
      textureSize: `${textureCanvas.width}x${textureCanvas.height}`,
      targetRect: { x: Math.round(x), y: Math.round(y), width: Math.round(width), height: Math.round(height) }
    });

    // Draw the design canvas into the correct UV region (on top of magenta background)
    textureCtx.drawImage(
      canvas, // source canvas
      0, 0, canvas.width, canvas.height, // source rectangle (entire design)
      x, y, width, height // destination rectangle (UV bounds)
    );

    // Create texture with proper settings for UV-mapped GLB models
    const canvasTexture = new THREE.CanvasTexture(textureCanvas);
    canvasTexture.needsUpdate = true;
    canvasTexture.flipY = false; // Critical for GLB models - UV coordinates are already flipped
    canvasTexture.generateMipmaps = true; // Enable mipmaps for better quality
    canvasTexture.minFilter = THREE.LinearMipmapLinearFilter;
    canvasTexture.magFilter = THREE.LinearFilter;
    canvasTexture.wrapS = THREE.RepeatWrapping; // Allow texture to repeat if needed
    canvasTexture.wrapT = THREE.RepeatWrapping;

    // Create material with proper settings for UV-mapped GLB models
    const newMaterial = new THREE.MeshBasicMaterial({
      map: canvasTexture,
      transparent: true, // Enable transparency for better blending
      side: THREE.DoubleSide,
      alphaTest: 0.01, // Lower threshold for better texture visibility
      color: 0xffffff // Ensure white base color for proper texture display
    });

    // Apply material
    mesh.material = newMaterial;

    // Debug: Log mesh visibility and material properties
    console.log(`✅ FINAL ENGINE: Applied UV-MAPPED TEXTURE to ${meshName} (${nonWhitePixels} colored pixels)`);
    console.log(`🔍 FINAL ENGINE: Mesh visibility - visible: ${mesh.visible}, position: ${mesh.position.x}, ${mesh.position.y}, ${mesh.position.z}`);
    console.log(`🔍 FINAL ENGINE: Material properties - transparent: ${newMaterial.transparent}, opacity: ${newMaterial.opacity}, side: ${newMaterial.side}`);
  }

  /**
   * Clear design from mesh
   */
  clearDesign(designArea: DesignAreaUV): void {
    const mesh = designArea.mesh;
    const meshName = designArea.areaName;
    
    const originalMaterial = this.originalMaterials.get(meshName);
    if (originalMaterial) {
      mesh.material = originalMaterial;
      console.log(`🔄 FINAL ENGINE: Cleared design from ${meshName}`);
    }
  }

  /**
   * Dispose resources
   */
  dispose(): void {
    this.originalMaterials.clear();
  }
}
