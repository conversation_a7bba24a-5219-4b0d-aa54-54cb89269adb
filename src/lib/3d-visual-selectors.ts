/**
 * 3D Visual Selectors
 * Creates visual selection indicators and transform handles for design elements in 3D space
 */

import * as THREE from 'three';
import { Element3DPosition, InteractionState } from './3d-element-mapping';

export interface SelectorStyle {
  color: number;
  opacity: number;
  lineWidth: number;
  glowIntensity: number;
}

export interface HandleConfig {
  size: number;
  color: number;
  hoverColor: number;
  opacity: number;
}

export class ThreeDVisualSelectors {
  private scene: THREE.Scene;
  private camera: THREE.Camera;
  private selectors: Map<string, THREE.Group> = new Map();
  private handles: Map<string, THREE.Group> = new Map();
  
  // Visual styles
  private hoverStyle: SelectorStyle = {
    color: 0x00ff00,
    opacity: 0.6,
    lineWidth: 2,
    glowIntensity: 0.3
  };
  
  private selectedStyle: SelectorStyle = {
    color: 0x0088ff,
    opacity: 0.8,
    lineWidth: 3,
    glowIntensity: 0.5
  };
  
  private handleConfig: HandleConfig = {
    size: 0.02,
    color: 0x0088ff,
    hoverColor: 0xff8800,
    opacity: 0.9
  };

  constructor(scene: THREE.Scene, camera: THREE.Camera) {
    this.scene = scene;
    this.camera = camera;
  }

  /**
   * Create or update visual selector for an element
   */
  updateSelector(
    elementId: string, 
    position: Element3DPosition, 
    interactionState: InteractionState
  ): void {
    // Remove existing selector
    this.removeSelector(elementId);

    if (interactionState.isHovered || interactionState.isSelected) {
      const selector = this.createSelector(position, interactionState);
      this.selectors.set(elementId, selector);
      this.scene.add(selector);

      // Add transform handles for selected elements
      if (interactionState.isSelected) {
        const handles = this.createTransformHandles(position);
        this.handles.set(elementId, handles);
        this.scene.add(handles);
      }
    }
  }

  /**
   * Create a visual selector for an element
   */
  private createSelector(
    position: Element3DPosition, 
    interactionState: InteractionState
  ): THREE.Group {
    const group = new THREE.Group();
    const style = interactionState.isSelected ? this.selectedStyle : this.hoverStyle;

    // Calculate element bounds in world space
    const bounds = this.calculateWorldBounds(position);
    
    // Create selection box outline
    const boxGeometry = new THREE.BoxGeometry(
      bounds.width,
      bounds.height,
      0.001 // Very thin depth
    );
    
    // Create wireframe material
    const wireframeMaterial = new THREE.LineBasicMaterial({
      color: style.color,
      opacity: style.opacity,
      transparent: true,
      linewidth: style.lineWidth
    });
    
    const wireframeGeometry = new THREE.WireframeGeometry(boxGeometry);
    const wireframe = new THREE.LineSegments(wireframeGeometry, wireframeMaterial);
    
    // Position the wireframe
    wireframe.position.copy(position.worldPosition);
    wireframe.lookAt(
      position.worldPosition.clone().add(position.surfaceNormal)
    );
    
    group.add(wireframe);

    // Add glow effect for selected elements
    if (interactionState.isSelected) {
      const glowBox = this.createGlowEffect(bounds, style);
      glowBox.position.copy(position.worldPosition);
      glowBox.lookAt(
        position.worldPosition.clone().add(position.surfaceNormal)
      );
      group.add(glowBox);
    }

    // Add corner indicators
    const corners = this.createCornerIndicators(bounds, style);
    corners.position.copy(position.worldPosition);
    corners.lookAt(
      position.worldPosition.clone().add(position.surfaceNormal)
    );
    group.add(corners);

    return group;
  }

  /**
   * Calculate world space bounds for an element
   */
  private calculateWorldBounds(position: Element3DPosition): {
    width: number;
    height: number;
    depth: number;
  } {
    // Convert UV bounds to approximate world space bounds
    // This is a simplified calculation - in practice, you'd need more precise UV-to-world mapping
    const uvBounds = position.bounds;
    const uvWidth = uvBounds.max.u - uvBounds.min.u;
    const uvHeight = uvBounds.max.v - uvBounds.min.v;
    
    // Scale factor based on mesh size (approximate)
    const scaleFactor = 0.5; // Adjust based on your mesh scale
    
    return {
      width: uvWidth * scaleFactor,
      height: uvHeight * scaleFactor,
      depth: 0.001
    };
  }

  /**
   * Create glow effect for selected elements
   */
  private createGlowEffect(
    bounds: { width: number; height: number; depth: number },
    style: SelectorStyle
  ): THREE.Mesh {
    const glowGeometry = new THREE.PlaneGeometry(
      bounds.width * 1.1,
      bounds.height * 1.1
    );
    
    const glowMaterial = new THREE.MeshBasicMaterial({
      color: style.color,
      opacity: style.glowIntensity,
      transparent: true,
      side: THREE.DoubleSide,
      blending: THREE.AdditiveBlending
    });
    
    return new THREE.Mesh(glowGeometry, glowMaterial);
  }

  /**
   * Create corner indicators for selection box
   */
  private createCornerIndicators(
    bounds: { width: number; height: number; depth: number },
    style: SelectorStyle
  ): THREE.Group {
    const group = new THREE.Group();
    const cornerSize = 0.01;
    
    const cornerGeometry = new THREE.BoxGeometry(cornerSize, cornerSize, cornerSize);
    const cornerMaterial = new THREE.MeshBasicMaterial({
      color: style.color,
      opacity: style.opacity,
      transparent: true
    });

    // Create 4 corner indicators
    const positions = [
      [-bounds.width / 2, -bounds.height / 2, 0],
      [bounds.width / 2, -bounds.height / 2, 0],
      [bounds.width / 2, bounds.height / 2, 0],
      [-bounds.width / 2, bounds.height / 2, 0]
    ];

    positions.forEach(pos => {
      const corner = new THREE.Mesh(cornerGeometry, cornerMaterial);
      corner.position.set(pos[0], pos[1], pos[2]);
      group.add(corner);
    });

    return group;
  }

  /**
   * Create transform handles for selected elements
   */
  private createTransformHandles(position: Element3DPosition): THREE.Group {
    const group = new THREE.Group();
    const bounds = this.calculateWorldBounds(position);
    
    // Create different types of handles
    const moveHandle = this.createMoveHandle();
    const rotateHandle = this.createRotateHandle(bounds);
    const scaleHandles = this.createScaleHandles(bounds);
    
    // Position handles
    moveHandle.position.set(0, 0, 0.01);
    rotateHandle.position.set(0, 0, 0.01);
    
    group.add(moveHandle);
    group.add(rotateHandle);
    group.add(scaleHandles);
    
    // Position the entire handle group
    group.position.copy(position.worldPosition);
    group.lookAt(
      position.worldPosition.clone().add(position.surfaceNormal)
    );
    
    return group;
  }

  /**
   * Create move handle (center point)
   */
  private createMoveHandle(): THREE.Mesh {
    const geometry = new THREE.SphereGeometry(this.handleConfig.size);
    const material = new THREE.MeshBasicMaterial({
      color: this.handleConfig.color,
      opacity: this.handleConfig.opacity,
      transparent: true
    });
    
    const handle = new THREE.Mesh(geometry, material);
    handle.userData = { handleType: 'move' };
    
    return handle;
  }

  /**
   * Create rotation handle (circular ring)
   */
  private createRotateHandle(bounds: { width: number; height: number }): THREE.Mesh {
    const radius = Math.max(bounds.width, bounds.height) * 0.6;
    const geometry = new THREE.RingGeometry(
      radius - 0.005,
      radius + 0.005,
      32
    );
    
    const material = new THREE.MeshBasicMaterial({
      color: this.handleConfig.color,
      opacity: this.handleConfig.opacity * 0.7,
      transparent: true,
      side: THREE.DoubleSide
    });
    
    const handle = new THREE.Mesh(geometry, material);
    handle.userData = { handleType: 'rotate' };
    
    return handle;
  }

  /**
   * Create scale handles (corner and edge handles)
   */
  private createScaleHandles(bounds: { width: number; height: number }): THREE.Group {
    const group = new THREE.Group();
    const handleSize = this.handleConfig.size;
    
    const handleGeometry = new THREE.BoxGeometry(handleSize, handleSize, handleSize);
    const cornerMaterial = new THREE.MeshBasicMaterial({
      color: this.handleConfig.color,
      opacity: this.handleConfig.opacity,
      transparent: true
    });
    
    const edgeMaterial = new THREE.MeshBasicMaterial({
      color: this.handleConfig.color,
      opacity: this.handleConfig.opacity * 0.8,
      transparent: true
    });

    // Corner handles (for proportional scaling)
    const cornerPositions = [
      [-bounds.width / 2, -bounds.height / 2, 0, 'scale-corner-tl'],
      [bounds.width / 2, -bounds.height / 2, 0, 'scale-corner-tr'],
      [bounds.width / 2, bounds.height / 2, 0, 'scale-corner-br'],
      [-bounds.width / 2, bounds.height / 2, 0, 'scale-corner-bl']
    ];

    cornerPositions.forEach(([x, y, z, type]) => {
      const handle = new THREE.Mesh(handleGeometry, cornerMaterial);
      handle.position.set(x as number, y as number, z as number);
      handle.userData = { handleType: type };
      group.add(handle);
    });

    // Edge handles (for width/height scaling)
    const edgePositions = [
      [0, -bounds.height / 2, 0, 'scale-edge-top'],
      [bounds.width / 2, 0, 0, 'scale-edge-right'],
      [0, bounds.height / 2, 0, 'scale-edge-bottom'],
      [-bounds.width / 2, 0, 0, 'scale-edge-left']
    ];

    edgePositions.forEach(([x, y, z, type]) => {
      const handle = new THREE.Mesh(handleGeometry, edgeMaterial);
      handle.position.set(x as number, y as number, z as number);
      handle.userData = { handleType: type };
      group.add(handle);
    });

    return group;
  }

  /**
   * Remove selector for an element
   */
  removeSelector(elementId: string): void {
    const selector = this.selectors.get(elementId);
    if (selector) {
      this.scene.remove(selector);
      this.selectors.delete(elementId);
    }

    const handles = this.handles.get(elementId);
    if (handles) {
      this.scene.remove(handles);
      this.handles.delete(elementId);
    }
  }

  /**
   * Update selector styles
   */
  updateStyles(styles: {
    hover?: Partial<SelectorStyle>;
    selected?: Partial<SelectorStyle>;
    handles?: Partial<HandleConfig>;
  }): void {
    if (styles.hover) {
      Object.assign(this.hoverStyle, styles.hover);
    }
    if (styles.selected) {
      Object.assign(this.selectedStyle, styles.selected);
    }
    if (styles.handles) {
      Object.assign(this.handleConfig, styles.handles);
    }
  }

  /**
   * Get handle at screen position (for interaction)
   */
  getHandleAtPosition(
    screenX: number,
    screenY: number,
    canvas: HTMLElement
  ): { elementId: string; handleType: string } | null {
    // This would implement raycasting to detect handle clicks
    // For now, return null - this would be implemented with the raycaster
    return null;
  }

  /**
   * Clear all selectors
   */
  clear(): void {
    this.selectors.forEach(selector => {
      this.scene.remove(selector);
    });
    this.handles.forEach(handles => {
      this.scene.remove(handles);
    });
    
    this.selectors.clear();
    this.handles.clear();
  }

  /**
   * Update selector positions (call this when camera moves)
   */
  updateSelectorPositions(): void {
    // Update selectors to always face the camera
    this.selectors.forEach(selector => {
      selector.lookAt(this.camera.position);
    });
    
    this.handles.forEach(handles => {
      handles.lookAt(this.camera.position);
    });
  }

  /**
   * Set visibility of all selectors
   */
  setVisible(visible: boolean): void {
    this.selectors.forEach(selector => {
      selector.visible = visible;
    });
    
    this.handles.forEach(handles => {
      handles.visible = visible;
    });
  }
}
