/**
 * 3D Surface Dragging
 * Enables dragging design elements along 3D mesh surfaces with proper UV mapping
 */

import * as THREE from 'three';
import { Element3DPosition, ThreeDElementMapper } from './3d-element-mapping';
import { DesignAreaUV } from './uv-extractor';

export interface DragOperation {
  elementId: string;
  startPosition: Element3DPosition;
  currentPosition: Element3DPosition;
  startScreenPos: THREE.Vector2;
  currentScreenPos: THREE.Vector2;
  designArea: DesignAreaUV;
  isDragging: boolean;
}

export interface SurfaceDragConfig {
  sensitivity: number;
  smoothing: number;
  constrainToSurface: boolean;
  snapToGrid: boolean;
  gridSize: number;
}

export class ThreeDSurfaceDragger {
  private elementMapper: ThreeDElementMapper;
  private camera: THREE.Camera;
  private raycaster: THREE.Raycaster;
  private mouse: THREE.Vector2;
  
  // Drag state
  private activeDrag: DragOperation | null = null;
  private dragPlane: THREE.Plane = new THREE.Plane();
  private dragIntersection: THREE.Vector3 = new THREE.Vector3();
  
  // Configuration
  private config: SurfaceDragConfig = {
    sensitivity: 1.0,
    smoothing: 0.1,
    constrainToSurface: true,
    snapToGrid: false,
    gridSize: 0.1
  };
  
  // Callbacks
  private onDragStart?: (operation: DragOperation) => void;
  private onDragUpdate?: (operation: DragOperation) => void;
  private onDragEnd?: (operation: DragOperation) => void;

  constructor(elementMapper: ThreeDElementMapper, camera: THREE.Camera) {
    this.elementMapper = elementMapper;
    this.camera = camera;
    this.raycaster = new THREE.Raycaster();
    this.mouse = new THREE.Vector2();
  }

  /**
   * Start dragging an element
   */
  startDrag(
    elementId: string,
    screenX: number,
    screenY: number,
    canvas: HTMLElement
  ): boolean {
    const position = this.elementMapper.getElementPosition(elementId);
    if (!position) {
      console.warn(`🖱️ Cannot start drag: Element ${elementId} not found`);
      return false;
    }

    // Get the design area for this element
    const designArea = this.getDesignAreaForElement(position.meshName);
    if (!designArea) {
      console.warn(`🖱️ Cannot start drag: Design area ${position.meshName} not found`);
      return false;
    }

    // Create drag plane aligned with the mesh surface
    this.createDragPlane(position, designArea);

    // Initialize drag operation
    this.activeDrag = {
      elementId,
      startPosition: { ...position },
      currentPosition: { ...position },
      startScreenPos: new THREE.Vector2(screenX, screenY),
      currentScreenPos: new THREE.Vector2(screenX, screenY),
      designArea,
      isDragging: true
    };

    // Update interaction state
    this.elementMapper.updateInteractionState(elementId, {
      isDragging: true
    });

    // Trigger callback
    if (this.onDragStart) {
      this.onDragStart(this.activeDrag);
    }

    console.log(`🖱️ Started dragging element ${elementId}`);
    return true;
  }

  /**
   * Update drag operation
   */
  updateDrag(screenX: number, screenY: number, canvas: HTMLElement): boolean {
    if (!this.activeDrag) return false;

    this.activeDrag.currentScreenPos.set(screenX, screenY);

    // Convert screen coordinates to normalized device coordinates
    const rect = canvas.getBoundingClientRect();
    this.mouse.x = ((screenX - rect.left) / rect.width) * 2 - 1;
    this.mouse.y = -((screenY - rect.top) / rect.height) * 2 + 1;

    // Cast ray from camera through mouse position
    this.raycaster.setFromCamera(this.mouse, this.camera);

    // Find intersection with drag plane
    const intersected = this.raycaster.ray.intersectPlane(this.dragPlane, this.dragIntersection);
    
    if (intersected) {
      // Update element position based on intersection
      this.updateElementPositionFromIntersection(this.dragIntersection);
      
      // Trigger callback
      if (this.onDragUpdate) {
        this.onDragUpdate(this.activeDrag);
      }
      
      return true;
    }

    return false;
  }

  /**
   * End drag operation
   */
  endDrag(): boolean {
    if (!this.activeDrag) return false;

    const elementId = this.activeDrag.elementId;

    // Update interaction state
    this.elementMapper.updateInteractionState(elementId, {
      isDragging: false
    });

    // Trigger callback
    if (this.onDragEnd) {
      this.onDragEnd(this.activeDrag);
    }

    console.log(`🖱️ Ended dragging element ${elementId}`);
    
    this.activeDrag = null;
    return true;
  }

  /**
   * Create a drag plane aligned with the mesh surface
   */
  private createDragPlane(position: Element3DPosition, designArea: DesignAreaUV): void {
    // Use the surface normal to create a plane
    const normal = position.surfaceNormal.clone().normalize();
    const point = position.worldPosition.clone();
    
    this.dragPlane.setFromNormalAndCoplanarPoint(normal, point);
  }

  /**
   * Update element position from intersection point
   */
  private updateElementPositionFromIntersection(intersectionPoint: THREE.Vector3): void {
    if (!this.activeDrag) return;

    // Apply smoothing
    const smoothedPosition = this.activeDrag.currentPosition.worldPosition.clone()
      .lerp(intersectionPoint, 1 - this.config.smoothing);

    // Apply snapping if enabled
    if (this.config.snapToGrid) {
      this.applyGridSnapping(smoothedPosition);
    }

    // Constrain to surface if enabled
    if (this.config.constrainToSurface) {
      this.constrainToSurface(smoothedPosition);
    }

    // Update the current position
    this.activeDrag.currentPosition.worldPosition = smoothedPosition;

    // Convert world position back to UV coordinates
    this.updateUVFromWorldPosition();

    // Update element mapper
    this.elementMapper.updateElementPosition(
      this.activeDrag.elementId,
      this.activeDrag.currentPosition
    );
  }

  /**
   * Apply grid snapping to position
   */
  private applyGridSnapping(position: THREE.Vector3): void {
    const gridSize = this.config.gridSize;
    position.x = Math.round(position.x / gridSize) * gridSize;
    position.y = Math.round(position.y / gridSize) * gridSize;
    position.z = Math.round(position.z / gridSize) * gridSize;
  }

  /**
   * Constrain position to mesh surface
   */
  private constrainToSurface(position: THREE.Vector3): void {
    if (!this.activeDrag) return;

    const designArea = this.activeDrag.designArea;
    const mesh = designArea.mesh;

    // Cast a ray from the position towards the mesh to find the closest surface point
    const direction = mesh.position.clone().sub(position).normalize();
    this.raycaster.set(position, direction);

    const intersects = this.raycaster.intersectObject(mesh);
    if (intersects.length > 0) {
      position.copy(intersects[0].point);
    }
  }

  /**
   * Update UV coordinates from world position
   */
  private updateUVFromWorldPosition(): void {
    if (!this.activeDrag) return;

    const designArea = this.activeDrag.designArea;
    const worldPos = this.activeDrag.currentPosition.worldPosition;
    
    // Convert world position to UV coordinates
    const uvCoords = this.worldToUV(worldPos, designArea);
    this.activeDrag.currentPosition.uvCoordinates = uvCoords;
  }

  /**
   * Convert world position to UV coordinates
   */
  private worldToUV(worldPos: THREE.Vector3, designArea: DesignAreaUV): { u: number; v: number } {
    const mesh = designArea.mesh;
    const geometry = mesh.geometry;
    
    // Convert world position to local position
    const localPos = worldPos.clone();
    mesh.worldToLocal(localPos);
    
    // Find the closest vertex in the mesh
    const positionAttribute = geometry.attributes.position;
    const uvAttribute = geometry.attributes.uv;
    
    let closestDistance = Infinity;
    let closestUV = { u: 0.5, v: 0.5 }; // Default to center
    
    for (let i = 0; i < positionAttribute.count; i++) {
      const vertex = new THREE.Vector3(
        positionAttribute.getX(i),
        positionAttribute.getY(i),
        positionAttribute.getZ(i)
      );
      
      const distance = localPos.distanceTo(vertex);
      if (distance < closestDistance) {
        closestDistance = distance;
        closestUV = {
          u: uvAttribute.getX(i),
          v: uvAttribute.getY(i)
        };
      }
    }
    
    return closestUV;
  }

  /**
   * Get design area for element
   */
  private getDesignAreaForElement(meshName: string): DesignAreaUV | null {
    // Access the design areas from the element mapper
    const designAreas = this.elementMapper['designAreas'] as Map<string, DesignAreaUV>;
    return designAreas.get(meshName) || null;
  }

  /**
   * Check if currently dragging
   */
  isDragging(): boolean {
    return this.activeDrag !== null;
  }

  /**
   * Get current drag operation
   */
  getCurrentDrag(): DragOperation | null {
    return this.activeDrag;
  }

  /**
   * Cancel current drag operation
   */
  cancelDrag(): void {
    if (this.activeDrag) {
      const elementId = this.activeDrag.elementId;
      
      // Restore original position
      this.elementMapper.updateElementPosition(
        elementId,
        this.activeDrag.startPosition
      );
      
      // Update interaction state
      this.elementMapper.updateInteractionState(elementId, {
        isDragging: false
      });
      
      console.log(`🖱️ Cancelled dragging element ${elementId}`);
      this.activeDrag = null;
    }
  }

  /**
   * Update configuration
   */
  updateConfig(config: Partial<SurfaceDragConfig>): void {
    Object.assign(this.config, config);
  }

  /**
   * Set callbacks
   */
  setCallbacks(callbacks: {
    onDragStart?: (operation: DragOperation) => void;
    onDragUpdate?: (operation: DragOperation) => void;
    onDragEnd?: (operation: DragOperation) => void;
  }): void {
    Object.assign(this, callbacks);
  }

  /**
   * Get drag configuration
   */
  getConfig(): SurfaceDragConfig {
    return { ...this.config };
  }

  /**
   * Enable/disable surface constraint
   */
  setSurfaceConstraint(enabled: boolean): void {
    this.config.constrainToSurface = enabled;
  }

  /**
   * Enable/disable grid snapping
   */
  setGridSnapping(enabled: boolean, gridSize?: number): void {
    this.config.snapToGrid = enabled;
    if (gridSize !== undefined) {
      this.config.gridSize = gridSize;
    }
  }

  /**
   * Set drag sensitivity
   */
  setSensitivity(sensitivity: number): void {
    this.config.sensitivity = Math.max(0.1, Math.min(5.0, sensitivity));
  }

  /**
   * Set smoothing factor
   */
  setSmoothing(smoothing: number): void {
    this.config.smoothing = Math.max(0, Math.min(1, smoothing));
  }

  /**
   * Get drag statistics (for debugging)
   */
  getDragStats(): {
    isDragging: boolean;
    elementId: string | null;
    dragDistance: number;
    screenDistance: number;
  } {
    if (!this.activeDrag) {
      return {
        isDragging: false,
        elementId: null,
        dragDistance: 0,
        screenDistance: 0
      };
    }

    const worldDistance = this.activeDrag.startPosition.worldPosition
      .distanceTo(this.activeDrag.currentPosition.worldPosition);
    
    const screenDistance = this.activeDrag.startScreenPos
      .distanceTo(this.activeDrag.currentScreenPos);

    return {
      isDragging: true,
      elementId: this.activeDrag.elementId,
      dragDistance: worldDistance,
      screenDistance
    };
  }
}
