/**
 * GLB Analysis System for Product Customizer
 * Analyzes PopCustoms GLB files to extract design areas, UV mapping, and material structure
 */

import * as THREE from 'three';
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader.js';
import { DRACOLoader } from 'three/examples/jsm/loaders/DRACOLoader.js';
import { KTX2Loader } from 'three/examples/jsm/loaders/KTX2Loader.js';

export interface DesignArea {
  name: string;
  mesh: THREE.Mesh;
  material: THREE.Material;
  uvCoordinates: Float32Array;
  boundingBox: THREE.Box3;
  center: THREE.Vector3;
}

export interface ProductAnalysis {
  productCode: string;
  productType: 'apparel' | 'drinkware' | 'footwear' | 'accessories' | 'unknown';
  views: {
    front?: string;
    back?: string;
    combination?: string;
  };
  designAreas: DesignArea[];
  textures: {
    [key: string]: string; // view -> texture path
  };
  previews: {
    [key: string]: string; // view -> preview path
  };
  metadata: {
    totalVertices: number;
    totalFaces: number;
    fileSize: number;
    hasCompression: boolean;
    hasKTX2Textures: boolean;
  };
}

export class GLBAnalyzer {
  private loader: GLTFLoader;
  private dracoLoader: DRACOLoader;
  private ktx2Loader: KTX2Loader;
  private renderer: THREE.WebGLRenderer;

  constructor() {
    // Initialize Three.js loaders
    this.loader = new GLTFLoader();

    // Setup Draco compression loader
    this.dracoLoader = new DRACOLoader();
    this.dracoLoader.setDecoderPath('/draco/');
    this.loader.setDRACOLoader(this.dracoLoader);

    // Setup KTX2 texture loader
    this.renderer = new THREE.WebGLRenderer();
    this.ktx2Loader = new KTX2Loader();
    this.ktx2Loader.setTranscoderPath('/basis/');
    this.ktx2Loader.detectSupport(this.renderer);
    this.loader.setKTX2Loader(this.ktx2Loader);
  }

  /**
   * Analyze a complete product from PopCustoms structure
   */
  async analyzeProduct(basePath: string, productCode: string): Promise<ProductAnalysis> {
    console.log(`🔍 Analyzing product: ${productCode}`);

    const analysis: ProductAnalysis = {
      productCode,
      productType: this.detectProductType(productCode),
      views: {},
      designAreas: [],
      textures: {},
      previews: {},
      metadata: {
        totalVertices: 0,
        totalFaces: 0,
        fileSize: 0,
        hasCompression: false,
        hasKTX2Textures: false
      }
    };

    // Define expected file structure based on PopCustoms pattern
    const viewFiles = [
      { key: 'front', file: '1-Front.glb' },
      { key: 'back', file: '2-Back.glb' },
      { key: 'combination', file: '3-Combination.glb' }
    ];

    // Analyze each view
    for (const view of viewFiles) {
      const glbPath = `${basePath}/${productCode}/VisualModels/${view.file}`;
      const texturePath = `${basePath}/${productCode}/VisualModels/Assets/${productCode}/Texture/${view.key === 'front' ? '1' : view.key === 'back' ? '2' : '3'}-${view.key === 'combination' ? 'Combination' : view.key.charAt(0).toUpperCase() + view.key.slice(1)}-white.ktx2`;
      const previewPath = `${basePath}/${productCode}/VisualModels/Assets/${productCode}/Previews/${view.key === 'front' ? '1' : view.key === 'back' ? '2' : '3'}-${view.key === 'combination' ? 'Combination' : view.key.charAt(0).toUpperCase() + view.key.slice(1)}-white.jpg`;

      try {
        // Check if files exist and analyze
        const glbAnalysis = await this.analyzeGLBFile(glbPath);

        analysis.views[view.key as keyof typeof analysis.views] = glbPath;
        analysis.textures[view.key] = texturePath;
        analysis.previews[view.key] = previewPath;

        // Merge design areas from this view
        analysis.designAreas.push(...glbAnalysis.designAreas);

        // Update metadata
        analysis.metadata.totalVertices += glbAnalysis.metadata.totalVertices;
        analysis.metadata.totalFaces += glbAnalysis.metadata.totalFaces;
        analysis.metadata.fileSize += glbAnalysis.metadata.fileSize;
        analysis.metadata.hasCompression = analysis.metadata.hasCompression || glbAnalysis.metadata.hasCompression;

      } catch (error) {
        console.warn(`⚠️ Could not analyze ${view.file} for ${productCode}:`, error);
      }
    }

    // Check for KTX2 textures
    analysis.metadata.hasKTX2Textures = Object.values(analysis.textures).some(path => path.endsWith('.ktx2'));

    console.log(`✅ Analysis complete for ${productCode}:`, {
      designAreas: analysis.designAreas.length,
      views: Object.keys(analysis.views).length,
      vertices: analysis.metadata.totalVertices,
      faces: analysis.metadata.totalFaces
    });

    return analysis;
  }

  /**
   * Analyze a single GLB file
   */
  async analyzeGLBFile(filePath: string): Promise<{
    designAreas: DesignArea[];
    metadata: ProductAnalysis['metadata'];
  }> {
    return new Promise((resolve, reject) => {
      this.loader.load(
        filePath,
        (gltf) => {
          const designAreas: DesignArea[] = [];
          let totalVertices = 0;
          let totalFaces = 0;
          let hasCompression = false;

          // Traverse the scene to find design areas
          gltf.scene.traverse((object) => {
            if (object instanceof THREE.Mesh) {
              const mesh = object as THREE.Mesh;
              const geometry = mesh.geometry;

              // Check if this looks like a design area based on name patterns
              const name = object.name.toLowerCase();
              if (this.isDesignArea(name)) {
                const designArea = this.extractDesignArea(mesh);
                if (designArea) {
                  designAreas.push(designArea);
                }
              }

              // Update metadata
              if (geometry.attributes.position) {
                totalVertices += geometry.attributes.position.count;
              }
              if (geometry.index) {
                totalFaces += geometry.index.count / 3;
              }

              // Check for Draco compression
              if (geometry.userData?.gltfExtensions?.KHR_draco_mesh_compression) {
                hasCompression = true;
              }
            }
          });

          resolve({
            designAreas,
            metadata: {
              totalVertices,
              totalFaces,
              fileSize: 0, // Would need file system access to get actual size
              hasCompression,
              hasKTX2Textures: false // Will be set at product level
            }
          });
        },
        (progress) => {
          console.log(`📊 Loading progress: ${(progress.loaded / progress.total * 100).toFixed(1)}%`);
        },
        (error) => {
          console.error(`❌ Error loading GLB file ${filePath}:`, error);
          reject(error);
        }
      );
    });
  }

  /**
   * Extract design area information from a mesh
   */
  private extractDesignArea(mesh: THREE.Mesh): DesignArea | null {
    const geometry = mesh.geometry;
    const material = mesh.material;

    if (!geometry.attributes.uv) {
      console.warn(`⚠️ Mesh ${mesh.name} has no UV coordinates`);
      return null;
    }

    // Calculate bounding box and center
    geometry.computeBoundingBox();
    const boundingBox = geometry.boundingBox!;
    const center = new THREE.Vector3();
    boundingBox.getCenter(center);

    return {
      name: mesh.name,
      mesh,
      material: Array.isArray(material) ? material[0] : material,
      uvCoordinates: geometry.attributes.uv.array as Float32Array,
      boundingBox,
      center
    };
  }

  /**
   * Determine if a mesh name indicates a design area
   */
  private isDesignArea(name: string): boolean {
    const designAreaPatterns = [
      'front', 'back', 'left', 'right', 'sleeve', 'collar', 'chest', 'pocket',
      'side', 'bottom', 'top', 'handle', 'body', 'panel', 'area'
    ];

    return designAreaPatterns.some(pattern => name.includes(pattern));
  }

  /**
   * Detect product type based on product code or structure
   */
  private detectProductType(productCode: string): ProductAnalysis['productType'] {
    const code = productCode.toLowerCase();

    // Common patterns in product codes
    if (code.includes('shirt') || code.includes('tee') || code.includes('hoodie') ||
        code.includes('tank') || code.includes('polo') || code.startsWith('ys_')) {
      return 'apparel';
    }

    if (code.includes('mug') || code.includes('cup') || code.includes('bottle') ||
        code.includes('tumbler') || code.includes('glass')) {
      return 'drinkware';
    }

    if (code.includes('shoe') || code.includes('sneaker') || code.includes('boot') ||
        code.includes('sandal') || code.includes('slipper')) {
      return 'footwear';
    }

    if (code.includes('bag') || code.includes('hat') || code.includes('cap') ||
        code.includes('wallet') || code.includes('case') || code.includes('keychain')) {
      return 'accessories';
    }

    return 'unknown';
  }

  /**
   * Dispose of resources
   */
  dispose(): void {
    this.dracoLoader.dispose();
    this.ktx2Loader.dispose();
    this.renderer.dispose();
  }
}

export default GLBAnalyzer;