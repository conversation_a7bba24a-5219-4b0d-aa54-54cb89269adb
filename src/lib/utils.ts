// General utility functions that don't depend on Konva

/**
 * Generate a unique ID for design elements
 */
export const generateId = (): string => {
  return `element_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
}

/**
 * Clamp a value between min and max
 */
export const clamp = (value: number, min: number, max: number): number => {
  return Math.min(Math.max(value, min), max)
}

/**
 * Convert degrees to radians
 */
export const degToRad = (degrees: number): number => {
  return (degrees * Math.PI) / 180
}

/**
 * Convert radians to degrees
 */
export const radToDeg = (radians: number): number => {
  return (radians * 180) / Math.PI
}

/**
 * Round a number to specified decimal places
 */
export const roundTo = (value: number, decimals: number = 0): number => {
  const factor = Math.pow(10, decimals)
  return Math.round(value * factor) / factor
}

/**
 * Check if a point is inside a rectangle
 */
export const isPointInRect = (
  point: { x: number; y: number },
  rect: { x: number; y: number; width: number; height: number }
): boolean => {
  return (
    point.x >= rect.x &&
    point.x <= rect.x + rect.width &&
    point.y >= rect.y &&
    point.y <= rect.y + rect.height
  )
}

/**
 * Calculate distance between two points
 */
export const distance = (
  point1: { x: number; y: number },
  point2: { x: number; y: number }
): number => {
  const dx = point2.x - point1.x
  const dy = point2.y - point1.y
  return Math.sqrt(dx * dx + dy * dy)
}

/**
 * Debounce function
 */
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let timeout: NodeJS.Timeout | null = null
  
  return (...args: Parameters<T>) => {
    if (timeout) {
      clearTimeout(timeout)
    }
    
    timeout = setTimeout(() => {
      func(...args)
    }, wait)
  }
}

/**
 * Throttle function
 */
export const throttle = <T extends (...args: any[]) => any>(
  func: T,
  limit: number
): ((...args: Parameters<T>) => void) => {
  let inThrottle: boolean = false

  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args)
      inThrottle = true
      setTimeout(() => {
        inThrottle = false
      }, limit)
    }
  }
}

/**
 * Calculate responsive canvas dimensions based on viewport
 */
export function calculateCanvasDimensions(): {
  width: number
  height: number
  maxSize: number
} {
  if (typeof window === 'undefined') {
    return { width: 600, height: 600, maxSize: 600 }
  }

  const viewportWidth = window.innerWidth
  const viewportHeight = window.innerHeight

  let availableWidth: number
  let availableHeight: number

  if (viewportWidth < 768) {
    // Mobile layout
    const headerHeight = 48
    const toolsHeight = 80
    const padding = 16
    const safeArea = 20

    availableWidth = viewportWidth - padding
    availableHeight = viewportHeight - headerHeight - toolsHeight - safeArea

    availableHeight = Math.max(availableHeight, 250)
    availableWidth = Math.max(availableWidth, 250)
  } else {
    // Desktop layout
    availableWidth = viewportWidth - 80 - 120
    availableHeight = viewportHeight - 80 - 120
  }

  let maxSize: number

  if (viewportWidth >= 1920) {
    maxSize = Math.min(800, availableWidth, availableHeight)
  } else if (viewportWidth >= 1440) {
    maxSize = Math.min(700, availableWidth, availableHeight)
  } else if (viewportWidth >= 1024) {
    maxSize = Math.min(600, availableWidth, availableHeight)
  } else if (viewportWidth >= 768) {
    maxSize = Math.min(500, availableWidth, availableHeight)
  } else {
    // Mobile sizing
    const mobileMaxSize = Math.min(availableWidth, availableHeight)

    if (viewportWidth <= 375) {
      maxSize = Math.min(320, mobileMaxSize)
    } else if (viewportWidth <= 414) {
      maxSize = Math.min(360, mobileMaxSize)
    } else {
      maxSize = Math.min(400, mobileMaxSize)
    }
  }

  // Ensure minimum sizes
  if (viewportWidth < 768) {
    maxSize = Math.max(200, maxSize)
  } else {
    maxSize = Math.max(400, maxSize)
  }

  return {
    width: maxSize,
    height: maxSize,
    maxSize
  }
}
