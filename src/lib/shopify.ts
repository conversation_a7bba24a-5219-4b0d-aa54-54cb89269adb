import { shopifyApi, LATEST_API_VERSION, Session } from '@shopify/shopify-api'
import '@shopify/shopify-api/adapters/node'
import { restResources } from '@shopify/shopify-api/rest/admin/2024-01'
import crypto from 'crypto'

// Initialize Shopify API
const shopify = shopifyApi({
  apiKey: process.env.SHOPIFY_API_KEY!,
  apiSecretKey: process.env.SHOPIFY_API_SECRET!,
  scopes: process.env.SHOPIFY_SCOPES!.split(','),
  hostName: process.env.SHOPIFY_APP_URL!.replace(/https?:\/\//, ''),
  hostScheme: process.env.SHOPIFY_APP_URL!.startsWith('https') ? 'https' : 'http',
  apiVersion: LATEST_API_VERSION,
  isEmbeddedApp: true,
  restResources,
})

export { shopify }

// Shopify OAuth utilities
export class ShopifyOAuth {
  static generateInstallUrl(shop: string, state?: string): string {
    const params = new URLSearchParams({
      client_id: process.env.SHOPIFY_API_KEY!,
      scope: process.env.SHOPIFY_SCOPES!,
      redirect_uri: `${process.env.SHOPIFY_APP_URL}/api/auth/shopify/callback`,
      state: state || crypto.randomBytes(16).toString('hex'),
    })

    return `https://${shop}/admin/oauth/authorize?${params.toString()}`
  }

  static async exchangeCodeForToken(shop: string, code: string): Promise<string> {
    const response = await fetch(`https://${shop}/admin/oauth/access_token`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        client_id: process.env.SHOPIFY_API_KEY!,
        client_secret: process.env.SHOPIFY_API_SECRET!,
        code,
      }),
    })

    if (!response.ok) {
      throw new Error(`Failed to exchange code for token: ${response.statusText}`)
    }

    const data = await response.json()
    return data.access_token
  }

  static verifyHmac(data: any, hmac: string): boolean {
    const body = JSON.stringify(data)
    const hash = crypto
      .createHmac('sha256', process.env.SHOPIFY_API_SECRET!)
      .update(body, 'utf8')
      .digest('base64')

    return crypto.timingSafeEqual(Buffer.from(hash), Buffer.from(hmac))
  }

  static verifyWebhook(body: string, signature: string): boolean {
    const hash = crypto
      .createHmac('sha256', process.env.SHOPIFY_API_SECRET!)
      .update(body, 'utf8')
      .digest('base64')

    return crypto.timingSafeEqual(Buffer.from(hash), Buffer.from(signature))
  }
}

// Shopify API client
export class ShopifyClient {
  private session: Session

  constructor(shop: string, accessToken: string) {
    this.session = new Session({
      id: `${shop}_session`,
      shop,
      state: 'active',
      isOnline: false,
      accessToken,
      scope: process.env.SHOPIFY_SCOPES!,
    })
  }

  async getShopInfo() {
    const client = new shopify.clients.Rest({ session: this.session })
    const response = await client.get({ path: 'shop' })
    return response.body.shop
  }

  async getProducts(limit = 50, page_info?: string) {
    const client = new shopify.clients.Rest({ session: this.session })
    const params: any = { limit }
    if (page_info) params.page_info = page_info

    const response = await client.get({ path: 'products', query: params })
    return {
      products: response.body.products,
      pageInfo: response.pageInfo,
    }
  }

  async getProduct(productId: string) {
    const client = new shopify.clients.Rest({ session: this.session })
    const response = await client.get({ path: `products/${productId}` })
    return response.body.product
  }

  async createProduct(productData: any) {
    const client = new shopify.clients.Rest({ session: this.session })
    const response = await client.post({
      path: 'products',
      data: { product: productData },
    })
    return response.body.product
  }

  async updateProduct(productId: string, productData: any) {
    const client = new shopify.clients.Rest({ session: this.session })
    const response = await client.put({
      path: `products/${productId}`,
      data: { product: productData },
    })
    return response.body.product
  }

  async deleteProduct(productId: string) {
    const client = new shopify.clients.Rest({ session: this.session })
    await client.delete({ path: `products/${productId}` })
    return true
  }

  async getOrders(limit = 50, status = 'any') {
    const client = new shopify.clients.Rest({ session: this.session })
    const response = await client.get({
      path: 'orders',
      query: { limit, status },
    })
    return response.body.orders
  }

  async getOrder(orderId: string) {
    const client = new shopify.clients.Rest({ session: this.session })
    const response = await client.get({ path: `orders/${orderId}` })
    return response.body.order
  }

  async fulfillOrder(orderId: string, fulfillmentData: any) {
    const client = new shopify.clients.Rest({ session: this.session })
    const response = await client.post({
      path: `orders/${orderId}/fulfillments`,
      data: { fulfillment: fulfillmentData },
    })
    return response.body.fulfillment
  }

  async createWebhook(topic: string, address: string) {
    const client = new shopify.clients.Rest({ session: this.session })
    const response = await client.post({
      path: 'webhooks',
      data: {
        webhook: {
          topic,
          address,
          format: 'json',
        },
      },
    })
    return response.body.webhook
  }

  async getWebhooks() {
    const client = new shopify.clients.Rest({ session: this.session })
    const response = await client.get({ path: 'webhooks' })
    return response.body.webhooks
  }

  async deleteWebhook(webhookId: string) {
    const client = new shopify.clients.Rest({ session: this.session })
    await client.delete({ path: `webhooks/${webhookId}` })
    return true
  }
}

// Utility functions
export function validateShopDomain(shop: string): boolean {
  const shopRegex = /^[a-zA-Z0-9][a-zA-Z0-9-]*[a-zA-Z0-9]\.myshopify\.com$/
  return shopRegex.test(shop)
}

export function sanitizeShopDomain(shop: string): string {
  // Remove protocol if present
  shop = shop.replace(/^https?:\/\//, '')
  
  // Remove trailing slash
  shop = shop.replace(/\/$/, '')
  
  // Add .myshopify.com if not present
  if (!shop.includes('.myshopify.com')) {
    shop = `${shop}.myshopify.com`
  }
  
  return shop
}

export function generateAppUrl(shop: string, path = ''): string {
  const baseUrl = process.env.SHOPIFY_APP_URL!
  const cleanPath = path.startsWith('/') ? path : `/${path}`
  return `${baseUrl}${cleanPath}?shop=${shop}`
}

// Webhook topics
export const WEBHOOK_TOPICS = {
  APP_UNINSTALLED: 'app/uninstalled',
  ORDERS_CREATE: 'orders/create',
  ORDERS_UPDATED: 'orders/updated',
  ORDERS_PAID: 'orders/paid',
  ORDERS_CANCELLED: 'orders/cancelled',
  PRODUCTS_CREATE: 'products/create',
  PRODUCTS_UPDATE: 'products/update',
  PRODUCTS_DELETE: 'products/delete',
  CUSTOMERS_CREATE: 'customers/create',
  CUSTOMERS_UPDATE: 'customers/update',
} as const

export type WebhookTopic = typeof WEBHOOK_TOPICS[keyof typeof WEBHOOK_TOPICS]
