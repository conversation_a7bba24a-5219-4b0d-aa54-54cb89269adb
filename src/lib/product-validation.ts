/**
 * Product validation utilities for the customizer
 */

import { createSupabaseBrowserClient } from '@/lib/supabase'

export interface ValidationResult {
  isValid: boolean
  errors: string[]
  warnings?: string[]
}

export interface Product {
  id: string
  name: string
  canvas_config?: any
  print_files?: Array<{
    url: string
    filename: string
  }>
  mockup_files?: Array<{
    url: string
    filename: string
  }>
  product_images?: Array<{
    url: string
    alt_text?: string
    is_primary: boolean
  }>
}

/**
 * Validate if a product is properly configured for the customizer
 */
export async function validateProductSetup(product: Product): Promise<ValidationResult> {
  const errors: string[] = []
  const warnings: string[] = []

  try {
    // Check canvas configuration
    if (!product.canvas_config) {
      errors.push("Canvas configuration missing")
    } else {
      const config = product.canvas_config
      
      // Check print file configuration
      if (!config.print_file) {
        errors.push("Print file configuration missing")
      } else {
        if (!config.print_file.width_px || !config.print_file.height_px) {
          errors.push("Print file dimensions not configured")
        }
        if (!config.print_file.dpi || config.print_file.dpi !== 300) {
          warnings.push("Print file DPI should be 300 for optimal quality")
        }
      }

      // Check display configuration
      if (!config.display) {
        warnings.push("Display configuration missing - will use defaults")
      }
    }

    // Check required files
    if (!product.mockup_files || product.mockup_files.length === 0) {
      errors.push("Mockup overlay image missing")
    }

    if (!product.print_files || product.print_files.length === 0) {
      errors.push("Print guidelines image missing")
    }

    // Check mapping areas
    const supabase = createSupabaseBrowserClient()
    const { data: mappingAreas, error: mappingError } = await supabase
      .from('product_mapping_areas')
      .select('*')
      .eq('product_id', product.id)

    if (mappingError) {
      errors.push("Failed to check mapping areas configuration")
    } else if (!mappingAreas || mappingAreas.length === 0) {
      errors.push("No design areas configured")
    } else {
      // Check if we have both mockup and print file areas
      const mockupAreas = mappingAreas.filter(area => area.view_type === 'mockup')
      const printFileAreas = mappingAreas.filter(area => area.view_type === 'printfile')

      if (mockupAreas.length === 0) {
        errors.push("No mockup design areas configured")
      }

      if (printFileAreas.length === 0) {
        errors.push("No print file design areas configured")
      }

      // Check if area IDs match between views
      const mockupAreaIds = new Set(mockupAreas.map(area => area.area_id))
      const printFileAreaIds = new Set(printFileAreas.map(area => area.area_id))

      const missingInPrintFile = [...mockupAreaIds].filter(id => !printFileAreaIds.has(id))
      const missingInMockup = [...printFileAreaIds].filter(id => !mockupAreaIds.has(id))

      if (missingInPrintFile.length > 0) {
        warnings.push(`Areas missing in print file view: ${missingInPrintFile.join(', ')}`)
      }

      if (missingInMockup.length > 0) {
        warnings.push(`Areas missing in mockup view: ${missingInMockup.join(', ')}`)
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    }

  } catch (error) {
    console.error('Error validating product setup:', error)
    return {
      isValid: false,
      errors: ['Failed to validate product configuration'],
      warnings
    }
  }
}

/**
 * Check if a product has basic files needed for customizer
 */
export function hasBasicFiles(product: Product): boolean {
  const hasMockupFiles = Boolean(product.mockup_files && product.mockup_files.length > 0)
  const hasPrintFiles = Boolean(product.print_files && product.print_files.length > 0)
  const hasProductImages = Boolean(product.product_images && product.product_images.length > 0)

  return hasMockupFiles || hasPrintFiles || hasProductImages
}

/**
 * Get a user-friendly error message for missing configuration
 */
export function getSetupErrorMessage(errors: string[]): string {
  if (errors.length === 0) return ''

  const criticalErrors = errors.filter(error => 
    error.includes('Canvas configuration') ||
    error.includes('design areas') ||
    error.includes('image missing')
  )

  if (criticalErrors.length > 0) {
    return 'This product\'s customizer is not fully configured. Please contact the administrator to complete the setup.'
  }

  return 'Some configuration issues were found. The customizer may not work as expected.'
}

/**
 * Quick validation for customizer availability
 */
export async function isCustomizerAvailable(product: Product): Promise<boolean> {
  const validation = await validateProductSetup(product)
  return validation.isValid
}
