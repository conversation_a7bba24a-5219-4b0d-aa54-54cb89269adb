/**
 * Enhanced 3D Raycasting System
 * Detects clicks on design elements in 3D space and handles interaction events
 */

import * as THREE from 'three';
import { ThreeDElementMapper, Element3DPosition } from './3d-element-mapping';
import { DesignAreaUV } from './uv-extractor';

export interface RaycastHit {
  elementId: string;
  position: Element3DPosition;
  intersectionPoint: THREE.Vector3;
  distance: number;
  hitType: 'element' | 'mesh' | 'handle';
  handleType?: 'move' | 'rotate' | 'scale-corner' | 'scale-edge';
}

export interface MouseEventData {
  clientX: number;
  clientY: number;
  button?: number;
  ctrlKey?: boolean;
  shiftKey?: boolean;
  altKey?: boolean;
}

export class ThreeDRaycaster {
  private raycaster: THREE.Raycaster;
  private mouse: THREE.Vector2;
  private camera: THREE.Camera;
  private scene: THREE.Scene;
  private elementMapper: ThreeDElementMapper;
  private canvas: HTMLElement;
  
  // Interaction state
  private isDragging = false;
  private dragStartPosition = new THREE.Vector2();
  private dragCurrentPosition = new THREE.Vector2();
  private selectedElementId: string | null = null;
  private hoveredElementId: string | null = null;

  // Event callbacks
  private onElementClick?: (elementId: string, hit: RaycastHit) => void;
  private onElementHover?: (elementId: string | null, hit: RaycastHit | null) => void;
  private onElementDragStart?: (elementId: string, hit: RaycastHit) => void;
  private onElementDrag?: (elementId: string, deltaX: number, deltaY: number) => void;
  private onElementDragEnd?: (elementId: string) => void;
  private onMeshClick?: (meshName: string, worldPosition: THREE.Vector3) => void;

  constructor(
    camera: THREE.Camera,
    scene: THREE.Scene,
    canvas: HTMLElement,
    elementMapper: ThreeDElementMapper
  ) {
    this.raycaster = new THREE.Raycaster();
    this.mouse = new THREE.Vector2();
    this.camera = camera;
    this.scene = scene;
    this.canvas = canvas;
    this.elementMapper = elementMapper;

    this.setupEventListeners();
  }

  /**
   * Setup mouse and touch event listeners
   */
  private setupEventListeners(): void {
    // Mouse events
    this.canvas.addEventListener('mousedown', this.handleMouseDown.bind(this));
    this.canvas.addEventListener('mousemove', this.handleMouseMove.bind(this));
    this.canvas.addEventListener('mouseup', this.handleMouseUp.bind(this));
    this.canvas.addEventListener('click', this.handleClick.bind(this));

    // Touch events for mobile
    this.canvas.addEventListener('touchstart', this.handleTouchStart.bind(this));
    this.canvas.addEventListener('touchmove', this.handleTouchMove.bind(this));
    this.canvas.addEventListener('touchend', this.handleTouchEnd.bind(this));

    // Prevent context menu on right click
    this.canvas.addEventListener('contextmenu', (e) => e.preventDefault());
  }

  /**
   * Convert screen coordinates to normalized device coordinates
   */
  private screenToNDC(clientX: number, clientY: number): THREE.Vector2 {
    const rect = this.canvas.getBoundingClientRect();
    const x = ((clientX - rect.left) / rect.width) * 2 - 1;
    const y = -((clientY - rect.top) / rect.height) * 2 + 1;
    return new THREE.Vector2(x, y);
  }

  /**
   * Perform raycast and return hits
   */
  private performRaycast(screenX: number, screenY: number): RaycastHit[] {
    const ndc = this.screenToNDC(screenX, screenY);
    this.raycaster.setFromCamera(ndc, this.camera);

    const hits: RaycastHit[] = [];

    // First, check for element hits
    const elementHits = this.checkElementHits(ndc);
    hits.push(...elementHits);

    // If no element hits, check for mesh hits
    if (hits.length === 0) {
      const meshHits = this.checkMeshHits();
      hits.push(...meshHits);
    }

    // Sort by distance (closest first)
    hits.sort((a, b) => a.distance - b.distance);

    return hits;
  }

  /**
   * Check for hits on design elements
   */
  private checkElementHits(ndc: THREE.Vector2): RaycastHit[] {
    const hits: RaycastHit[] = [];
    
    // Get all element positions from the mapper
    const allElements = Array.from(this.elementMapper['elementPositions'].values());
    
    for (const position of allElements) {
      const hit = this.checkElementHit(position, ndc);
      if (hit) {
        hits.push(hit);
      }
    }

    return hits;
  }

  /**
   * Check if a specific element is hit by the ray
   */
  private checkElementHit(position: Element3DPosition, ndc: THREE.Vector2): RaycastHit | null {
    // Create a bounding box in world space for the element
    const worldPos = position.worldPosition;
    const bounds = position.bounds;
    
    // For now, use a simple distance-based hit detection
    // In a more advanced implementation, we'd create actual geometry for hit testing
    const screenPos = this.worldToScreen(worldPos);
    const screenDistance = Math.sqrt(
      Math.pow(ndc.x - screenPos.x, 2) + Math.pow(ndc.y - screenPos.y, 2)
    );

    // Hit threshold (adjust based on element size)
    const hitThreshold = 0.1; // NDC units

    if (screenDistance < hitThreshold) {
      return {
        elementId: position.elementId,
        position,
        intersectionPoint: worldPos.clone(),
        distance: screenDistance,
        hitType: 'element'
      };
    }

    return null;
  }

  /**
   * Check for hits on mesh surfaces
   */
  private checkMeshHits(): RaycastHit[] {
    const hits: RaycastHit[] = [];
    const intersects = this.raycaster.intersectObjects(this.scene.children, true);

    for (const intersect of intersects) {
      if (intersect.object instanceof THREE.Mesh) {
        const mesh = intersect.object as THREE.Mesh;
        
        // Only consider design area meshes
        if (this.isDesignAreaMesh(mesh.name)) {
          hits.push({
            elementId: '', // No element, this is a mesh hit
            position: {} as Element3DPosition, // Empty position for mesh hits
            intersectionPoint: intersect.point,
            distance: intersect.distance,
            hitType: 'mesh'
          });
        }
      }
    }

    return hits;
  }

  /**
   * Check if a mesh is a design area
   */
  private isDesignAreaMesh(meshName: string): boolean {
    const designAreaPatterns = [
      'front', 'back', 'left_sleeve', 'right_sleeve', 'sleeve',
      'collar', 'chest', 'pocket', 'side', 'panel'
    ];

    const lowerName = meshName.toLowerCase();
    return designAreaPatterns.some(pattern => lowerName.includes(pattern));
  }

  /**
   * Convert world position to screen coordinates
   */
  private worldToScreen(worldPos: THREE.Vector3): THREE.Vector2 {
    const screenPos = worldPos.clone().project(this.camera);
    return new THREE.Vector2(screenPos.x, screenPos.y);
  }

  /**
   * Handle mouse down events
   */
  private handleMouseDown(event: MouseEvent): void {
    const hits = this.performRaycast(event.clientX, event.clientY);
    
    if (hits.length > 0) {
      const hit = hits[0];
      
      if (hit.hitType === 'element') {
        this.selectedElementId = hit.elementId;
        this.isDragging = true;
        this.dragStartPosition.set(event.clientX, event.clientY);
        this.dragCurrentPosition.set(event.clientX, event.clientY);
        
        // Update interaction state
        this.elementMapper.updateInteractionState(hit.elementId, {
          isSelected: true,
          isDragging: true
        });

        // Trigger drag start callback
        if (this.onElementDragStart) {
          this.onElementDragStart(hit.elementId, hit);
        }
      }
    }
  }

  /**
   * Handle mouse move events
   */
  private handleMouseMove(event: MouseEvent): void {
    if (this.isDragging && this.selectedElementId) {
      // Calculate drag delta
      const deltaX = event.clientX - this.dragCurrentPosition.x;
      const deltaY = event.clientY - this.dragCurrentPosition.y;
      
      this.dragCurrentPosition.set(event.clientX, event.clientY);

      // Trigger drag callback
      if (this.onElementDrag) {
        this.onElementDrag(this.selectedElementId, deltaX, deltaY);
      }
    } else {
      // Handle hover
      const hits = this.performRaycast(event.clientX, event.clientY);
      const newHoveredId = hits.length > 0 && hits[0].hitType === 'element' ? hits[0].elementId : null;
      
      if (newHoveredId !== this.hoveredElementId) {
        // Update previous hovered element
        if (this.hoveredElementId) {
          this.elementMapper.updateInteractionState(this.hoveredElementId, {
            isHovered: false
          });
        }
        
        // Update new hovered element
        if (newHoveredId) {
          this.elementMapper.updateInteractionState(newHoveredId, {
            isHovered: true
          });
        }
        
        this.hoveredElementId = newHoveredId;
        
        // Trigger hover callback
        if (this.onElementHover) {
          this.onElementHover(newHoveredId, hits.length > 0 ? hits[0] : null);
        }
      }
    }
  }

  /**
   * Handle mouse up events
   */
  private handleMouseUp(event: MouseEvent): void {
    if (this.isDragging && this.selectedElementId) {
      // Update interaction state
      this.elementMapper.updateInteractionState(this.selectedElementId, {
        isDragging: false
      });

      // Trigger drag end callback
      if (this.onElementDragEnd) {
        this.onElementDragEnd(this.selectedElementId);
      }
    }

    this.isDragging = false;
    this.selectedElementId = null;
  }

  /**
   * Handle click events
   */
  private handleClick(event: MouseEvent): void {
    const hits = this.performRaycast(event.clientX, event.clientY);
    
    if (hits.length > 0) {
      const hit = hits[0];
      
      if (hit.hitType === 'element') {
        // Trigger element click callback
        if (this.onElementClick) {
          this.onElementClick(hit.elementId, hit);
        }
      } else if (hit.hitType === 'mesh') {
        // Trigger mesh click callback
        if (this.onMeshClick) {
          console.log('🔍 Getting mesh name from intersection...');
          const meshName = this.getMeshNameFromIntersection(hit.intersectionPoint);
          console.log('🔍 Mesh name result:', meshName);
          if (meshName) {
            this.onMeshClick(meshName, hit.intersectionPoint);
          } else {
            console.log('🔍 No valid mesh name found');
          }
        }
      }
    }
  }

  /**
   * Get mesh name from intersection point
   */
  private getMeshNameFromIntersection(point: THREE.Vector3): string | null {
    // Get all intersections
    const intersects = this.raycaster.intersectObjects(this.scene.children, true);

    // Filter out mask meshes and find the first valid design area mesh
    for (const intersect of intersects) {
      if (intersect.object instanceof THREE.Mesh) {
        const mesh = intersect.object as THREE.Mesh;
        const meshName = mesh.name;

        // Skip mask meshes and highlight meshes
        if (meshName.toLowerCase().includes('mask') ||
            meshName.toLowerCase().includes('highlight')) {
          continue;
        }

        // Prioritize design area meshes
        if (this.isDesignAreaMesh(meshName)) {
          console.log(`🎯 Found design area mesh: ${meshName}`);
          return meshName;
        }

        // If no design area mesh found, return any valid non-mask mesh
        if (meshName && meshName.trim() !== '') {
          console.log(`🎯 Found valid mesh: ${meshName}`);
          return meshName;
        }
      }
    }

    console.log('🎯 No valid mesh found in intersection');
    return null;
  }

  /**
   * Handle touch events (simplified for mobile support)
   */
  private handleTouchStart(event: TouchEvent): void {
    if (event.touches.length === 1) {
      const touch = event.touches[0];
      this.handleMouseDown({
        clientX: touch.clientX,
        clientY: touch.clientY,
        button: 0
      } as MouseEvent);
    }
  }

  private handleTouchMove(event: TouchEvent): void {
    if (event.touches.length === 1) {
      const touch = event.touches[0];
      this.handleMouseMove({
        clientX: touch.clientX,
        clientY: touch.clientY
      } as MouseEvent);
    }
  }

  private handleTouchEnd(event: TouchEvent): void {
    this.handleMouseUp({} as MouseEvent);
  }

  /**
   * Set event callbacks
   */
  setCallbacks(callbacks: {
    onElementClick?: (elementId: string, hit: RaycastHit) => void;
    onElementHover?: (elementId: string | null, hit: RaycastHit | null) => void;
    onElementDragStart?: (elementId: string, hit: RaycastHit) => void;
    onElementDrag?: (elementId: string, deltaX: number, deltaY: number) => void;
    onElementDragEnd?: (elementId: string) => void;
    onMeshClick?: (meshName: string, worldPosition: THREE.Vector3) => void;
  }): void {
    Object.assign(this, callbacks);
  }

  /**
   * Cleanup event listeners
   */
  dispose(): void {
    this.canvas.removeEventListener('mousedown', this.handleMouseDown);
    this.canvas.removeEventListener('mousemove', this.handleMouseMove);
    this.canvas.removeEventListener('mouseup', this.handleMouseUp);
    this.canvas.removeEventListener('click', this.handleClick);
    this.canvas.removeEventListener('touchstart', this.handleTouchStart);
    this.canvas.removeEventListener('touchmove', this.handleTouchMove);
    this.canvas.removeEventListener('touchend', this.handleTouchEnd);
  }
}
