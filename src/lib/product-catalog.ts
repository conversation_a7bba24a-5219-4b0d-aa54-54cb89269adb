/**
 * Product Catalog System
 * Scans and catalogs all PopCustoms products for analysis
 */

import { GLBAnalyzer, ProductAnalysis } from './glb-analyzer';
import * as fs from 'fs';
import * as path from 'path';

export interface ProductCatalog {
  totalProducts: number;
  productsByType: {
    [key: string]: number;
  };
  products: ProductAnalysis[];
  lastUpdated: Date;
  statistics: {
    totalVertices: number;
    totalFaces: number;
    totalFileSize: number;
    averageDesignAreas: number;
    compressionUsage: number;
    ktx2Usage: number;
  };
}

export class ProductCatalogBuilder {
  private analyzer: GLBAnalyzer;
  private basePath: string;

  constructor(basePath: string) {
    this.analyzer = new GLBAnalyzer();
    this.basePath = basePath;
  }

  /**
   * Scan directory for all product folders
   */
  async scanProducts(): Promise<string[]> {
    console.log(`🔍 Scanning for products in: ${this.basePath}`);

    try {
      const entries = await fs.promises.readdir(this.basePath, { withFileTypes: true });
      const productCodes = entries
        .filter(entry => entry.isDirectory())
        .map(entry => entry.name)
        .filter(name => !name.startsWith('.') && name !== 'ext'); // Filter out hidden and utility folders

      console.log(`📦 Found ${productCodes.length} potential products`);
      return productCodes;
    } catch (error) {
      console.error(`❌ Error scanning products directory:`, error);
      return [];
    }
  }

  /**
   * Build complete product catalog
   */
  async buildCatalog(): Promise<ProductCatalog> {
    console.log(`🏗️ Building product catalog...`);

    const productCodes = await this.scanProducts();
    const products: ProductAnalysis[] = [];
    const productsByType: { [key: string]: number } = {};

    let totalVertices = 0;
    let totalFaces = 0;
    let totalFileSize = 0;
    let totalDesignAreas = 0;
    let compressionCount = 0;
    let ktx2Count = 0;

    // Analyze each product
    for (let i = 0; i < productCodes.length; i++) {
      const productCode = productCodes[i];
      console.log(`📊 Analyzing product ${i + 1}/${productCodes.length}: ${productCode}`);

      try {
        const analysis = await this.analyzer.analyzeProduct(this.basePath, productCode);
        products.push(analysis);

        // Update statistics
        const type = analysis.productType;
        productsByType[type] = (productsByType[type] || 0) + 1;

        totalVertices += analysis.metadata.totalVertices;
        totalFaces += analysis.metadata.totalFaces;
        totalFileSize += analysis.metadata.fileSize;
        totalDesignAreas += analysis.designAreas.length;

        if (analysis.metadata.hasCompression) compressionCount++;
        if (analysis.metadata.hasKTX2Textures) ktx2Count++;

      } catch (error) {
        console.warn(`⚠️ Failed to analyze product ${productCode}:`, error);
      }
    }

    const catalog: ProductCatalog = {
      totalProducts: products.length,
      productsByType,
      products,
      lastUpdated: new Date(),
      statistics: {
        totalVertices,
        totalFaces,
        totalFileSize,
        averageDesignAreas: products.length > 0 ? totalDesignAreas / products.length : 0,
        compressionUsage: products.length > 0 ? (compressionCount / products.length) * 100 : 0,
        ktx2Usage: products.length > 0 ? (ktx2Count / products.length) * 100 : 0
      }
    };

    console.log(`✅ Catalog complete!`, {
      totalProducts: catalog.totalProducts,
      productTypes: Object.keys(productsByType).length,
      averageDesignAreas: catalog.statistics.averageDesignAreas.toFixed(1),
      compressionUsage: `${catalog.statistics.compressionUsage.toFixed(1)}%`,
      ktx2Usage: `${catalog.statistics.ktx2Usage.toFixed(1)}%`
    });

    return catalog;
  }

  /**
   * Save catalog to JSON file
   */
  async saveCatalog(catalog: ProductCatalog, outputPath: string): Promise<void> {
    try {
      // Create a serializable version (remove Three.js objects)
      const serializableCatalog = {
        ...catalog,
        products: catalog.products.map(product => ({
          ...product,
          designAreas: product.designAreas.map(area => ({
            name: area.name,
            uvCoordinates: Array.from(area.uvCoordinates),
            boundingBox: {
              min: area.boundingBox.min.toArray(),
              max: area.boundingBox.max.toArray()
            },
            center: area.center.toArray()
          }))
        }))
      };

      await fs.promises.writeFile(
        outputPath,
        JSON.stringify(serializableCatalog, null, 2),
        'utf8'
      );

      console.log(`💾 Catalog saved to: ${outputPath}`);
    } catch (error) {
      console.error(`❌ Error saving catalog:`, error);
    }
  }

  /**
   * Load catalog from JSON file
   */
  async loadCatalog(inputPath: string): Promise<ProductCatalog | null> {
    try {
      const data = await fs.promises.readFile(inputPath, 'utf8');
      const catalog = JSON.parse(data);
      console.log(`📂 Catalog loaded from: ${inputPath}`);
      return catalog;
    } catch (error) {
      console.error(`❌ Error loading catalog:`, error);
      return null;
    }
  }

  /**
   * Dispose of resources
   */
  dispose(): void {
    this.analyzer.dispose();
  }
}

export default ProductCatalogBuilder;