/**
 * Fabric-Three.js Bridge
 * Synchronizes design elements between Fabric.js 2D canvas and Three.js 3D space
 */

import { fabric } from 'fabric';
import * as THREE from 'three';
import { ThreeDElementMapper, Element3DPosition } from './3d-element-mapping';
import { ThreeDRaycaster, RaycastHit } from './3d-raycasting';
import { DesignAreaUV } from './uv-extractor';

export interface DesignElement {
  id: string;
  type: 'text' | 'image' | 'shape';
  x: number;
  y: number;
  width: number;
  height: number;
  rotation: number;
  zIndex: number;
  data: any;
}

export class FabricThreeJSBridge {
  private fabricCanvas: fabric.Canvas;
  private elementMapper: ThreeDElementMapper;
  private raycaster: ThreeDRaycaster;
  private designAreas: Map<string, DesignAreaUV> = new Map();
  private currentDesignArea: DesignAreaUV | null = null;
  
  // Element tracking
  private fabricObjects: Map<string, fabric.Object> = new Map();
  private elementData: Map<string, DesignElement> = new Map();
  
  // Sync state
  private isSyncing = false;
  private syncCallbacks: {
    onElementUpdate?: (elementId: string, element: DesignElement) => void;
    onElementSelect?: (elementId: string | null) => void;
    onCanvasUpdate?: (canvas: HTMLCanvasElement) => void;
  } = {};

  constructor(
    fabricCanvas: fabric.Canvas,
    elementMapper: ThreeDElementMapper,
    raycaster: ThreeDRaycaster
  ) {
    this.fabricCanvas = fabricCanvas;
    this.elementMapper = elementMapper;
    this.raycaster = raycaster;

    this.setupFabricEventListeners();
    this.setupRaycastCallbacks();
  }

  /**
   * Setup Fabric.js event listeners
   */
  private setupFabricEventListeners(): void {
    // Object modification events
    this.fabricCanvas.on('object:modified', this.handleFabricObjectModified.bind(this));
    this.fabricCanvas.on('object:moving', this.handleFabricObjectMoving.bind(this));
    this.fabricCanvas.on('object:scaling', this.handleFabricObjectScaling.bind(this));
    this.fabricCanvas.on('object:rotating', this.handleFabricObjectRotating.bind(this));
    
    // Selection events
    this.fabricCanvas.on('selection:created', this.handleFabricSelectionCreated.bind(this));
    this.fabricCanvas.on('selection:updated', this.handleFabricSelectionUpdated.bind(this));
    this.fabricCanvas.on('selection:cleared', this.handleFabricSelectionCleared.bind(this));
    
    // Canvas events
    this.fabricCanvas.on('after:render', this.handleFabricAfterRender.bind(this));
  }

  /**
   * Setup raycaster callbacks for 3D interactions
   */
  private setupRaycastCallbacks(): void {
    this.raycaster.setCallbacks({
      onElementClick: this.handle3DElementClick.bind(this),
      onElementDragStart: this.handle3DElementDragStart.bind(this),
      onElementDrag: this.handle3DElementDrag.bind(this),
      onElementDragEnd: this.handle3DElementDragEnd.bind(this),
      onMeshClick: this.handle3DMeshClick.bind(this)
    });
  }

  /**
   * Register a design area
   */
  registerDesignArea(designArea: DesignAreaUV): void {
    this.designAreas.set(designArea.areaName, designArea);
    this.elementMapper.registerDesignArea(designArea);
  }

  /**
   * Set the current active design area
   */
  setCurrentDesignArea(areaName: string): void {
    const designArea = this.designAreas.get(areaName);
    if (designArea) {
      this.currentDesignArea = designArea;
      console.log(`🌉 Bridge: Set current design area to ${areaName}`);
    }
  }

  /**
   * Add a design element to both Fabric and 3D space
   */
  addElement(element: DesignElement): void {
    console.log(`🌉 Bridge: Adding element ${element.id}`, element);
    
    // Store element data
    this.elementData.set(element.id, element);
    
    // Create Fabric object
    const fabricObject = this.createFabricObject(element);
    if (fabricObject) {
      (fabricObject as any).elementId = element.id;
      this.fabricObjects.set(element.id, fabricObject);
      this.fabricCanvas.add(fabricObject);
    }
    
    // Map to 3D space if we have a current design area
    if (this.currentDesignArea) {
      this.mapElementTo3D(element);
    }
    
    this.fabricCanvas.renderAll();
    this.triggerCanvasUpdate();
  }

  /**
   * Create a Fabric.js object from a design element
   */
  private createFabricObject(element: DesignElement): fabric.Object | null {
    let fabricObject: fabric.Object | null = null;

    switch (element.type) {
      case 'text':
        fabricObject = new fabric.IText(element.data.text || 'Text', {
          left: element.x,
          top: element.y,
          fontSize: element.data.fontSize || 24,
          fill: element.data.color || '#000000',
          fontFamily: element.data.fontFamily || 'Arial',
          angle: element.rotation,
          originX: 'left',
          originY: 'top'
        });
        break;

      case 'image':
        if (element.data.src) {
          fabric.Image.fromURL(element.data.src, (img) => {
            img.set({
              left: element.x,
              top: element.y,
              scaleX: element.width / (img.width || 1),
              scaleY: element.height / (img.height || 1),
              angle: element.rotation,
              originX: 'left',
              originY: 'top'
            });
            (img as any).elementId = element.id;
            this.fabricObjects.set(element.id, img);
            this.fabricCanvas.add(img);
            this.fabricCanvas.renderAll();
            this.triggerCanvasUpdate();
          });
        }
        break;

      case 'shape':
        if (element.data.shape === 'rectangle') {
          fabricObject = new fabric.Rect({
            left: element.x,
            top: element.y,
            width: element.width,
            height: element.height,
            fill: element.data.color || '#000000',
            stroke: element.data.borderColor || '#000000',
            strokeWidth: element.data.borderWidth || 0,
            angle: element.rotation,
            originX: 'left',
            originY: 'top'
          });
        } else if (element.data.shape === 'circle') {
          fabricObject = new fabric.Circle({
            left: element.x,
            top: element.y,
            radius: Math.min(element.width, element.height) / 2,
            fill: element.data.color || '#000000',
            stroke: element.data.borderColor || '#000000',
            strokeWidth: element.data.borderWidth || 0,
            angle: element.rotation,
            originX: 'left',
            originY: 'top'
          });
        }
        break;
    }

    return fabricObject;
  }

  /**
   * Map an element to 3D space
   */
  private mapElementTo3D(element: DesignElement): void {
    if (!this.currentDesignArea) return;

    const canvasWidth = this.fabricCanvas.getWidth();
    const canvasHeight = this.fabricCanvas.getHeight();

    this.elementMapper.mapElementTo3D(
      element.id,
      this.currentDesignArea.areaName,
      element.x + element.width / 2, // Use center position
      element.y + element.height / 2,
      canvasWidth,
      canvasHeight,
      element.width,
      element.height,
      element.rotation
    );
  }

  /**
   * Update an element in both Fabric and 3D space
   */
  updateElement(elementId: string, updates: Partial<DesignElement>): void {
    const element = this.elementData.get(elementId);
    const fabricObject = this.fabricObjects.get(elementId);
    
    if (!element || !fabricObject) return;

    this.isSyncing = true;

    // Update element data
    const updatedElement = { ...element, ...updates };
    this.elementData.set(elementId, updatedElement);

    // Update Fabric object
    this.updateFabricObject(fabricObject, updatedElement);

    // Update 3D mapping
    if (this.currentDesignArea) {
      this.mapElementTo3D(updatedElement);
    }

    this.fabricCanvas.renderAll();
    this.triggerCanvasUpdate();
    
    this.isSyncing = false;

    // Trigger callback
    if (this.syncCallbacks.onElementUpdate) {
      this.syncCallbacks.onElementUpdate(elementId, updatedElement);
    }
  }

  /**
   * Update a Fabric object with element data
   */
  private updateFabricObject(fabricObject: fabric.Object, element: DesignElement): void {
    fabricObject.set({
      left: element.x,
      top: element.y,
      angle: element.rotation
    });

    if (element.type === 'text' && fabricObject instanceof fabric.IText) {
      fabricObject.set({
        text: element.data.text,
        fontSize: element.data.fontSize,
        fill: element.data.color,
        fontFamily: element.data.fontFamily
      });
    } else if (element.type === 'image' && fabricObject instanceof fabric.Image) {
      fabricObject.set({
        scaleX: element.width / (fabricObject.width || 1),
        scaleY: element.height / (fabricObject.height || 1)
      });
    } else if (element.type === 'shape') {
      fabricObject.set({
        width: element.width,
        height: element.height,
        fill: element.data.color,
        stroke: element.data.borderColor,
        strokeWidth: element.data.borderWidth
      });
    }
  }

  /**
   * Remove an element from both Fabric and 3D space
   */
  removeElement(elementId: string): void {
    const fabricObject = this.fabricObjects.get(elementId);
    
    if (fabricObject) {
      this.fabricCanvas.remove(fabricObject);
      this.fabricObjects.delete(elementId);
    }
    
    this.elementData.delete(elementId);
    this.elementMapper.removeElement(elementId);
    
    this.fabricCanvas.renderAll();
    this.triggerCanvasUpdate();
  }

  /**
   * Handle Fabric object modification
   */
  private handleFabricObjectModified(event: fabric.IEvent): void {
    if (this.isSyncing) return;
    
    const fabricObject = event.target;
    const elementId = (fabricObject as any)?.elementId as string;
    
    if (elementId && fabricObject) {
      this.syncFabricToElement(elementId, fabricObject);
    }
  }

  /**
   * Handle Fabric object moving
   */
  private handleFabricObjectMoving(event: fabric.IEvent): void {
    if (this.isSyncing) return;
    
    const fabricObject = event.target;
    const elementId = (fabricObject as any)?.elementId as string;
    
    if (elementId && fabricObject) {
      this.syncFabricToElement(elementId, fabricObject);
    }
  }

  /**
   * Handle Fabric object scaling
   */
  private handleFabricObjectScaling(event: fabric.IEvent): void {
    if (this.isSyncing) return;
    
    const fabricObject = event.target;
    const elementId = (fabricObject as any)?.elementId as string;

    if (elementId && fabricObject) {
      this.syncFabricToElement(elementId, fabricObject);
    }
  }

  /**
   * Handle Fabric object rotating
   */
  private handleFabricObjectRotating(event: fabric.IEvent): void {
    if (this.isSyncing) return;
    
    const fabricObject = event.target;
    const elementId = (fabricObject as any)?.elementId as string;

    if (elementId && fabricObject) {
      this.syncFabricToElement(elementId, fabricObject);
    }
  }

  /**
   * Sync Fabric object changes to element data
   */
  private syncFabricToElement(elementId: string, fabricObject: fabric.Object): void {
    const element = this.elementData.get(elementId);
    if (!element) return;

    const updates: Partial<DesignElement> = {
      x: fabricObject.left || 0,
      y: fabricObject.top || 0,
      rotation: fabricObject.angle || 0
    };

    // Handle scaling for images and shapes
    if (element.type === 'image' || element.type === 'shape') {
      updates.width = (fabricObject.width || 0) * (fabricObject.scaleX || 1);
      updates.height = (fabricObject.height || 0) * (fabricObject.scaleY || 1);
    }

    // Handle text changes
    if (element.type === 'text' && fabricObject instanceof fabric.IText) {
      updates.data = {
        ...element.data,
        text: fabricObject.text,
        fontSize: fabricObject.fontSize,
        color: fabricObject.fill,
        fontFamily: fabricObject.fontFamily
      };
    }

    this.updateElement(elementId, updates);
  }

  /**
   * Handle Fabric selection events
   */
  private handleFabricSelectionCreated(event: fabric.IEvent): void {
    const activeObject = event.selected?.[0];
    const elementId = (activeObject as any)?.elementId as string;
    
    if (elementId && this.syncCallbacks.onElementSelect) {
      this.syncCallbacks.onElementSelect(elementId);
    }
  }

  private handleFabricSelectionUpdated(event: fabric.IEvent): void {
    const activeObject = event.selected?.[0];
    const elementId = (activeObject as any)?.elementId as string;
    
    if (elementId && this.syncCallbacks.onElementSelect) {
      this.syncCallbacks.onElementSelect(elementId);
    }
  }

  private handleFabricSelectionCleared(): void {
    if (this.syncCallbacks.onElementSelect) {
      this.syncCallbacks.onElementSelect(null);
    }
  }

  /**
   * Handle Fabric after render
   */
  private handleFabricAfterRender(): void {
    this.triggerCanvasUpdate();
  }

  /**
   * Handle 3D element interactions
   */
  private handle3DElementClick(elementId: string, hit: RaycastHit): void {
    const fabricObject = this.fabricObjects.get(elementId);
    if (fabricObject) {
      this.fabricCanvas.setActiveObject(fabricObject);
      this.fabricCanvas.renderAll();
    }
  }

  private handle3DElementDragStart(elementId: string, hit: RaycastHit): void {
    console.log(`🌉 Bridge: 3D drag start for element ${elementId}`);
  }

  private handle3DElementDrag(elementId: string, deltaX: number, deltaY: number): void {
    const fabricObject = this.fabricObjects.get(elementId);
    if (fabricObject) {
      // Convert screen delta to canvas delta
      const canvasDeltaX = deltaX;
      const canvasDeltaY = deltaY;
      
      fabricObject.set({
        left: (fabricObject.left || 0) + canvasDeltaX,
        top: (fabricObject.top || 0) + canvasDeltaY
      });
      
      this.fabricCanvas.renderAll();
      this.syncFabricToElement(elementId, fabricObject);
    }
  }

  private handle3DElementDragEnd(elementId: string): void {
    console.log(`🌉 Bridge: 3D drag end for element ${elementId}`);
  }

  private handle3DMeshClick(meshName: string, worldPosition: THREE.Vector3): void {
    console.log(`🌉 Bridge: 3D mesh click on ${meshName}`, worldPosition);
  }

  /**
   * Trigger canvas update callback
   */
  private triggerCanvasUpdate(): void {
    if (this.syncCallbacks.onCanvasUpdate) {
      const canvasElement = this.fabricCanvas.getElement();
      this.syncCallbacks.onCanvasUpdate(canvasElement);
    }
  }

  /**
   * Set sync callbacks
   */
  setSyncCallbacks(callbacks: {
    onElementUpdate?: (elementId: string, element: DesignElement) => void;
    onElementSelect?: (elementId: string | null) => void;
    onCanvasUpdate?: (canvas: HTMLCanvasElement) => void;
  }): void {
    this.syncCallbacks = callbacks;
  }

  /**
   * Get all elements
   */
  getAllElements(): DesignElement[] {
    return Array.from(this.elementData.values());
  }

  /**
   * Get element by ID
   */
  getElement(elementId: string): DesignElement | null {
    return this.elementData.get(elementId) || null;
  }

  /**
   * Clear all elements
   */
  clear(): void {
    this.fabricCanvas.clear();
    this.fabricObjects.clear();
    this.elementData.clear();
    this.elementMapper.clear();
  }

  /**
   * Dispose of the bridge
   */
  dispose(): void {
    this.fabricCanvas.off();
    this.clear();
  }
}
