/**
 * 3D Hover Effects
 * Provides visual feedback for hovering over design elements in 3D space
 */

import * as THREE from 'three';
import { Element3DPosition, InteractionState } from './3d-element-mapping';

export interface HoverEffectConfig {
  glowColor: number;
  glowIntensity: number;
  pulseSpeed: number;
  scaleMultiplier: number;
  animationDuration: number;
}

export interface HoverAnimation {
  elementId: string;
  startTime: number;
  duration: number;
  type: 'glow' | 'pulse' | 'scale' | 'outline';
  progress: number;
}

export class ThreeDHoverEffects {
  private scene: THREE.Scene;
  private clock: THREE.Clock;
  private hoverEffects: Map<string, THREE.Group> = new Map();
  private animations: Map<string, HoverAnimation> = new Map();
  private animationFrame: number | null = null;
  
  // Effect configuration
  private config: HoverEffectConfig = {
    glowColor: 0x00ff88,
    glowIntensity: 0.4,
    pulseSpeed: 2.0,
    scaleMultiplier: 1.05,
    animationDuration: 300 // milliseconds
  };

  constructor(scene: THREE.Scene) {
    this.scene = scene;
    this.clock = new THREE.Clock();
    this.startAnimationLoop();
  }

  /**
   * Start the animation loop
   */
  private startAnimationLoop(): void {
    const animate = () => {
      this.updateAnimations();
      this.animationFrame = requestAnimationFrame(animate);
    };
    animate();
  }

  /**
   * Update hover effect for an element
   */
  updateHoverEffect(
    elementId: string,
    position: Element3DPosition,
    interactionState: InteractionState
  ): void {
    if (interactionState.isHovered && !interactionState.isSelected) {
      this.showHoverEffect(elementId, position);
    } else {
      this.hideHoverEffect(elementId);
    }
  }

  /**
   * Show hover effect for an element
   */
  private showHoverEffect(elementId: string, position: Element3DPosition): void {
    // Remove existing effect
    this.removeHoverEffect(elementId);

    // Create new hover effect
    const hoverGroup = this.createHoverEffect(position);
    this.hoverEffects.set(elementId, hoverGroup);
    this.scene.add(hoverGroup);

    // Start animation
    this.startAnimation(elementId, 'glow');

    console.log(`✨ Hover effect shown for element ${elementId}`);
  }

  /**
   * Hide hover effect for an element
   */
  private hideHoverEffect(elementId: string): void {
    const animation = this.animations.get(elementId);
    if (animation) {
      // Start fade out animation
      this.startAnimation(elementId, 'glow', true);
    } else {
      // Remove immediately if no animation
      this.removeHoverEffect(elementId);
    }
  }

  /**
   * Create hover effect visual elements
   */
  private createHoverEffect(position: Element3DPosition): THREE.Group {
    const group = new THREE.Group();
    
    // Calculate element bounds
    const bounds = this.calculateWorldBounds(position);
    
    // Create glow effect
    const glowEffect = this.createGlowEffect(bounds);
    group.add(glowEffect);
    
    // Create outline effect
    const outlineEffect = this.createOutlineEffect(bounds);
    group.add(outlineEffect);
    
    // Create pulse effect
    const pulseEffect = this.createPulseEffect(bounds);
    group.add(pulseEffect);
    
    // Position the group
    group.position.copy(position.worldPosition);
    group.lookAt(
      position.worldPosition.clone().add(position.surfaceNormal)
    );
    
    return group;
  }

  /**
   * Calculate world bounds for an element
   */
  private calculateWorldBounds(position: Element3DPosition): {
    width: number;
    height: number;
    depth: number;
  } {
    const uvBounds = position.bounds;
    const uvWidth = uvBounds.max.u - uvBounds.min.u;
    const uvHeight = uvBounds.max.v - uvBounds.min.v;
    
    const scaleFactor = 0.5;
    
    return {
      width: uvWidth * scaleFactor,
      height: uvHeight * scaleFactor,
      depth: 0.001
    };
  }

  /**
   * Create glow effect
   */
  private createGlowEffect(bounds: { width: number; height: number }): THREE.Mesh {
    const geometry = new THREE.PlaneGeometry(
      bounds.width * 1.2,
      bounds.height * 1.2
    );
    
    const material = new THREE.MeshBasicMaterial({
      color: this.config.glowColor,
      opacity: 0,
      transparent: true,
      side: THREE.DoubleSide,
      blending: THREE.AdditiveBlending
    });
    
    const mesh = new THREE.Mesh(geometry, material);
    mesh.userData = { effectType: 'glow' };
    
    return mesh;
  }

  /**
   * Create outline effect
   */
  private createOutlineEffect(bounds: { width: number; height: number }): THREE.LineSegments {
    const geometry = new THREE.PlaneGeometry(bounds.width, bounds.height);
    const wireframeGeometry = new THREE.WireframeGeometry(geometry);
    
    const material = new THREE.LineBasicMaterial({
      color: this.config.glowColor,
      opacity: 0,
      transparent: true,
      linewidth: 2
    });
    
    const outline = new THREE.LineSegments(wireframeGeometry, material);
    outline.userData = { effectType: 'outline' };
    
    return outline;
  }

  /**
   * Create pulse effect
   */
  private createPulseEffect(bounds: { width: number; height: number }): THREE.Mesh {
    const geometry = new THREE.RingGeometry(
      Math.max(bounds.width, bounds.height) * 0.3,
      Math.max(bounds.width, bounds.height) * 0.4,
      16
    );
    
    const material = new THREE.MeshBasicMaterial({
      color: this.config.glowColor,
      opacity: 0,
      transparent: true,
      side: THREE.DoubleSide
    });
    
    const mesh = new THREE.Mesh(geometry, material);
    mesh.userData = { effectType: 'pulse' };
    
    return mesh;
  }

  /**
   * Start animation for an element
   */
  private startAnimation(
    elementId: string, 
    type: 'glow' | 'pulse' | 'scale' | 'outline',
    reverse: boolean = false
  ): void {
    const animation: HoverAnimation = {
      elementId,
      startTime: this.clock.getElapsedTime(),
      duration: this.config.animationDuration / 1000, // Convert to seconds
      type,
      progress: reverse ? 1 : 0
    };
    
    this.animations.set(elementId, animation);
  }

  /**
   * Update all animations
   */
  private updateAnimations(): void {
    const currentTime = this.clock.getElapsedTime();
    const completedAnimations: string[] = [];
    
    this.animations.forEach((animation, elementId) => {
      const elapsed = currentTime - animation.startTime;
      const progress = Math.min(elapsed / animation.duration, 1);
      
      animation.progress = progress;
      
      // Update visual effects based on animation progress
      this.updateAnimationVisuals(elementId, animation);
      
      // Check if animation is complete
      if (progress >= 1) {
        completedAnimations.push(elementId);
      }
    });
    
    // Clean up completed animations
    completedAnimations.forEach(elementId => {
      const animation = this.animations.get(elementId);
      if (animation && animation.progress >= 1) {
        // If it was a fade out animation, remove the effect
        if (animation.type === 'glow' && animation.progress === 1) {
          this.removeHoverEffect(elementId);
        }
        this.animations.delete(elementId);
      }
    });
  }

  /**
   * Update animation visuals
   */
  private updateAnimationVisuals(elementId: string, animation: HoverAnimation): void {
    const hoverGroup = this.hoverEffects.get(elementId);
    if (!hoverGroup) return;
    
    const easeProgress = this.easeInOutCubic(animation.progress);
    
    hoverGroup.children.forEach(child => {
      const effectType = child.userData.effectType;
      
      switch (effectType) {
        case 'glow':
          this.updateGlowEffect(child as THREE.Mesh, easeProgress);
          break;
        case 'outline':
          this.updateOutlineEffect(child as THREE.LineSegments, easeProgress);
          break;
        case 'pulse':
          this.updatePulseEffect(child as THREE.Mesh, easeProgress, animation.startTime);
          break;
      }
    });
  }

  /**
   * Update glow effect
   */
  private updateGlowEffect(mesh: THREE.Mesh, progress: number): void {
    const material = mesh.material as THREE.MeshBasicMaterial;
    material.opacity = progress * this.config.glowIntensity;
    
    // Scale effect
    const scale = 1 + (progress * 0.1);
    mesh.scale.setScalar(scale);
  }

  /**
   * Update outline effect
   */
  private updateOutlineEffect(outline: THREE.LineSegments, progress: number): void {
    const material = outline.material as THREE.LineBasicMaterial;
    material.opacity = progress * 0.8;
  }

  /**
   * Update pulse effect
   */
  private updatePulseEffect(mesh: THREE.Mesh, progress: number, startTime: number): void {
    const material = mesh.material as THREE.MeshBasicMaterial;
    
    // Pulsing opacity
    const pulseTime = (this.clock.getElapsedTime() - startTime) * this.config.pulseSpeed;
    const pulseOpacity = (Math.sin(pulseTime) + 1) * 0.5;
    
    material.opacity = progress * pulseOpacity * 0.3;
    
    // Pulsing scale
    const pulseScale = 1 + (pulseOpacity * 0.1);
    mesh.scale.setScalar(pulseScale);
  }

  /**
   * Easing function for smooth animations
   */
  private easeInOutCubic(t: number): number {
    return t < 0.5 ? 4 * t * t * t : 1 - Math.pow(-2 * t + 2, 3) / 2;
  }

  /**
   * Remove hover effect
   */
  removeHoverEffect(elementId: string): void {
    const hoverGroup = this.hoverEffects.get(elementId);
    if (hoverGroup) {
      this.scene.remove(hoverGroup);
      this.hoverEffects.delete(elementId);
    }
    
    this.animations.delete(elementId);
  }

  /**
   * Update hover effect configuration
   */
  updateConfig(config: Partial<HoverEffectConfig>): void {
    Object.assign(this.config, config);
  }

  /**
   * Set hover effect visibility
   */
  setVisible(visible: boolean): void {
    this.hoverEffects.forEach(group => {
      group.visible = visible;
    });
  }

  /**
   * Clear all hover effects
   */
  clear(): void {
    this.hoverEffects.forEach(group => {
      this.scene.remove(group);
    });
    
    this.hoverEffects.clear();
    this.animations.clear();
  }

  /**
   * Dispose of hover effects
   */
  dispose(): void {
    if (this.animationFrame) {
      cancelAnimationFrame(this.animationFrame);
      this.animationFrame = null;
    }
    
    this.clear();
  }

  /**
   * Get hover effect for element (for debugging)
   */
  getHoverEffect(elementId: string): THREE.Group | null {
    return this.hoverEffects.get(elementId) || null;
  }

  /**
   * Check if element has active hover effect
   */
  hasHoverEffect(elementId: string): boolean {
    return this.hoverEffects.has(elementId);
  }

  /**
   * Trigger immediate hover effect (for testing)
   */
  triggerHoverEffect(elementId: string, position: Element3DPosition): void {
    this.showHoverEffect(elementId, position);
  }

  /**
   * Get current animation state (for debugging)
   */
  getAnimationState(): { [elementId: string]: HoverAnimation } {
    const state: { [elementId: string]: HoverAnimation } = {};
    this.animations.forEach((animation, elementId) => {
      state[elementId] = { ...animation };
    });
    return state;
  }
}
