/**
 * 3D Element Mapping System
 * Maps design elements to their 3D positions, UV coordinates, and mesh surfaces
 */

import * as THREE from 'three';
import { DesignAreaUV } from './uv-extractor';

export interface Element3DPosition {
  elementId: string;
  meshName: string;
  uvCoordinates: { u: number; v: number };
  worldPosition: THREE.Vector3;
  surfaceNormal: THREE.Vector3;
  bounds: {
    min: { u: number; v: number };
    max: { u: number; v: number };
  };
  transform: {
    rotation: number;
    scaleX: number;
    scaleY: number;
  };
}

export interface InteractionState {
  isHovered: boolean;
  isSelected: boolean;
  isDragging: boolean;
  isTransforming: boolean;
  transformType?: 'move' | 'rotate' | 'scale' | 'resize';
}

export class ThreeDElementMapper {
  private elementPositions: Map<string, Element3DPosition> = new Map();
  private interactionStates: Map<string, InteractionState> = new Map();
  private designAreas: Map<string, DesignAreaUV> = new Map();
  private raycaster: THREE.Raycaster;
  private mouse: THREE.Vector2;

  constructor() {
    this.raycaster = new THREE.Raycaster();
    this.mouse = new THREE.Vector2();
  }

  /**
   * Register a design area for element mapping
   */
  registerDesignArea(designArea: DesignAreaUV): void {
    this.designAreas.set(designArea.areaName, designArea);
    console.log(`🗺️ Registered design area: ${designArea.areaName}`);
  }

  /**
   * Map a design element to 3D space
   */
  mapElementTo3D(
    elementId: string,
    meshName: string,
    canvasX: number,
    canvasY: number,
    canvasWidth: number,
    canvasHeight: number,
    elementWidth: number,
    elementHeight: number,
    rotation: number = 0
  ): Element3DPosition | null {
    const designArea = this.designAreas.get(meshName);
    if (!designArea) {
      console.warn(`🗺️ Design area not found: ${meshName}`);
      return null;
    }

    // Convert canvas coordinates to UV coordinates
    const uvCoords = this.canvasToUV(canvasX, canvasY, canvasWidth, canvasHeight, designArea);
    
    // Calculate world position from UV coordinates
    const worldPosition = this.uvToWorldPosition(uvCoords.u, uvCoords.v, designArea);
    
    // Calculate surface normal at this position
    const surfaceNormal = this.calculateSurfaceNormal(uvCoords.u, uvCoords.v, designArea);
    
    // Calculate element bounds in UV space
    const elementBounds = this.calculateElementBounds(
      uvCoords.u, uvCoords.v, 
      elementWidth, elementHeight, 
      canvasWidth, canvasHeight, 
      designArea
    );

    const position: Element3DPosition = {
      elementId,
      meshName,
      uvCoordinates: uvCoords,
      worldPosition,
      surfaceNormal,
      bounds: elementBounds,
      transform: {
        rotation,
        scaleX: 1,
        scaleY: 1
      }
    };

    this.elementPositions.set(elementId, position);
    this.initializeInteractionState(elementId);

    console.log(`🗺️ Mapped element ${elementId} to 3D:`, position);
    return position;
  }

  /**
   * Convert canvas coordinates to UV coordinates
   */
  private canvasToUV(
    canvasX: number, 
    canvasY: number, 
    canvasWidth: number, 
    canvasHeight: number, 
    designArea: DesignAreaUV
  ): { u: number; v: number } {
    // Normalize canvas coordinates (0-1)
    const normalizedX = canvasX / canvasWidth;
    const normalizedY = canvasY / canvasHeight;

    // Map to UV space using the design area's UV bounds
    const uvBounds = designArea.mapping.boundingBox;
    const u = uvBounds.min[0] + (normalizedX * (uvBounds.max[0] - uvBounds.min[0]));
    const v = uvBounds.min[1] + (normalizedY * (uvBounds.max[1] - uvBounds.min[1]));

    return { u, v };
  }

  /**
   * Convert UV coordinates to world position
   */
  private uvToWorldPosition(u: number, v: number, designArea: DesignAreaUV): THREE.Vector3 {
    const geometry = designArea.mesh.geometry;
    const uvAttribute = geometry.attributes.uv;
    const positionAttribute = geometry.attributes.position;

    // Find the closest UV coordinate in the mesh
    let closestDistance = Infinity;
    let closestIndex = 0;

    for (let i = 0; i < uvAttribute.count; i++) {
      const meshU = uvAttribute.getX(i);
      const meshV = uvAttribute.getY(i);
      const distance = Math.sqrt((u - meshU) ** 2 + (v - meshV) ** 2);
      
      if (distance < closestDistance) {
        closestDistance = distance;
        closestIndex = i;
      }
    }

    // Get world position from the closest vertex
    const localPosition = new THREE.Vector3(
      positionAttribute.getX(closestIndex),
      positionAttribute.getY(closestIndex),
      positionAttribute.getZ(closestIndex)
    );

    // Transform to world coordinates
    const worldPosition = localPosition.clone();
    designArea.mesh.localToWorld(worldPosition);

    return worldPosition;
  }

  /**
   * Calculate surface normal at UV position
   */
  private calculateSurfaceNormal(u: number, v: number, designArea: DesignAreaUV): THREE.Vector3 {
    const geometry = designArea.mesh.geometry;
    
    // For simplicity, use the mesh's general normal
    // In a more advanced implementation, we'd interpolate between vertex normals
    const normalAttribute = geometry.attributes.normal;
    if (normalAttribute) {
      const normal = new THREE.Vector3(
        normalAttribute.getX(0),
        normalAttribute.getY(0),
        normalAttribute.getZ(0)
      );
      
      // Transform to world space
      const worldNormal = normal.clone();
      designArea.mesh.localToWorld(worldNormal);
      worldNormal.sub(designArea.mesh.position).normalize();
      
      return worldNormal;
    }

    return new THREE.Vector3(0, 0, 1); // Default normal
  }

  /**
   * Calculate element bounds in UV space
   */
  private calculateElementBounds(
    centerU: number, 
    centerV: number, 
    elementWidth: number, 
    elementHeight: number,
    canvasWidth: number,
    canvasHeight: number,
    designArea: DesignAreaUV
  ): { min: { u: number; v: number }; max: { u: number; v: number } } {
    const uvBounds = designArea.mapping.boundingBox;
    const uvWidth = uvBounds.max[0] - uvBounds.min[0];
    const uvHeight = uvBounds.max[1] - uvBounds.min[1];

    // Convert element size to UV space
    const elementUWidth = (elementWidth / canvasWidth) * uvWidth;
    const elementVHeight = (elementHeight / canvasHeight) * uvHeight;

    return {
      min: {
        u: centerU - elementUWidth / 2,
        v: centerV - elementVHeight / 2
      },
      max: {
        u: centerU + elementUWidth / 2,
        v: centerV + elementVHeight / 2
      }
    };
  }

  /**
   * Initialize interaction state for an element
   */
  private initializeInteractionState(elementId: string): void {
    this.interactionStates.set(elementId, {
      isHovered: false,
      isSelected: false,
      isDragging: false,
      isTransforming: false
    });
  }

  /**
   * Get element 3D position
   */
  getElementPosition(elementId: string): Element3DPosition | null {
    return this.elementPositions.get(elementId) || null;
  }

  /**
   * Update element position
   */
  updateElementPosition(elementId: string, updates: Partial<Element3DPosition>): void {
    const current = this.elementPositions.get(elementId);
    if (current) {
      this.elementPositions.set(elementId, { ...current, ...updates });
    }
  }

  /**
   * Get interaction state
   */
  getInteractionState(elementId: string): InteractionState | null {
    return this.interactionStates.get(elementId) || null;
  }

  /**
   * Update interaction state
   */
  updateInteractionState(elementId: string, updates: Partial<InteractionState>): void {
    const current = this.interactionStates.get(elementId);
    if (current) {
      this.interactionStates.set(elementId, { ...current, ...updates });
    }
  }

  /**
   * Remove element mapping
   */
  removeElement(elementId: string): void {
    this.elementPositions.delete(elementId);
    this.interactionStates.delete(elementId);
  }

  /**
   * Get all mapped elements for a mesh
   */
  getElementsForMesh(meshName: string): Element3DPosition[] {
    return Array.from(this.elementPositions.values())
      .filter(pos => pos.meshName === meshName);
  }

  /**
   * Clear all mappings
   */
  clear(): void {
    this.elementPositions.clear();
    this.interactionStates.clear();
  }
}
