export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  public: {
    Enums: {
      customization_type: "text" | "image" | "color" | "size" | "font" | "position"
      order_status: "pending" | "processing" | "shipped" | "delivered" | "cancelled" | "refunded"
      payment_status: "pending" | "paid" | "failed" | "refunded" | "partially_refunded"
      subscription_status: "active" | "inactive" | "cancelled" | "past_due" | "trialing"
      subscription_tier: "free" | "basic" | "pro" | "enterprise"
      ticket_priority: "low" | "medium" | "high" | "urgent"
      ticket_status: "open" | "in_progress" | "resolved" | "closed"
      user_role: "super_admin" | "merchant" | "customer"
    }
    Tables: {
      activity_logs: {
        Row: {
          id: string
          user_id: string | null
          merchant_id: string | null
          action: string
          resource_type: string
          resource_id: string | null
          details: Json | null
          ip_address: unknown
          user_agent: string | null
          created_at: string | null
        }
        Insert: {
          id?: string
          user_id?: string | null
          merchant_id?: string | null
          action: string
          resource_type: string
          resource_id?: string | null
          details?: Json | null
          ip_address?: unknown
          user_agent?: string | null
          created_at?: string | null
        }
        Update: {
          id?: string
          user_id?: string | null
          merchant_id?: string | null
          action?: string
          resource_type?: string
          resource_id?: string | null
          details?: Json | null
          ip_address?: unknown
          user_agent?: string | null
          created_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "activity_logs_merchant_id_fkey"
            columns: ["merchant_id"]
            isOneToOne: false
            referencedRelation: "merchants"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "activity_logs_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
      customization_options: {
        Row: {
          id: string
          product_id: string
          type: Database["public"]["Enums"]["customization_type"]
          name: string
          description: string | null
          required: boolean | null
          options: Json
          price_modifier: number | null
          max_length: number | null
          allowed_file_types: string[] | null
          position_constraints: Json | null
          sort_order: number | null
          is_active: boolean | null
          created_at: string | null
          updated_at: string | null
        }
        Insert: {
          id?: string
          product_id: string
          type: Database["public"]["Enums"]["customization_type"]
          name: string
          description?: string | null
          required?: boolean | null
          options?: Json
          price_modifier?: number | null
          max_length?: number | null
          allowed_file_types?: string[] | null
          position_constraints?: Json | null
          sort_order?: number | null
          is_active?: boolean | null
          created_at?: string | null
          updated_at?: string | null
        }
        Update: {
          id?: string
          product_id?: string
          type?: Database["public"]["Enums"]["customization_type"]
          name?: string
          description?: string | null
          required?: boolean | null
          options?: Json
          price_modifier?: number | null
          max_length?: number | null
          allowed_file_types?: string[] | null
          position_constraints?: Json | null
          sort_order?: number | null
          is_active?: boolean | null
          created_at?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "customization_options_product_id_fkey"
            columns: ["product_id"]
            isOneToOne: false
            referencedRelation: "products"
            referencedColumns: ["id"]
          }
        ]
      }
      designs: {
        Row: {
          id: string
          customer_email: string
          merchant_id: string
          product_id: string
          name: string
          design_data: Json
          preview_url: string | null
          thumbnail_url: string | null
          is_saved: boolean | null
          is_public: boolean | null
          created_at: string | null
          updated_at: string | null
        }
        Insert: {
          id?: string
          customer_email: string
          merchant_id: string
          product_id: string
          name: string
          design_data: Json
          preview_url?: string | null
          thumbnail_url?: string | null
          is_saved?: boolean | null
          is_public?: boolean | null
          created_at?: string | null
          updated_at?: string | null
        }
        Update: {
          id?: string
          customer_email?: string
          merchant_id?: string
          product_id?: string
          name?: string
          design_data?: Json
          preview_url?: string | null
          thumbnail_url?: string | null
          is_saved?: boolean | null
          is_public?: boolean | null
          created_at?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "designs_merchant_id_fkey"
            columns: ["merchant_id"]
            isOneToOne: false
            referencedRelation: "merchants"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "designs_product_id_fkey"
            columns: ["product_id"]
            isOneToOne: false
            referencedRelation: "products"
            referencedColumns: ["id"]
          }
        ]
      }
      merchant_products: {
        Row: {
          id: string
          merchant_id: string
          product_id: string
          shopify_product_id: string | null
          custom_price: number | null
          is_published: boolean | null
          published_at: string | null
          created_at: string | null
          updated_at: string | null
        }
        Insert: {
          id?: string
          merchant_id: string
          product_id: string
          shopify_product_id?: string | null
          custom_price?: number | null
          is_published?: boolean | null
          published_at?: string | null
          created_at?: string | null
          updated_at?: string | null
        }
        Update: {
          id?: string
          merchant_id?: string
          product_id?: string
          shopify_product_id?: string | null
          custom_price?: number | null
          is_published?: boolean | null
          published_at?: string | null
          created_at?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "merchant_products_merchant_id_fkey"
            columns: ["merchant_id"]
            isOneToOne: false
            referencedRelation: "merchants"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "merchant_products_product_id_fkey"
            columns: ["product_id"]
            isOneToOne: false
            referencedRelation: "products"
            referencedColumns: ["id"]
          }
        ]
      }
      merchants: {
        Row: {
          id: string
          user_id: string
          shop_domain: string
          shop_name: string
          access_token: string
          subscription_tier: Database["public"]["Enums"]["subscription_tier"]
          subscription_status: Database["public"]["Enums"]["subscription_status"]
          trial_ends_at: string | null
          billing_address: Json | null
          settings: Json | null
          webhook_url: string | null
          is_active: boolean | null
          created_at: string | null
          updated_at: string | null
        }
        Insert: {
          id?: string
          user_id: string
          shop_domain: string
          shop_name: string
          access_token: string
          subscription_tier?: Database["public"]["Enums"]["subscription_tier"]
          subscription_status?: Database["public"]["Enums"]["subscription_status"]
          trial_ends_at?: string | null
          billing_address?: Json | null
          settings?: Json | null
          webhook_url?: string | null
          is_active?: boolean | null
          created_at?: string | null
          updated_at?: string | null
        }
        Update: {
          id?: string
          user_id?: string
          shop_domain?: string
          shop_name?: string
          access_token?: string
          subscription_tier?: Database["public"]["Enums"]["subscription_tier"]
          subscription_status?: Database["public"]["Enums"]["subscription_status"]
          trial_ends_at?: string | null
          billing_address?: Json | null
          settings?: Json | null
          webhook_url?: string | null
          is_active?: boolean | null
          created_at?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "merchants_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
      order_items: {
        Row: {
          id: string
          order_id: string
          legacy_product_id: string
          design_id: string | null
          quantity: number
          legacy_unit_price: number
          total_price: number
          customization_data: Json | null
          production_files: Json | null
          sku: string | null
          created_at: string | null
          merchant_product_id: string | null
          product_variant_id: string | null
          shopify_line_item_id: string | null
          product_name: string
          variant_title: string | null
          price: number | null
          fulfillment_status: string | null
          updated_at: string | null
        }
        Insert: {
          id?: string
          order_id: string
          legacy_product_id: string
          design_id?: string | null
          quantity?: number
          legacy_unit_price: number
          total_price?: number
          customization_data?: Json | null
          production_files?: Json | null
          sku?: string | null
          created_at?: string | null
          merchant_product_id?: string | null
          product_variant_id?: string | null
          shopify_line_item_id?: string | null
          product_name: string
          variant_title?: string | null
          price?: number | null
          fulfillment_status?: string | null
          updated_at?: string | null
        }
        Update: {
          id?: string
          order_id?: string
          legacy_product_id?: string
          design_id?: string | null
          quantity?: number
          legacy_unit_price?: number
          total_price?: number
          customization_data?: Json | null
          production_files?: Json | null
          sku?: string | null
          created_at?: string | null
          merchant_product_id?: string | null
          product_variant_id?: string | null
          shopify_line_item_id?: string | null
          product_name?: string
          variant_title?: string | null
          price?: number | null
          fulfillment_status?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "order_items_design_id_fkey"
            columns: ["design_id"]
            isOneToOne: false
            referencedRelation: "designs"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "order_items_merchant_product_id_fkey"
            columns: ["merchant_product_id"]
            isOneToOne: false
            referencedRelation: "merchant_products"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "order_items_order_id_fkey"
            columns: ["order_id"]
            isOneToOne: false
            referencedRelation: "orders"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "order_items_product_id_fkey"
            columns: ["legacy_product_id"]
            isOneToOne: false
            referencedRelation: "products"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "fk_order_items_product_variant_id"
            columns: ["product_variant_id"]
            isOneToOne: false
            referencedRelation: "product_variants"
            referencedColumns: ["id"]
          }
        ]
      }
      orders: {
        Row: {
          id: string
          order_number: string
          merchant_id: string
          customer_email: string
          customer_name: string | null
          customer_phone: string | null
          total_amount: number
          subtotal: number
          tax_amount: number | null
          shipping_amount: number | null
          discount_amount: number | null
          status: Database["public"]["Enums"]["order_status"]
          payment_status: Database["public"]["Enums"]["payment_status"]
          payment_intent_id: string | null
          shipping_address: Json | null
          billing_address: Json | null
          tracking_number: string | null
          tracking_url: string | null
          notes: string | null
          shopify_order_id: string | null
          fulfillment_service: string | null
          created_at: string | null
          updated_at: string | null
        }
        Insert: {
          id?: string
          order_number: string
          merchant_id: string
          customer_email: string
          customer_name?: string | null
          customer_phone?: string | null
          total_amount: number
          subtotal: number
          tax_amount?: number | null
          shipping_amount?: number | null
          discount_amount?: number | null
          status?: Database["public"]["Enums"]["order_status"]
          payment_status?: Database["public"]["Enums"]["payment_status"]
          payment_intent_id?: string | null
          shipping_address?: Json | null
          billing_address?: Json | null
          tracking_number?: string | null
          tracking_url?: string | null
          notes?: string | null
          shopify_order_id?: string | null
          fulfillment_service?: string | null
          created_at?: string | null
          updated_at?: string | null
        }
        Update: {
          id?: string
          order_number?: string
          merchant_id?: string
          customer_email?: string
          customer_name?: string | null
          customer_phone?: string | null
          total_amount?: number
          subtotal?: number
          tax_amount?: number | null
          shipping_amount?: number | null
          discount_amount?: number | null
          status?: Database["public"]["Enums"]["order_status"]
          payment_status?: Database["public"]["Enums"]["payment_status"]
          payment_intent_id?: string | null
          shipping_address?: Json | null
          billing_address?: Json | null
          tracking_number?: string | null
          tracking_url?: string | null
          notes?: string | null
          shopify_order_id?: string | null
          fulfillment_service?: string | null
          created_at?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "orders_merchant_id_fkey"
            columns: ["merchant_id"]
            isOneToOne: false
            referencedRelation: "merchants"
            referencedColumns: ["id"]
          }
        ]
      }
      products: {
        Row: {
          base_price: number
          canvas_config: Json | null
          care_instructions: string | null
          category_id: string | null
          created_at: string | null
          description: string | null
          dimensions: Json | null
          featured: boolean | null
          glb_files: Json | null
          id: string
          is_active: boolean | null
          materials: string[] | null
          mockup_files: Json | null
          name: string
          preview_files: Json | null
          print_files: Json | null
          sku: string | null
          slug: string
          sort_order: number | null
          tags: string[] | null
          texture_files: Json | null
          updated_at: string | null
          weight: number | null
        }
        Insert: {
          base_price: number
          canvas_config?: Json | null
          care_instructions?: string | null
          category_id?: string | null
          created_at?: string | null
          description?: string | null
          dimensions?: Json | null
          featured?: boolean | null
          glb_files?: Json | null
          id?: string
          is_active?: boolean | null
          materials?: string[] | null
          mockup_files?: Json | null
          name: string
          preview_files?: Json | null
          print_files?: Json | null
          sku?: string | null
          slug: string
          sort_order?: number | null
          tags?: string[] | null
          texture_files?: Json | null
          updated_at?: string | null
          weight?: number | null
        }
        Update: {
          base_price?: number
          canvas_config?: Json | null
          care_instructions?: string | null
          category_id?: string | null
          created_at?: string | null
          description?: string | null
          dimensions?: Json | null
          featured?: boolean | null
          glb_files?: Json | null
          id?: string
          is_active?: boolean | null
          materials?: string[] | null
          mockup_files?: Json | null
          name?: string
          preview_files?: Json | null
          print_files?: Json | null
          sku?: string | null
          slug?: string
          sort_order?: number | null
          tags?: string[] | null
          texture_files?: Json | null
          updated_at?: string | null
          weight?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "products_category_id_fkey"
            columns: ["category_id"]
            isOneToOne: false
            referencedRelation: "product_categories"
            referencedColumns: ["id"]
          }
        ]
      }
      product_categories: {
        Row: {
          id: string
          name: string
          description: string | null
          slug: string
          image_url: string | null
          sort_order: number | null
          is_active: boolean | null
          created_at: string | null
          updated_at: string | null
        }
        Insert: {
          id?: string
          name: string
          description?: string | null
          slug: string
          image_url?: string | null
          sort_order?: number | null
          is_active?: boolean | null
          created_at?: string | null
          updated_at?: string | null
        }
        Update: {
          id?: string
          name?: string
          description?: string | null
          slug?: string
          image_url?: string | null
          sort_order?: number | null
          is_active?: boolean | null
          created_at?: string | null
          updated_at?: string | null
        }
        Relationships: []
      }
      product_images: {
        Row: {
          id: string
          product_id: string
          url: string
          alt_text: string | null
          is_primary: boolean | null
          sort_order: number | null
          created_at: string | null
        }
        Insert: {
          id?: string
          product_id: string
          url: string
          alt_text?: string | null
          is_primary?: boolean | null
          sort_order?: number | null
          created_at?: string | null
        }
        Update: {
          id?: string
          product_id?: string
          url?: string
          alt_text?: string | null
          is_primary?: boolean | null
          sort_order?: number | null
          created_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "product_images_product_id_fkey"
            columns: ["product_id"]
            isOneToOne: false
            referencedRelation: "products"
            referencedColumns: ["id"]
          }
        ]
      }
      product_mapping_areas: {
        Row: {
          id: string
          product_id: string
          area_id: string
          area_name: string
          view_type: string
          svg_path: string
          display_bounds: Json
          print_bounds: Json
          transform_matrix: Json | null
          sort_order: number | null
          is_active: boolean | null
          created_at: string | null
          updated_at: string | null
        }
        Insert: {
          id?: string
          product_id: string
          area_id: string
          area_name: string
          view_type: string
          svg_path: string
          display_bounds: Json
          print_bounds: Json
          transform_matrix?: Json | null
          sort_order?: number | null
          is_active?: boolean | null
          created_at?: string | null
          updated_at?: string | null
        }
        Update: {
          id?: string
          product_id?: string
          area_id?: string
          area_name?: string
          view_type?: string
          svg_path?: string
          display_bounds?: Json
          print_bounds?: Json
          transform_matrix?: Json | null
          sort_order?: number | null
          is_active?: boolean | null
          created_at?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "product_mapping_areas_product_id_fkey"
            columns: ["product_id"]
            isOneToOne: false
            referencedRelation: "products"
            referencedColumns: ["id"]
          }
        ]
      }
      product_variants: {
        Row: {
          id: string
          merchant_product_id: string
          shopify_variant_id: string | null
          title: string
          price: number
          compare_at_price: number | null
          sku: string | null
          barcode: string | null
          inventory_quantity: number | null
          inventory_policy: string | null
          fulfillment_service: string | null
          inventory_management: string | null
          option1: string | null
          option2: string | null
          option3: string | null
          weight: number | null
          weight_unit: string | null
          requires_shipping: boolean | null
          taxable: boolean | null
          position: number | null
          image_url: string | null
          is_active: boolean | null
          created_at: string | null
          updated_at: string | null
        }
        Insert: {
          id?: string
          merchant_product_id: string
          shopify_variant_id?: string | null
          title: string
          price?: number
          compare_at_price?: number | null
          sku?: string | null
          barcode?: string | null
          inventory_quantity?: number | null
          inventory_policy?: string | null
          fulfillment_service?: string | null
          inventory_management?: string | null
          option1?: string | null
          option2?: string | null
          option3?: string | null
          weight?: number | null
          weight_unit?: string | null
          requires_shipping?: boolean | null
          taxable?: boolean | null
          position?: number | null
          image_url?: string | null
          is_active?: boolean | null
          created_at?: string | null
          updated_at?: string | null
        }
        Update: {
          id?: string
          merchant_product_id?: string
          shopify_variant_id?: string | null
          title?: string
          price?: number
          compare_at_price?: number | null
          sku?: string | null
          barcode?: string | null
          inventory_quantity?: number | null
          inventory_policy?: string | null
          fulfillment_service?: string | null
          inventory_management?: string | null
          option1?: string | null
          option2?: string | null
          option3?: string | null
          weight?: number | null
          weight_unit?: string | null
          requires_shipping?: boolean | null
          taxable?: boolean | null
          position?: number | null
          image_url?: string | null
          is_active?: boolean | null
          created_at?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "product_variants_merchant_product_id_fkey"
            columns: ["merchant_product_id"]
            isOneToOne: false
            referencedRelation: "merchant_products"
            referencedColumns: ["id"]
          }
        ]
      }
      users: {
        Row: {
          id: string
          email: string
          role: Database["public"]["Enums"]["user_role"]
          created_at: string | null
          updated_at: string | null
        }
        Insert: {
          id: string
          email: string
          role?: Database["public"]["Enums"]["user_role"]
          created_at?: string | null
          updated_at?: string | null
        }
        Update: {
          id?: string
          email?: string
          role?: Database["public"]["Enums"]["user_role"]
          created_at?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "users_id_fkey"
            columns: ["id"]
            isOneToOne: true
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
      ticket_messages: {
        Row: {
          id: string
          ticket_id: string
          user_id: string
          message: string
          is_internal: boolean | null
          created_at: string | null
          updated_at: string | null
        }
        Insert: {
          id?: string
          ticket_id: string
          user_id: string
          message: string
          is_internal?: boolean | null
          created_at?: string | null
          updated_at?: string | null
        }
        Update: {
          id?: string
          ticket_id?: string
          user_id?: string
          message?: string
          is_internal?: boolean | null
          created_at?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "ticket_messages_ticket_id_fkey"
            columns: ["ticket_id"]
            isOneToOne: false
            referencedRelation: "tickets"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "ticket_messages_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
      tickets: {
        Row: {
          id: string
          ticket_number: string
          user_id: string
          merchant_id: string | null
          subject: string
          description: string | null
          status: Database["public"]["Enums"]["ticket_status"]
          priority: Database["public"]["Enums"]["ticket_priority"]
          assigned_to: string | null
          created_at: string | null
          updated_at: string | null
        }
        Insert: {
          id?: string
          ticket_number: string
          user_id: string
          merchant_id?: string | null
          subject: string
          description?: string | null
          status?: Database["public"]["Enums"]["ticket_status"]
          priority?: Database["public"]["Enums"]["ticket_priority"]
          assigned_to?: string | null
          created_at?: string | null
          updated_at?: string | null
        }
        Update: {
          id?: string
          ticket_number?: string
          user_id?: string
          merchant_id?: string | null
          subject?: string
          description?: string | null
          status?: Database["public"]["Enums"]["ticket_status"]
          priority?: Database["public"]["Enums"]["ticket_priority"]
          assigned_to?: string | null
          created_at?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "tickets_assigned_to_fkey"
            columns: ["assigned_to"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "tickets_merchant_id_fkey"
            columns: ["merchant_id"]
            isOneToOne: false
            referencedRelation: "merchants"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "tickets_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
      user_profiles: {
        Row: {
          id: string
          user_id: string
          first_name: string | null
          last_name: string | null
          avatar_url: string | null
          phone: string | null
          company: string | null
          created_at: string | null
          updated_at: string | null
        }
        Insert: {
          id?: string
          user_id: string
          first_name?: string | null
          last_name?: string | null
          avatar_url?: string | null
          phone?: string | null
          company?: string | null
          created_at?: string | null
          updated_at?: string | null
        }
        Update: {
          id?: string
          user_id?: string
          first_name?: string | null
          last_name?: string | null
          avatar_url?: string | null
          phone?: string | null
          company?: string | null
          created_at?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "user_profiles_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: true
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      get_admin_stats: {
        Args: Record<PropertyKey, never>
        Returns: Json
      }
      get_merchant_stats: {
        Args: {
          merchant_id: string
        }
        Returns: Json
      }
      log_activity: {
        Args: {
          p_action: string
          p_resource_type: string
          p_user_id: string
          p_merchant_id?: string
          p_resource_id?: string
          p_details?: Json
        }
        Returns: undefined
      }
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}