// User Types
export type UserRole = 'super_admin' | 'merchant' | 'customer'

export interface User {
  id: string
  email: string
  role: User<PERSON><PERSON>
  created_at: string
  updated_at: string
  profile?: UserProfile
}

export interface UserProfile {
  id: string
  user_id: string
  first_name?: string
  last_name?: string
  avatar_url?: string
  phone?: string
  company?: string
  created_at: string
  updated_at: string
}

// Merchant Types
export interface Merchant {
  id: string
  user_id: string
  shop_domain: string
  shop_name: string
  access_token: string
  subscription_tier: SubscriptionTier
  subscription_status: SubscriptionStatus
  billing_address?: BillingAddress
  settings: MerchantSettings
  created_at: string
  updated_at: string
}

export type SubscriptionTier = 'free' | 'basic' | 'pro' | 'enterprise'
export type SubscriptionStatus = 'active' | 'inactive' | 'cancelled' | 'past_due'

export interface MerchantSettings {
  customizer_enabled: boolean
  auto_fulfill: boolean
  notification_email?: string
  webhook_url?: string
}

export interface BillingAddress {
  line1: string
  line2?: string
  city: string
  state: string
  postal_code: string
  country: string
}

// Product Types
export interface Product {
  id: string
  name: string
  description: string
  base_price: number
  category: string
  tags: string[]
  images: ProductImage[]
  customization_options: CustomizationOption[]
  is_active: boolean
  created_at: string
  updated_at: string
}

export interface ProductImage {
  id: string
  url: string
  alt_text?: string
  is_primary: boolean
  sort_order: number
}

export interface CustomizationOption {
  id: string
  type: 'text' | 'image' | 'color' | 'size'
  name: string
  required: boolean
  options: any[]
  price_modifier?: number
}

// Order Types
export interface Order {
  id: string
  merchant_id: string
  customer_email: string
  customer_name?: string
  items: OrderItem[]
  total_amount: number
  status: OrderStatus
  payment_status: PaymentStatus
  shipping_address?: ShippingAddress
  tracking_number?: string
  created_at: string
  updated_at: string
}

export type OrderStatus = 'pending' | 'processing' | 'shipped' | 'delivered' | 'cancelled'
export type PaymentStatus = 'pending' | 'paid' | 'failed' | 'refunded'

export interface OrderItem {
  id: string
  product_id: string
  quantity: number
  unit_price: number
  customization_data: any
  design_id?: string
}

export interface ShippingAddress {
  name: string
  line1: string
  line2?: string
  city: string
  state: string
  postal_code: string
  country: string
  phone?: string
}

// Design Types
export interface Design {
  id: string
  customer_email: string
  merchant_id: string
  product_id: string
  name: string
  design_data: any
  preview_url?: string
  is_saved: boolean
  created_at: string
  updated_at: string
}

// Ticket Types
export interface Ticket {
  id: string
  user_id: string
  merchant_id?: string
  subject: string
  description: string
  status: TicketStatus
  priority: TicketPriority
  messages: TicketMessage[]
  created_at: string
  updated_at: string
}

export type TicketStatus = 'open' | 'in_progress' | 'resolved' | 'closed'
export type TicketPriority = 'low' | 'medium' | 'high' | 'urgent'

export interface TicketMessage {
  id: string
  ticket_id: string
  user_id: string
  message: string
  is_internal: boolean
  created_at: string
}

// API Response Types
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  message?: string
}

// Shopify Types
export interface ShopifyProduct {
  id: string
  title: string
  handle: string
  description: string
  vendor: string
  product_type: string
  tags: string[]
  variants: ShopifyVariant[]
  images: ShopifyImage[]
}

export interface ShopifyVariant {
  id: string
  title: string
  price: string
  sku?: string
  inventory_quantity: number
}

export interface ShopifyImage {
  id: string
  src: string
  alt?: string
  width: number
  height: number
}
