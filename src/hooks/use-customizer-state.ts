'use client'

import { useMemo } from 'react'
import { useCustomizer, DesignElement, InitializationState } from '@/components/providers/customizer-provider'
import { DesignAreaUV } from '@/lib/uv-extractor'
import { FinalTextureEngine } from '@/lib/final-texture-engine'
import { ThreeDInteractionManager } from '@/lib/3d-interaction-manager'
import { CanvasViewState } from '@/components/customizer/canvas-view-manager'

/**
 * Hook for managing design elements
 * Provides CRUD operations and element selection
 */
export function useDesignElements() {
  const { 
    state, 
    addElement, 
    updateElement, 
    deleteElement, 
    setSelectedElement, 
    setEditingText, 
    clearElements 
  } = useCustomizer()
  
  const designElements = state.designElements
  const selectedElementId = state.selectedElementId
  const editingTextId = state.editingTextId
  
  // Computed values
  const selectedElement = useMemo(() => 
    designElements.find(el => el.id === selectedElementId) || null,
    [designElements, selectedElementId]
  )
  
  const editingElement = useMemo(() => 
    designElements.find(el => el.id === editingTextId) || null,
    [designElements, editingTextId]
  )
  
  const elementCount = designElements.length
  
  // Helper functions
  const getElementById = (id: string) => 
    designElements.find(el => el.id === id) || null
  
  const getElementsByType = (type: DesignElement['type']) => 
    designElements.filter(el => el.type === type)
  
  const getElementsInArea = (areaName: string) => 
    designElements.filter(el => el.data?.areaName === areaName)
  
  return {
    // State
    designElements,
    selectedElementId,
    editingTextId,
    selectedElement,
    editingElement,
    elementCount,
    
    // Actions
    addElement,
    updateElement,
    deleteElement,
    setSelectedElement,
    setEditingText,
    clearElements,
    
    // Helpers
    getElementById,
    getElementsByType,
    getElementsInArea
  }
}

/**
 * Hook for managing UI state
 * Provides color, loading, and modal state management
 */
export function useCustomizerUI() {
  const { 
    state, 
    setColor, 
    setLoading, 
    setSetupModal 
  } = useCustomizer()
  
  return {
    // State
    selectedColor: state.selectedColor,
    isLoading: state.isLoading,
    showSetupModal: state.showSetupModal,
    
    // Actions
    setColor,
    setLoading,
    setSetupModal
  }
}

/**
 * Hook for managing 3D state
 * Provides 3D engine, design areas, and interaction management
 */
export function use3DState() {
  const { 
    state, 
    setDesignAreas, 
    setSelectedDesignArea, 
    setTextureEngine, 
    setInteractionManager,
    setWireframe,
    setMeshVisibility,
    setAvailableMeshes,
    set3DInteraction,
    setInitializationState
  } = useCustomizer()
  
  const designAreas = state.designAreas
  const selectedDesignArea = state.selectedDesignArea
  const textureEngine = state.textureEngine
  const interactionManager = state.interactionManager
  const showWireframe = state.showWireframe
  const meshVisibility = state.meshVisibility
  const availableMeshes = state.availableMeshes
  const enable3DInteraction = state.enable3DInteraction
  const initializationState = state.initializationState
  
  // Computed values
  const isInitialized = initializationState === InitializationState.FULLY_INITIALIZED
  const hasDesignAreas = designAreas.length > 0
  const hasSelectedArea = selectedDesignArea !== null
  const hasTextureEngine = textureEngine !== null
  const hasInteractionManager = interactionManager !== null
  
  // Helper functions
  const getDesignAreaByName = (name: string) => 
    designAreas.find(area => area.areaName === name) || null
  
  const isAreaVisible = (areaName: string) => 
    meshVisibility[areaName] !== false
  
  const getVisibleAreas = () => 
    designAreas.filter(area => isAreaVisible(area.areaName))
  
  return {
    // State
    designAreas,
    selectedDesignArea,
    textureEngine,
    interactionManager,
    showWireframe,
    meshVisibility,
    availableMeshes,
    enable3DInteraction,
    initializationState,
    
    // Computed
    isInitialized,
    hasDesignAreas,
    hasSelectedArea,
    hasTextureEngine,
    hasInteractionManager,
    
    // Actions
    setDesignAreas,
    setSelectedDesignArea,
    setTextureEngine,
    setInteractionManager,
    setWireframe,
    setMeshVisibility,
    setAvailableMeshes,
    set3DInteraction,
    setInitializationState,
    
    // Helpers
    getDesignAreaByName,
    isAreaVisible,
    getVisibleAreas
  }
}

/**
 * Hook for managing canvas view state
 * Provides view switching and canvas management
 */
export function useCanvasState() {
  const { state, setCanvasView } = useCustomizer()
  
  const canvasView = state.canvasView
  
  // Computed values
  const isMainCanvas3D = canvasView.mainCanvasView === '3d'
  const isMiniCanvas3D = canvasView.miniCanvasView === '3d'
  const shouldShow3DInMain = canvasView.mainCanvasView === '3d'
  const shouldShowPrintFileInMain = canvasView.mainCanvasView === 'printfile'
  
  // Helper functions
  const setMainView = (view: '3d' | 'printfile') => {
    setCanvasView({
      ...canvasView,
      mainCanvasView: view,
      miniCanvasView: view === '3d' ? 'printfile' : '3d'
    })
  }
  
  const setMiniView = (view: '3d' | 'printfile') => {
    setCanvasView({
      ...canvasView,
      miniCanvasView: view
    })
  }
  
  const swapViews = () => {
    setCanvasView({
      ...canvasView,
      mainCanvasView: canvasView.miniCanvasView,
      miniCanvasView: canvasView.mainCanvasView,
      isSwapping: true
    })
    
    // Reset swapping state after animation
    setTimeout(() => {
      setCanvasView({
        ...canvasView,
        mainCanvasView: canvasView.miniCanvasView,
        miniCanvasView: canvasView.mainCanvasView,
        isSwapping: false
      })
    }, 300)
  }
  
  const setSelectedGLB = (index: number) => {
    setCanvasView({
      ...canvasView,
      selectedGLBIndex: index
    })
  }
  
  const setSelectedPrintFile = (index: number) => {
    setCanvasView({
      ...canvasView,
      selectedPrintFileIndex: index
    })
  }
  
  return {
    // State
    canvasView,
    
    // Computed
    isMainCanvas3D,
    isMiniCanvas3D,
    shouldShow3DInMain,
    shouldShowPrintFileInMain,
    
    // Actions
    setCanvasView,
    setMainView,
    setMiniView,
    swapViews,
    setSelectedGLB,
    setSelectedPrintFile
  }
}

/**
 * Hook for getting the complete customizer state
 * Useful for debugging and complex operations
 */
export function useCustomizerState() {
  const { state, dispatch } = useCustomizer()
  
  return {
    state,
    dispatch
  }
}

/**
 * Hook for performance-optimized element selection
 * Only re-renders when the specific element changes
 */
export function useElement(elementId: string) {
  const { state } = useCustomizer()
  
  const element = useMemo(() => 
    state.designElements.find(el => el.id === elementId) || null,
    [state.designElements, elementId]
  )
  
  const isSelected = state.selectedElementId === elementId
  const isEditing = state.editingTextId === elementId
  
  return {
    element,
    isSelected,
    isEditing
  }
}

/**
 * Hook for performance-optimized area selection
 * Only re-renders when the specific area changes
 */
export function useDesignArea(areaName: string) {
  const { state } = useCustomizer()
  
  const area = useMemo(() => 
    state.designAreas.find(area => area.areaName === areaName) || null,
    [state.designAreas, areaName]
  )
  
  const isSelected = state.selectedDesignArea?.areaName === areaName
  const isVisible = state.meshVisibility[areaName] !== false
  
  const elementsInArea = useMemo(() => 
    state.designElements.filter(el => el.data?.areaName === areaName),
    [state.designElements, areaName]
  )
  
  return {
    area,
    isSelected,
    isVisible,
    elementsInArea,
    elementCount: elementsInArea.length
  }
}
