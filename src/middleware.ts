import { createServerClient } from '@supabase/ssr'
import { NextResponse, type NextRequest } from 'next/server'
import type { Database } from './lib/database.types'

export async function middleware(request: NextRequest) {
  let response = NextResponse.next({
    request: {
      headers: request.headers,
    },
  })

  const supabase = createServerClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return request.cookies.get(name)?.value
        },
        set(name: string, value: string, options: any) {
          request.cookies.set({
            name,
            value,
            ...options,
          })
          response = NextResponse.next({
            request: {
              headers: request.headers,
            },
          })
          response.cookies.set({
            name,
            value,
            ...options,
          })
        },
        remove(name: string, options: any) {
          request.cookies.set({
            name,
            value: '',
            ...options,
          })
          response = NextResponse.next({
            request: {
              headers: request.headers,
            },
          })
          response.cookies.set({
            name,
            value: '',
            ...options,
          })
        },
      },
    }
  )

  // Get current user
  const { data: { user: authUser }, error } = await supabase.auth.getUser()
  
  const pathname = request.nextUrl.pathname

  // Public routes that don't require authentication
  const publicRoutes = [
    '/',
    '/catalog',
    '/customizer',
    '/about',
    '/contact',
    '/privacy',
    '/terms',
    '/auth/signin',
    '/auth/signup',
    '/auth/forgot-password',
    '/auth/reset-password',
    '/auth/shopify/success',
    '/shopify',
    '/api/webhooks',
    '/api/auth/shopify/install',
    '/api/auth/shopify/callback',
    '/api/products', // Allow public access to product data for customizer
    '/api/merchants/create',
    '/api/merchants/get',
    '/api/test/create-merchant',
    '/blog',
    '/careers',
    '/cookies',
    '/docs',
    '/pricing',
    '/api-docs',
    '/status',
    '/gdpr',
  ]

  // Check if current path is public
  const isPublicRoute = publicRoutes.some(route =>
    pathname === route || pathname.startsWith(`${route}/`)
  )

  // Special handling for Shopify installation flow
  const hasShopParam = request.nextUrl.searchParams.has('shop')
  const isShopifyFlow = hasShopParam && (pathname === '/dashboard' || pathname === '/shopify')

  // If no user and trying to access protected route, redirect to signin
  // BUT allow Shopify installation flows to proceed
  if (!authUser && !isPublicRoute && !isShopifyFlow) {
    const redirectUrl = new URL('/auth/signin', request.url)
    redirectUrl.searchParams.set('redirect', pathname)
    return NextResponse.redirect(redirectUrl)
  }

  // If user exists, get their role and profile
  let userRole: string | null = null
  let userProfile: any = null

  if (authUser) {
    const { data: user } = await supabase
      .from('users')
      .select(`
        *,
        user_profiles(*),
        merchants(*)
      `)
      .eq('id', authUser.id)
      .single()

    if (user) {
      userRole = user.role
      userProfile = user
    }
  }

  // Route protection based on user role
  if (authUser && userRole) {
    // Super Admin routes
    if (pathname.startsWith('/admin')) {
      if (userRole !== 'super_admin') {
        return NextResponse.redirect(new URL('/dashboard', request.url))
      }
    }

    // Merchant dashboard routes
    if (pathname.startsWith('/dashboard')) {
      if (userRole === 'super_admin') {
        return NextResponse.redirect(new URL('/admin', request.url))
      }
      if (userRole !== 'merchant') {
        return NextResponse.redirect(new URL('/', request.url))
      }
    }

    // Shopify app routes (embedded in Shopify admin)
    if (pathname.startsWith('/shopify')) {
      if (userRole !== 'merchant') {
        return NextResponse.redirect(new URL('/', request.url))
      }
    }

    // Redirect authenticated users away from auth pages
    if (pathname.startsWith('/auth/') && !pathname.includes('signout')) {
      switch (userRole) {
        case 'super_admin':
          return NextResponse.redirect(new URL('/admin', request.url))
        case 'merchant':
          return NextResponse.redirect(new URL('/dashboard', request.url))
        case 'customer':
          return NextResponse.redirect(new URL('/', request.url))
        default:
          return NextResponse.redirect(new URL('/', request.url))
      }
    }
  }

  // Handle Shopify OAuth callback
  if (pathname === '/auth/shopify/callback') {
    // This will be handled by the API route
    return response
  }

  // Handle Shopify app installation
  if (pathname === '/auth/shopify/install') {
    // This will be handled by the API route
    return response
  }

  // Add user info to headers for use in components
  if (userProfile) {
    response.headers.set('x-user-id', userProfile.id)
    response.headers.set('x-user-role', userProfile.role)
    response.headers.set('x-user-email', userProfile.email)
    
    if (userProfile.merchants && userProfile.merchants.length > 0) {
      response.headers.set('x-merchant-id', userProfile.merchants[0].id)
      response.headers.set('x-shop-domain', userProfile.merchants[0].shop_domain)
    }
  }

  return response
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    '/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp|glb)$).*)',
  ],
}
