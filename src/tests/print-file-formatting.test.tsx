import React from 'react'
import { render, screen } from '@testing-library/react'
import '@testing-library/jest-dom'
import VerticalPrintFileSelector from '../components/customizer/vertical-print-file-selector'

// Mock data with various filename formats
const mockPrintFiles = [
  { id: '1', filename: 'front.png', url: 'front.png' },
  { id: '2', filename: 'back.png', url: 'back.png' },
  { id: '3', filename: 'left_sleeve.png', url: 'left_sleeve.png' },
  { id: '4', filename: 'right-sleeve.jpg', url: 'right-sleeve.jpg' },
  { id: '5', filename: 'chest_pocket_detail.pdf', url: 'chest_pocket_detail.pdf' },
  { id: '6', filename: 'COLLAR_DESIGN.svg', url: 'COLLAR_DESIGN.svg' },
  { id: '7', filename: 'Adult Full Zip Turtleneck Hoodie Streetwear/Lm C3/Print Files/Bottom.png', url: 'path/to/bottom.png' },
  { id: '8', filename: 'Product\\Designs\\Front\\main_design.jpg', url: 'path/to/main_design.jpg' },
  { id: '9', filename: 'very_long_filename_that_might_get_truncated_in_ui.png', url: 'very_long_filename_that_might_get_truncated_in_ui.png' }
]

const mockGetFileUrl = (bucket: string, path: string) => `https://example.com/${bucket}/${path}`

describe('Print File Name Formatting', () => {
  const defaultProps = {
    printFiles: mockPrintFiles,
    selectedIndex: 0,
    onPrintFileSelect: jest.fn(),
    getFileUrl: mockGetFileUrl
  }

  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('formats simple filenames correctly', () => {
    render(
      <VerticalPrintFileSelector
        {...defaultProps}
        printFiles={[mockPrintFiles[0], mockPrintFiles[1]]}
      />
    )

    expect(screen.getAllByText('Front')).toHaveLength(2) // Button and footer
    expect(screen.getByText('Back')).toBeInTheDocument()
  })

  it('formats underscore-separated filenames correctly', () => {
    render(
      <VerticalPrintFileSelector
        {...defaultProps}
        printFiles={[mockPrintFiles[2]]}
        selectedIndex={0}
      />
    )

    expect(screen.getAllByText('Left Sleeve')).toHaveLength(2) // Button and footer
  })

  it('formats hyphen-separated filenames correctly', () => {
    render(
      <VerticalPrintFileSelector
        {...defaultProps}
        printFiles={[mockPrintFiles[3]]}
        selectedIndex={0}
      />
    )

    expect(screen.getAllByText('Right Sleeve')).toHaveLength(2) // Button and footer
  })

  it('formats complex multi-word filenames correctly', () => {
    render(
      <VerticalPrintFileSelector
        {...defaultProps}
        printFiles={[mockPrintFiles[4]]}
        selectedIndex={0}
      />
    )

    expect(screen.getAllByText('Chest Pocket Detail')).toHaveLength(2) // Button and footer
  })

  it('handles uppercase filenames correctly', () => {
    render(
      <VerticalPrintFileSelector
        {...defaultProps}
        printFiles={[mockPrintFiles[5]]}
        selectedIndex={0}
      />
    )

    expect(screen.getAllByText('Collar Design')).toHaveLength(2) // Button and footer
  })

  it('removes various file extensions correctly', () => {
    render(
      <VerticalPrintFileSelector
        {...defaultProps}
      />
    )

    // Check that no file extensions are visible
    expect(screen.queryByText(/\.png/)).not.toBeInTheDocument()
    expect(screen.queryByText(/\.jpg/)).not.toBeInTheDocument()
    expect(screen.queryByText(/\.pdf/)).not.toBeInTheDocument()
    expect(screen.queryByText(/\.svg/)).not.toBeInTheDocument()
  })

  it('shows larger thumbnails', () => {
    render(
      <VerticalPrintFileSelector
        {...defaultProps}
        printFiles={[mockPrintFiles[0]]}
      />
    )

    const thumbnail = screen.getByAltText('front.png')
    expect(thumbnail).toHaveClass('w-12', 'h-12')
  })

  it('displays formatted names in both button and footer', () => {
    render(
      <VerticalPrintFileSelector
        {...defaultProps}
        printFiles={[mockPrintFiles[2]]} // left_sleeve.png
        selectedIndex={0}
      />
    )

    // Should appear in both the button content and the footer
    const leftSleeveElements = screen.getAllByText('Left Sleeve')
    expect(leftSleeveElements).toHaveLength(2)
    
    // One should be in the button (larger text)
    expect(leftSleeveElements[0]).toHaveClass('text-sm', 'font-semibold')
    
    // One should be in the footer (smaller text)
    expect(leftSleeveElements[1]).toHaveClass('text-xs', 'text-gray-600')
  })

  it('handles full file paths correctly (forward slashes)', () => {
    render(
      <VerticalPrintFileSelector
        {...defaultProps}
        printFiles={[mockPrintFiles[6]]} // Full path with forward slashes
        selectedIndex={0}
      />
    )

    // Should extract just "Bottom" from the full path
    expect(screen.getAllByText('Bottom')).toHaveLength(2) // Button and footer

    // Should not show the full path
    expect(screen.queryByText(/Adult Full Zip/)).not.toBeInTheDocument()
    expect(screen.queryByText(/Streetwear/)).not.toBeInTheDocument()
  })

  it('handles full file paths correctly (backslashes)', () => {
    render(
      <VerticalPrintFileSelector
        {...defaultProps}
        printFiles={[mockPrintFiles[7]]} // Full path with backslashes
        selectedIndex={0}
      />
    )

    // Should extract just "Main Design" from the full path
    expect(screen.getAllByText('Main Design')).toHaveLength(2) // Button and footer

    // Should not show the full path
    expect(screen.queryByText(/Product/)).not.toBeInTheDocument()
    expect(screen.queryByText(/Designs/)).not.toBeInTheDocument()
  })

  it('displays long names without truncation', () => {
    render(
      <VerticalPrintFileSelector
        {...defaultProps}
        printFiles={[mockPrintFiles[8]]} // Very long filename
        selectedIndex={0}
      />
    )

    // Should show the full formatted name without truncation
    const fullName = 'Very Long Filename That Might Get Truncated In Ui'
    expect(screen.getAllByText(fullName)).toHaveLength(2) // Button and footer

    // Verify the text elements don't have truncate class
    const nameElements = screen.getAllByText(fullName)
    nameElements.forEach(element => {
      expect(element).not.toHaveClass('truncate')
    })
  })
})
