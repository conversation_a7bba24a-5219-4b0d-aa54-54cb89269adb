/**
 * Test Runner for 3D Interaction System
 * Simple test runner to verify the integration works correctly
 */

import * as THREE from 'three';
import { ThreeDInteractionManager } from '@/lib/3d-interaction-manager';
import { DesignElement } from '@/lib/fabric-threejs-bridge';
import { DesignAreaUV } from '@/lib/uv-extractor';

interface TestResult {
  test: string;
  passed: boolean;
  error?: string;
}

export class InteractionTestRunner {
  private results: TestResult[] = [];

  /**
   * Run all integration tests
   */
  async runTests(): Promise<{ passed: number; failed: number; results: TestResult[] }> {
    console.log('🧪 Starting 3D Interaction Integration Tests...');
    
    this.results = [];
    
    // Run individual tests
    await this.testSystemInitialization();
    await this.testElementManagement();
    await this.testElementSelection();
    await this.testErrorHandling();
    await this.testMemoryManagement();
    
    // Calculate results
    const passed = this.results.filter(r => r.passed).length;
    const failed = this.results.filter(r => !r.passed).length;
    
    console.log(`🧪 Tests completed: ${passed} passed, ${failed} failed`);
    
    return { passed, failed, results: this.results };
  }

  /**
   * Test system initialization
   */
  private async testSystemInitialization(): Promise<void> {
    try {
      const { scene, camera, canvas, interactionManager, designArea } = this.createTestEnvironment();
      
      // Test 1: Manager should initialize
      this.assert('System initialization', interactionManager !== null);
      
      // Test 2: Design area should register
      interactionManager.registerDesignArea(designArea);
      interactionManager.setCurrentDesignArea(designArea);
      
      const state = interactionManager.getState();
      this.assert('Design area registration', state.currentDesignArea === 'Front');
      
      // Cleanup
      interactionManager.dispose();
      
    } catch (error) {
      this.assert('System initialization', false, error instanceof Error ? error.message : 'Unknown error');
    }
  }

  /**
   * Test element management
   */
  private async testElementManagement(): Promise<void> {
    try {
      const { interactionManager, designArea } = this.createTestEnvironment();
      
      interactionManager.registerDesignArea(designArea);
      interactionManager.setCurrentDesignArea(designArea);
      
      // Test 1: Add element
      const element: DesignElement = {
        id: 'test-element-1',
        type: 'text',
        x: 100,
        y: 100,
        width: 200,
        height: 50,
        rotation: 0,
        zIndex: 1,
        data: {
          text: 'Test Text',
          fontSize: 24,
          color: '#000000'
        }
      };
      
      interactionManager.addElement(element);
      this.assert('Add element', interactionManager.getState().elementCount === 1);
      
      // Test 2: Update element
      interactionManager.updateElement(element.id, { x: 150, y: 150 });
      const updatedElement = interactionManager.getAllElements().find(el => el.id === element.id);
      this.assert('Update element', updatedElement?.x === 150 && updatedElement?.y === 150);
      
      // Test 3: Remove element
      interactionManager.removeElement(element.id);
      this.assert('Remove element', interactionManager.getState().elementCount === 0);
      
      // Cleanup
      interactionManager.dispose();
      
    } catch (error) {
      this.assert('Element management', false, error instanceof Error ? error.message : 'Unknown error');
    }
  }

  /**
   * Test element selection
   */
  private async testElementSelection(): Promise<void> {
    try {
      const { interactionManager, designArea } = this.createTestEnvironment();
      
      interactionManager.registerDesignArea(designArea);
      interactionManager.setCurrentDesignArea(designArea);
      
      const element: DesignElement = {
        id: 'test-element-2',
        type: 'text',
        x: 100,
        y: 100,
        width: 200,
        height: 50,
        rotation: 0,
        zIndex: 1,
        data: {
          text: 'Selectable Text',
          fontSize: 24,
          color: '#000000'
        }
      };
      
      // Test 1: Select element
      interactionManager.addElement(element);
      interactionManager.selectElement(element.id);
      this.assert('Select element', interactionManager.getState().selectedElementId === element.id);
      
      // Test 2: Clear selection
      interactionManager.selectElement(null);
      this.assert('Clear selection', interactionManager.getState().selectedElementId === null);
      
      // Cleanup
      interactionManager.dispose();
      
    } catch (error) {
      this.assert('Element selection', false, error instanceof Error ? error.message : 'Unknown error');
    }
  }

  /**
   * Test error handling
   */
  private async testErrorHandling(): Promise<void> {
    try {
      const { interactionManager, designArea } = this.createTestEnvironment();
      
      interactionManager.registerDesignArea(designArea);
      interactionManager.setCurrentDesignArea(designArea);
      
      // Test 1: Invalid element operations should not throw
      let noError = true;
      try {
        interactionManager.updateElement('non-existent-id', { x: 100 });
        interactionManager.removeElement('non-existent-id');
        interactionManager.selectElement('non-existent-id');
      } catch {
        noError = false;
      }
      
      this.assert('Invalid operations handling', noError);
      
      // Cleanup
      interactionManager.dispose();
      
    } catch (error) {
      this.assert('Error handling', false, error instanceof Error ? error.message : 'Unknown error');
    }
  }

  /**
   * Test memory management
   */
  private async testMemoryManagement(): Promise<void> {
    try {
      const { interactionManager, designArea } = this.createTestEnvironment();
      
      interactionManager.registerDesignArea(designArea);
      interactionManager.setCurrentDesignArea(designArea);
      
      // Add multiple elements
      const elements: DesignElement[] = [
        {
          id: 'test-element-3',
          type: 'text',
          x: 100,
          y: 100,
          width: 200,
          height: 50,
          rotation: 0,
          zIndex: 1,
          data: { text: 'Element 1', fontSize: 24, color: '#000000' }
        },
        {
          id: 'test-element-4',
          type: 'text',
          x: 200,
          y: 200,
          width: 200,
          height: 50,
          rotation: 0,
          zIndex: 2,
          data: { text: 'Element 2', fontSize: 24, color: '#000000' }
        }
      ];
      
      elements.forEach(element => interactionManager.addElement(element));
      this.assert('Add multiple elements', interactionManager.getState().elementCount === 2);
      
      // Test clear
      interactionManager.clear();
      this.assert('Clear all elements', interactionManager.getState().elementCount === 0);
      
      // Test disposal
      let disposalSuccess = true;
      try {
        interactionManager.dispose();
      } catch {
        disposalSuccess = false;
      }
      
      this.assert('Disposal', disposalSuccess);
      
    } catch (error) {
      this.assert('Memory management', false, error instanceof Error ? error.message : 'Unknown error');
    }
  }

  /**
   * Create test environment
   */
  private createTestEnvironment() {
    // Create mock DOM elements
    const canvas = {
      addEventListener: () => {},
      removeEventListener: () => {},
      getBoundingClientRect: () => ({ left: 0, top: 0, width: 800, height: 600 }),
      width: 800,
      height: 600
    } as any;

    const fabricCanvas = {
      width: 512,
      height: 512,
      style: { display: 'none' }
    } as any;

    // Create Three.js components
    const scene = new THREE.Scene();
    const camera = new THREE.OrthographicCamera(-2, 2, 2, -2, 0.1, 1000);
    
    // Add a simple mesh for testing
    const geometry = new THREE.BoxGeometry(1, 1, 1);
    const material = new THREE.MeshBasicMaterial({ color: 0x00ff00 });
    const mesh = new THREE.Mesh(geometry, material);
    mesh.name = 'Front';
    scene.add(mesh);

    // Create design area
    const designArea: DesignAreaUV = {
      areaName: 'Front',
      mesh,
      material: mesh.material,
      isDesignArea: true,
      mapping: {
        name: 'Front',
        boundingBox: {
          min: [0, 0],
          max: [1, 1]
        },
        uvCoordinates: new Float32Array([0, 0, 1, 0, 1, 1, 0, 1]),
        vertices: new Float32Array([0, 0, 0, 1, 0, 0, 1, 1, 0, 0, 1, 0]),
        indices: new Uint16Array([0, 1, 2, 0, 2, 3]),
        textureSize: { width: 512, height: 512 },
        center: [0.5, 0.5]
      }
    };

    // Mock document.body for fabric canvas
    if (typeof document !== 'undefined') {
      (document.body as any).appendChild = (node: any) => node;
      (document.body as any).removeChild = (node: any) => node;
      (document.body as any).contains = () => true;
    }

    // Create interaction manager
    const interactionManager = new ThreeDInteractionManager(
      scene,
      camera,
      canvas,
      fabricCanvas
    );

    return {
      scene,
      camera,
      canvas,
      fabricCanvas,
      interactionManager,
      designArea
    };
  }

  /**
   * Assert test result
   */
  private assert(testName: string, condition: boolean, error?: string): void {
    this.results.push({
      test: testName,
      passed: condition,
      error
    });
    
    if (condition) {
      console.log(`✅ ${testName}`);
    } else {
      console.log(`❌ ${testName}${error ? `: ${error}` : ''}`);
    }
  }

  /**
   * Get test results
   */
  getResults() {
    return this.results;
  }
}

// Export function to run tests
export async function runIntegrationTests() {
  const runner = new InteractionTestRunner();
  return await runner.runTests();
}
