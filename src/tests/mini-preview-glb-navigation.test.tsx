import React from 'react'
import { render, screen, fireEvent } from '@testing-library/react'
import '@testing-library/jest-dom'
import MiniPreviewCanvas from '../components/customizer/mini-preview-canvas'

// Mock the Simple3DViewer component
jest.mock('../components/Simple3DViewer', () => {
  return function MockSimple3DViewer() {
    return <div data-testid="mock-3d-viewer">3D Viewer</div>
  }
})

// Mock data
const mockGLBFiles = [
  { id: '1', name: 'Front View', url: 'front.glb' },
  { id: '2', name: 'Back View', url: 'back.glb' },
  { id: '3', name: 'Side View', url: 'side.glb' }
]

const mockPrintFiles = [
  { id: '1', filename: 'front.png', url: 'front.png' },
  { id: '2', filename: 'back.png', url: 'back.png' }
]

const mockGetGLBUrl = (glbFile: any) => `https://example.com/${glbFile.url}`
const mockGetFileUrl = (bucket: string, path: string) => `https://example.com/${bucket}/${path}`

describe('MiniPreviewCanvas GLB Navigation', () => {
  const defaultProps = {
    onSwapViews: jest.fn(),
    printFiles: mockPrintFiles,
    selectedPrintFileIndex: 0,
    onPrintFileSelect: jest.fn(),
    glbFiles: mockGLBFiles,
    selectedGLBIndex: 0,
    onGLBSelect: jest.fn(),
    designElements: [],
    getFileUrl: mockGetFileUrl,
    getGLBUrl: mockGetGLBUrl
  }

  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('shows GLB navigation controls when currentView is "3d" and multiple GLB files exist', () => {
    render(
      <MiniPreviewCanvas
        {...defaultProps}
        currentView="3d"
      />
    )

    // Should show navigation arrows (look for buttons with absolute positioning)
    const allButtons = screen.getAllByRole('button')
    const navButtons = allButtons.filter(btn =>
      btn.className.includes('absolute') &&
      (btn.className.includes('left-1') || btn.className.includes('right-1'))
    )
    expect(navButtons.length).toBeGreaterThanOrEqual(2) // Should have prev and next buttons

    // Should show GLB thumbnails with titles
    expect(screen.getByTitle('Front View')).toBeInTheDocument()
    expect(screen.getByTitle('Back View')).toBeInTheDocument()
    expect(screen.getByTitle('Side View')).toBeInTheDocument()

    // Should show indicator dots
    const dots = allButtons.filter(btn =>
      btn.className.includes('w-1.5 h-1.5 rounded-full')
    )
    expect(dots).toHaveLength(3) // One dot for each GLB file
  })

  it('calls onGLBSelect when GLB thumbnail is clicked', () => {
    const onGLBSelect = jest.fn()

    render(
      <MiniPreviewCanvas
        {...defaultProps}
        currentView="3d"
        onGLBSelect={onGLBSelect}
      />
    )

    // Click the second GLB thumbnail by title
    const backViewButton = screen.getByTitle('Back View')
    fireEvent.click(backViewButton)
    expect(onGLBSelect).toHaveBeenCalledWith(1)
  })

  it('calls onGLBSelect when indicator dot is clicked', () => {
    const onGLBSelect = jest.fn()
    
    render(
      <MiniPreviewCanvas
        {...defaultProps}
        currentView="3d"
        onGLBSelect={onGLBSelect}
      />
    )

    // Find indicator dots (small round buttons in the GLB section)
    const dots = screen.getAllByRole('button').filter(btn => 
      btn.className.includes('w-1.5 h-1.5 rounded-full')
    )

    // Click the third dot
    fireEvent.click(dots[2])
    expect(onGLBSelect).toHaveBeenCalledWith(2)
  })

  it('does not show GLB navigation when only one GLB file exists', () => {
    render(
      <MiniPreviewCanvas
        {...defaultProps}
        currentView="3d"
        glbFiles={[mockGLBFiles[0]]} // Only one GLB file
      />
    )

    // Should not show GLB thumbnails section when only one file
    expect(screen.queryByTitle('Front View')).not.toBeInTheDocument()
    expect(screen.queryByTitle('Back View')).not.toBeInTheDocument()
    expect(screen.queryByTitle('Side View')).not.toBeInTheDocument()
  })

  it('does not show GLB navigation when currentView is "printfile"', () => {
    render(
      <MiniPreviewCanvas
        {...defaultProps}
        currentView="printfile"
      />
    )

    // Should not show GLB navigation controls
    expect(screen.queryByTitle('Front View')).not.toBeInTheDocument()
    expect(screen.queryByTitle('Back View')).not.toBeInTheDocument()
    expect(screen.queryByTitle('Side View')).not.toBeInTheDocument()
  })

  it('highlights the selected GLB thumbnail', () => {
    render(
      <MiniPreviewCanvas
        {...defaultProps}
        currentView="3d"
        selectedGLBIndex={1}
      />
    )

    // The selected button (Back View at index 1) should have purple border
    const backViewButton = screen.getByTitle('Back View')
    const frontViewButton = screen.getByTitle('Front View')
    const sideViewButton = screen.getByTitle('Side View')

    expect(backViewButton).toHaveClass('border-purple-500')
    expect(frontViewButton).not.toHaveClass('border-purple-500')
    expect(sideViewButton).not.toHaveClass('border-purple-500')
  })
})
