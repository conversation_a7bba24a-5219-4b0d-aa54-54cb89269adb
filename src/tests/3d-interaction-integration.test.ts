/**
 * 3D Interaction Integration Tests
 * Tests the complete integration of all 3D interaction systems
 */

import * as THREE from 'three';
import { ThreeDInteractionManager } from '@/lib/3d-interaction-manager';
import { ThreeDElementMapper } from '@/lib/3d-element-mapping';
import { ThreeDRaycaster } from '@/lib/3d-raycasting';
import { FabricThreeJSBridge, DesignElement } from '@/lib/fabric-threejs-bridge';
import { ThreeDVisualSelectors } from '@/lib/3d-visual-selectors';
import { ThreeDTransformHandles } from '@/lib/3d-transform-handles';
import { ThreeDHoverEffects } from '@/lib/3d-hover-effects';
import { ThreeDPerformanceOptimizer } from '@/lib/3d-performance-optimizer';
import { DesignAreaUV } from '@/lib/uv-extractor';

// Mock DOM elements
const mockCanvas = {
  addEventListener: jest.fn(),
  removeEventListener: jest.fn(),
  getBoundingClientRect: () => ({ left: 0, top: 0, width: 800, height: 600 }),
  width: 800,
  height: 600
} as any;

const mockFabricCanvas = {
  width: 512,
  height: 512,
  style: { display: 'none' }
} as any;

// Mock Three.js components
const createMockScene = (): THREE.Scene => {
  const scene = new THREE.Scene();
  
  // Add a simple mesh for testing
  const geometry = new THREE.BoxGeometry(1, 1, 1);
  const material = new THREE.MeshBasicMaterial({ color: 0x00ff00 });
  const mesh = new THREE.Mesh(geometry, material);
  mesh.name = 'Front';
  scene.add(mesh);
  
  return scene;
};

const createMockCamera = (): THREE.Camera => {
  return new THREE.OrthographicCamera(-2, 2, 2, -2, 0.1, 1000);
};

const createMockRenderer = (): THREE.WebGLRenderer => {
  const canvas = document.createElement('canvas');
  const context = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
  
  return {
    domElement: mockCanvas,
    getContext: () => context,
    setSize: jest.fn(),
    render: jest.fn(),
    dispose: jest.fn(),
    info: {
      render: { calls: 0, triangles: 0 },
      memory: { geometries: 0, textures: 0 }
    },
    capabilities: { isWebGL2: false },
    setPixelRatio: jest.fn(),
    outputColorSpace: THREE.SRGBColorSpace,
    shadowMap: { enabled: false, type: THREE.PCFSoftShadowMap },
    sortObjects: true,
    autoClear: true,
    autoClearColor: true,
    autoClearDepth: true,
    autoClearStencil: true
  } as any;
};

const createMockDesignArea = (): DesignAreaUV => {
  const geometry = new THREE.BoxGeometry(1, 1, 1);
  const material = new THREE.MeshBasicMaterial({ color: 0x00ff00 });
  const mesh = new THREE.Mesh(geometry, material);
  mesh.name = 'Front';
  
  return {
    areaName: 'Front',
    mesh,
    material: mesh.material,
    isDesignArea: true,
    mapping: {
      name: 'Front',
      boundingBox: {
        min: [0, 0],
        max: [1, 1]
      },
      uvCoordinates: new Float32Array([0, 0, 1, 0, 1, 1, 0, 1]),
      vertices: new Float32Array([0, 0, 0, 1, 0, 0, 1, 1, 0, 0, 1, 0]),
      indices: new Uint16Array([0, 1, 2, 0, 2, 3]),
      textureSize: {
        width: 512,
        height: 512
      },
      center: [0.5, 0.5]
    }
  };
};

describe('3D Interaction Integration Tests', () => {
  let scene: THREE.Scene;
  let camera: THREE.Camera;
  let renderer: THREE.WebGLRenderer;
  let interactionManager: ThreeDInteractionManager;
  let designArea: DesignAreaUV;

  beforeEach(() => {
    // Setup Three.js components
    scene = createMockScene();
    camera = createMockCamera();
    renderer = createMockRenderer();
    designArea = createMockDesignArea();
    
    // Mock document.body for fabric canvas
    document.body.appendChild = jest.fn();
    document.body.removeChild = jest.fn();
    document.body.contains = jest.fn().mockReturnValue(true);
    
    // Initialize interaction manager
    interactionManager = new ThreeDInteractionManager(
      scene,
      camera,
      mockCanvas,
      mockFabricCanvas
    );
    
    // Register design area
    interactionManager.registerDesignArea(designArea);
    interactionManager.setCurrentDesignArea(designArea);
  });

  afterEach(() => {
    if (interactionManager) {
      interactionManager.dispose();
    }
  });

  describe('System Initialization', () => {
    test('should initialize all systems correctly', () => {
      expect(interactionManager).toBeDefined();
      expect(interactionManager.getState().currentDesignArea).toBe('Front');
    });

    test('should register design areas', () => {
      const state = interactionManager.getState();
      expect(state.currentDesignArea).toBe('Front');
    });
  });

  describe('Element Management', () => {
    test('should add design elements', () => {
      const element: DesignElement = {
        id: 'test-element-1',
        type: 'text',
        x: 100,
        y: 100,
        width: 200,
        height: 50,
        rotation: 0,
        zIndex: 1,
        data: {
          text: 'Test Text',
          fontSize: 24,
          color: '#000000'
        }
      };

      interactionManager.addElement(element);
      
      const state = interactionManager.getState();
      expect(state.elementCount).toBe(1);
    });

    test('should update design elements', () => {
      const element: DesignElement = {
        id: 'test-element-2',
        type: 'text',
        x: 100,
        y: 100,
        width: 200,
        height: 50,
        rotation: 0,
        zIndex: 1,
        data: {
          text: 'Original Text',
          fontSize: 24,
          color: '#000000'
        }
      };

      interactionManager.addElement(element);
      
      const updates = {
        x: 150,
        y: 150,
        data: { ...element.data, text: 'Updated Text' }
      };
      
      interactionManager.updateElement(element.id, updates);
      
      const updatedElement = interactionManager.getAllElements().find(el => el.id === element.id);
      expect(updatedElement?.x).toBe(150);
      expect(updatedElement?.y).toBe(150);
      expect(updatedElement?.data.text).toBe('Updated Text');
    });

    test('should remove design elements', () => {
      const element: DesignElement = {
        id: 'test-element-3',
        type: 'image',
        x: 50,
        y: 50,
        width: 100,
        height: 100,
        rotation: 0,
        zIndex: 1,
        data: {
          src: 'test-image.jpg'
        }
      };

      interactionManager.addElement(element);
      expect(interactionManager.getState().elementCount).toBe(1);
      
      interactionManager.removeElement(element.id);
      expect(interactionManager.getState().elementCount).toBe(0);
    });
  });

  describe('Element Selection', () => {
    test('should select elements', () => {
      const element: DesignElement = {
        id: 'test-element-4',
        type: 'text',
        x: 100,
        y: 100,
        width: 200,
        height: 50,
        rotation: 0,
        zIndex: 1,
        data: {
          text: 'Selectable Text',
          fontSize: 24,
          color: '#000000'
        }
      };

      interactionManager.addElement(element);
      interactionManager.selectElement(element.id);
      
      const state = interactionManager.getState();
      expect(state.selectedElementId).toBe(element.id);
    });

    test('should clear selection', () => {
      const element: DesignElement = {
        id: 'test-element-5',
        type: 'text',
        x: 100,
        y: 100,
        width: 200,
        height: 50,
        rotation: 0,
        zIndex: 1,
        data: {
          text: 'Selectable Text',
          fontSize: 24,
          color: '#000000'
        }
      };

      interactionManager.addElement(element);
      interactionManager.selectElement(element.id);
      expect(interactionManager.getState().selectedElementId).toBe(element.id);
      
      interactionManager.selectElement(null);
      expect(interactionManager.getState().selectedElementId).toBeNull();
    });
  });

  describe('Configuration Management', () => {
    test('should update configuration', () => {
      const newConfig = {
        enableHoverEffects: false,
        enableTransformHandles: false,
        debugMode: true
      };

      interactionManager.updateConfig(newConfig);
      
      // Configuration should be updated (we can't directly test private config,
      // but we can test that the method doesn't throw)
      expect(() => interactionManager.updateConfig(newConfig)).not.toThrow();
    });
  });

  describe('Callback System', () => {
    test('should handle callbacks', () => {
      const onElementSelect = jest.fn();
      const onElementUpdate = jest.fn();
      const onCanvasUpdate = jest.fn();

      interactionManager.setCallbacks({
        onElementSelect,
        onElementUpdate,
        onCanvasUpdate
      });

      const element: DesignElement = {
        id: 'test-element-6',
        type: 'text',
        x: 100,
        y: 100,
        width: 200,
        height: 50,
        rotation: 0,
        zIndex: 1,
        data: {
          text: 'Callback Test',
          fontSize: 24,
          color: '#000000'
        }
      };

      interactionManager.addElement(element);
      interactionManager.selectElement(element.id);

      expect(onElementSelect).toHaveBeenCalledWith(element.id);
    });
  });

  describe('Performance Optimization', () => {
    test('should initialize performance optimizer', () => {
      const optimizer = new ThreeDPerformanceOptimizer(renderer, scene, camera);
      
      expect(optimizer).toBeDefined();
      expect(optimizer.getMetrics()).toBeDefined();
      
      optimizer.dispose();
    });

    test('should provide performance metrics', () => {
      const optimizer = new ThreeDPerformanceOptimizer(renderer, scene, camera);
      const metrics = optimizer.getMetrics();
      
      expect(metrics).toHaveProperty('fps');
      expect(metrics).toHaveProperty('frameTime');
      expect(metrics).toHaveProperty('drawCalls');
      expect(metrics).toHaveProperty('triangles');
      expect(metrics).toHaveProperty('memoryUsage');
      
      optimizer.dispose();
    });

    test('should provide performance recommendations', () => {
      const optimizer = new ThreeDPerformanceOptimizer(renderer, scene, camera);
      const recommendations = optimizer.getRecommendations();
      
      expect(Array.isArray(recommendations)).toBe(true);
      
      optimizer.dispose();
    });
  });

  describe('Error Handling', () => {
    test('should handle invalid element operations gracefully', () => {
      expect(() => {
        interactionManager.updateElement('non-existent-id', { x: 100 });
      }).not.toThrow();
      
      expect(() => {
        interactionManager.removeElement('non-existent-id');
      }).not.toThrow();
      
      expect(() => {
        interactionManager.selectElement('non-existent-id');
      }).not.toThrow();
    });

    test('should handle operations without design area', () => {
      const newManager = new ThreeDInteractionManager(
        scene,
        camera,
        mockCanvas,
        mockFabricCanvas
      );

      const element: DesignElement = {
        id: 'test-element-7',
        type: 'text',
        x: 100,
        y: 100,
        width: 200,
        height: 50,
        rotation: 0,
        zIndex: 1,
        data: {
          text: 'No Design Area',
          fontSize: 24,
          color: '#000000'
        }
      };

      // Should not throw even without design area
      expect(() => {
        newManager.addElement(element);
      }).not.toThrow();

      newManager.dispose();
    });
  });

  describe('Memory Management', () => {
    test('should dispose resources properly', () => {
      const element: DesignElement = {
        id: 'test-element-8',
        type: 'text',
        x: 100,
        y: 100,
        width: 200,
        height: 50,
        rotation: 0,
        zIndex: 1,
        data: {
          text: 'Disposal Test',
          fontSize: 24,
          color: '#000000'
        }
      };

      interactionManager.addElement(element);
      expect(interactionManager.getState().elementCount).toBe(1);
      
      interactionManager.dispose();
      
      // After disposal, operations should not throw but may not work
      expect(() => {
        interactionManager.addElement(element);
      }).not.toThrow();
    });

    test('should clear all elements', () => {
      const elements: DesignElement[] = [
        {
          id: 'test-element-9',
          type: 'text',
          x: 100,
          y: 100,
          width: 200,
          height: 50,
          rotation: 0,
          zIndex: 1,
          data: { text: 'Element 1', fontSize: 24, color: '#000000' }
        },
        {
          id: 'test-element-10',
          type: 'text',
          x: 200,
          y: 200,
          width: 200,
          height: 50,
          rotation: 0,
          zIndex: 2,
          data: { text: 'Element 2', fontSize: 24, color: '#000000' }
        }
      ];

      elements.forEach(element => interactionManager.addElement(element));
      expect(interactionManager.getState().elementCount).toBe(2);
      
      interactionManager.clear();
      expect(interactionManager.getState().elementCount).toBe(0);
    });
  });
});
