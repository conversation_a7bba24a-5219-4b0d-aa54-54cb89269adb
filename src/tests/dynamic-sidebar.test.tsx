import React from 'react'
import { render, screen, fireEvent } from '@testing-library/react'
import '@testing-library/jest-dom'
import DynamicSidebar from '../components/customizer/dynamic-sidebar'

// Mock data
const mockGLBFiles = [
  { id: '1', name: 'Model 1', url: 'model1.glb' },
  { id: '2', name: 'Model 2', url: 'model2.glb' }
]

const mockPrintFiles = [
  { id: '1', filename: 'front.png', url: 'front.png' },
  { id: '2', filename: 'back.png', url: 'back.png' }
]

const mockGetGLBUrl = (glbFile: any) => `https://example.com/${glbFile.url}`
const mockGetFileUrl = (bucket: string, path: string) => `https://example.com/${bucket}/${path}`

describe('DynamicSidebar', () => {
  const defaultProps = {
    glbFiles: mockGLBFiles,
    selectedGLBIndex: 0,
    onGLBSelect: jest.fn(),
    getGLBUrl: mockGetGLBUrl,
    printFiles: mockPrintFiles,
    selectedPrintFileIndex: 0,
    onPrintFileSelect: jest.fn(),
    getFileUrl: mockGetFileUrl
  }

  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('renders 3D selector when currentView is "3d"', () => {
    render(
      <DynamicSidebar
        {...defaultProps}
        currentView="3d"
      />
    )

    expect(screen.getByText('3D Views')).toBeInTheDocument()
    expect(screen.getAllByText('Model 1')).toHaveLength(2) // One in button, one in footer
    expect(screen.getByText('Model 2')).toBeInTheDocument()
  })

  it('renders print file selector when currentView is "printfile"', () => {
    render(
      <DynamicSidebar
        {...defaultProps}
        currentView="printfile"
      />
    )

    expect(screen.getByText('Print Files')).toBeInTheDocument()
    expect(screen.getAllByText('Front')).toHaveLength(2) // One in button, one in footer
    expect(screen.getByText('Back')).toBeInTheDocument()
  })

  it('calls onGLBSelect when 3D model is clicked', () => {
    const onGLBSelect = jest.fn()
    
    render(
      <DynamicSidebar
        {...defaultProps}
        currentView="3d"
        onGLBSelect={onGLBSelect}
      />
    )

    fireEvent.click(screen.getByText('Model 2'))
    expect(onGLBSelect).toHaveBeenCalledWith(1)
  })

  it('calls onPrintFileSelect when print file is clicked', () => {
    const onPrintFileSelect = jest.fn()
    
    render(
      <DynamicSidebar
        {...defaultProps}
        currentView="printfile"
        onPrintFileSelect={onPrintFileSelect}
      />
    )

    fireEvent.click(screen.getByText('Back'))
    expect(onPrintFileSelect).toHaveBeenCalledWith(1)
  })

  it('shows fallback message when no GLB files are available', () => {
    render(
      <DynamicSidebar
        {...defaultProps}
        currentView="3d"
        glbFiles={[]}
      />
    )

    expect(screen.getByText('No 3D models available')).toBeInTheDocument()
  })

  it('shows fallback message when no print files are available', () => {
    render(
      <DynamicSidebar
        {...defaultProps}
        currentView="printfile"
        printFiles={[]}
      />
    )

    expect(screen.getByText('No print files available')).toBeInTheDocument()
  })

  it('switches content when currentView changes', () => {
    const { rerender } = render(
      <DynamicSidebar
        {...defaultProps}
        currentView="3d"
      />
    )

    expect(screen.getByText('3D Views')).toBeInTheDocument()

    rerender(
      <DynamicSidebar
        {...defaultProps}
        currentView="printfile"
      />
    )

    expect(screen.getByText('Print Files')).toBeInTheDocument()
    expect(screen.queryByText('3D Views')).not.toBeInTheDocument()
  })
})
