'use client'

import { useEffect, useState, Suspense } from 'react'
import { useSearchParams } from 'next/navigation'
import MerchantDashboard from '@/components/dashboard/merchant-dashboard'

function EmbeddedDashboardContent() {
  const [isLoading, setIsLoading] = useState(true)
  const [merchant, setMerchant] = useState<any>(null)
  const [user, setUser] = useState<any>(null)
  const [error, setError] = useState<string | null>(null)
  const searchParams = useSearchParams()

  useEffect(() => {
    const loadMerchantData = async () => {
      const shopParam = searchParams.get('shop')
      
      if (!shopParam) {
        setError('Shop parameter is missing')
        setIsLoading(false)
        return
      }

      try {
        // Use API endpoint to get merchant data (bypasses RLS issues)
        const response = await fetch(`/api/merchants/get?shop_domain=${encodeURIComponent(shopParam)}`)
        const result = await response.json()

        if (!response.ok || !result.success) {
          console.error('Error fetching merchant:', result.error)
          setError('Merchant not found. Please reinstall the app.')
          setIsLoading(false)
          return
        }

        const merchantData = result.merchant
        setMerchant(merchantData)

        // Log dashboard access
        try {
          await fetch('/api/activity/log', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              action: 'merchant_dashboard_accessed',
              resource_type: 'dashboard',
              resource_id: merchantData.id,
              details: {
                shop_domain: merchantData.shop_domain,
                shop_name: merchantData.shop_name,
                access_method: 'embedded_shopify_admin'
              }
            })
          })
        } catch (logError) {
          console.warn('Failed to log dashboard access:', logError)
        }

        // Create user data for display
        const userData = {
          id: merchantData.user_id || 'mock-user',
          email: merchantData.shop_domain,
          role: 'merchant',
          created_at: merchantData.created_at,
          updated_at: merchantData.updated_at,
          user_profiles: [{
            id: 'mock-profile',
            user_id: merchantData.user_id || 'mock-user',
            first_name: 'Merchant',
            last_name: 'User',
            company: merchantData.shop_name || merchantData.shop_domain,
            created_at: merchantData.created_at,
            updated_at: merchantData.updated_at
          }]
        }
        setUser(userData)

        setIsLoading(false)
      } catch (error) {
        console.error('Error loading merchant data:', error)
        setError('Failed to load merchant data')
        setIsLoading(false)
      }
    }

    loadMerchantData()
  }, [searchParams])

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading your dashboard...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
            {error}
          </div>
          <button
            onClick={() => window.location.reload()}
            className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
          >
            Retry
          </button>
        </div>
      </div>
    )
  }

  if (!merchant) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded mb-4">
            Merchant not found. Please reinstall the app.
          </div>
        </div>
      </div>
    )
  }

  // Prepare data for merchant dashboard
  const profile = user?.user_profiles?.[0] || {
    id: 'mock-profile',
    user_id: user?.id || merchant.user_id,
    first_name: 'Merchant',
    last_name: 'User',
    avatar_url: null,
    company: merchant.shop_name || merchant.shop_domain,
    phone: null,
    created_at: user?.created_at || merchant.created_at,
    updated_at: user?.updated_at || merchant.updated_at,
  }

  const dashboardData = {
    type: 'merchant',
    merchantAccount: merchant,
    shopifyData: null,
    customOrders: [],
    customProducts: [],
    recentOrders: [],
    stats: {
      total_orders: 0,
      total_revenue: 0,
      products_enabled: 0,
      designs_created: 0,
    },
  }

  return (
    <div className="min-h-screen bg-gray-50 pb-safe-bottom">
      <MerchantDashboard
        user={user || profile}
        profile={profile}
        data={dashboardData}
        merchant={merchant}
        isEmbedded={true}
      />
    </div>
  )
}

export default function EmbeddedDashboard() {
  return (
    <Suspense fallback={
      <div className="flex items-center justify-center min-h-screen bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading Biypod Customizer...</p>
        </div>
      </div>
    }>
      <EmbeddedDashboardContent />
    </Suspense>
  )
}
