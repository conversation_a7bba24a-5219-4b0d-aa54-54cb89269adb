'use client'

import { useEffect, useState, Suspense } from 'react'
import { useSearchParams, useRouter } from 'next/navigation'

function ShopifyEmbeddedAppContent() {
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const searchParams = useSearchParams()
  const router = useRouter()

  useEffect(() => {
    const initializeApp = async () => {
      // Get shop and host from URL parameters
      const shopParam = searchParams.get('shop')
      const hostParam = searchParams.get('host')

      if (!shopParam) {
        setError('Shop parameter is missing')
        setIsLoading(false)
        return
      }

      try {
        // Ensure merchant exists, create if not
        const response = await fetch('/api/merchants/create', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            shop_domain: shopParam
          })
        })

        const result = await response.json()

        if (result.success) {
          console.log('Merchant ready:', result.merchant)
          // Redirect to embedded dashboard
          const dashboardUrl = `/shopify/dashboard?shop=${encodeURIComponent(shopParam)}`
          if (hostParam) {
            router.push(`${dashboardUrl}&host=${encodeURIComponent(hostParam)}`)
          } else {
            router.push(dashboardUrl)
          }
        } else {
          console.error('Error ensuring merchant exists:', result.error)
          setError('Failed to initialize merchant account')
          setIsLoading(false)
        }
      } catch (error) {
        console.error('Error initializing app:', error)
        setError('Failed to initialize app')
        setIsLoading(false)
      }
    }

    initializeApp()
  }, [searchParams, router])





  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Initializing Biypod Customizer...</p>
          <p className="text-sm text-gray-500 mt-2">Setting up your merchant account...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
            {error}
          </div>
          <button
            onClick={() => window.location.reload()}
            className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
          >
            Retry
          </button>
        </div>
      </div>
    )
  }

  // This should not be reached as we redirect to dashboard
  return (
    <div className="flex items-center justify-center min-h-screen bg-gray-50">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <p className="text-gray-600">Redirecting to dashboard...</p>
      </div>
    </div>
  )
}

export default function ShopifyEmbeddedApp() {
  return (
    <Suspense fallback={
      <div className="flex items-center justify-center min-h-screen bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading Biypod Customizer...</p>
        </div>
      </div>
    }>
      <ShopifyEmbeddedAppContent />
    </Suspense>
  )
}

// Declare global types for Shopify App Bridge
declare global {
  interface Window {
    shopifyAppBridge: any
  }
}
