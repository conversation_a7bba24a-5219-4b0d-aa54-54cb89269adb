import type { Metadata, Viewport } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { AuthProvider } from "@/components/providers/auth-provider";
import ConditionalLayout from "@/components/layout/conditional-layout";
import SafariViewportFix from "@/components/layout/safari-viewport-fix";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
});

export const viewport: Viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 1,
  userScalable: false,
  viewportFit: 'cover',
}

export const metadata: Metadata = {
  title: "Biypod Customizer - Print-on-Demand Shopify App",
  description: "Create and customize products with our intuitive design tools. Perfect for Shopify merchants looking to offer personalized products.",
  keywords: ["print-on-demand", "shopify", "customization", "design", "products"],
  authors: [{ name: "Biypod Team" }],
  openGraph: {
    title: "Biypod Customizer",
    description: "Create and customize products with our intuitive design tools",
    type: "website",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className={inter.variable}>
      <body className="font-sans antialiased bg-white text-gray-900">
        <SafariViewportFix />
        <AuthProvider>
          <ConditionalLayout>
            {children}
          </ConditionalLayout>
        </AuthProvider>
      </body>
    </html>
  );
}
