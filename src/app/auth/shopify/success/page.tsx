'use client'

import { useEffect, useState, Suspense } from 'react'
import { useSearchParams, useRouter } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { CheckCircle, ExternalLink, Mail, Settings } from 'lucide-react'

function ShopifySuccessContent() {
  const [isLoading, setIsLoading] = useState(true)
  const searchParams = useSearchParams()
  const router = useRouter()
  
  const shop = searchParams.get('shop')
  const userId = searchParams.get('user_id')
  const existing = searchParams.get('existing') === 'true'

  useEffect(() => {
    // Simulate loading time for better UX
    const timer = setTimeout(() => {
      setIsLoading(false)
    }, 2000)

    return () => clearTimeout(timer)
  }, [])

  const handleContinue = () => {
    if (existing) {
      // For existing merchants, redirect to sign in
      router.push('/auth/signin')
    } else {
      // For new merchants, they need to check email and sign in
      router.push('/auth/signin')
    }
  }

  const openShopifyAdmin = () => {
    if (shop) {
      window.open(`https://${shop}/admin`, '_blank')
    }
  }

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 via-white to-purple-50">
        <Card className="w-full max-w-md">
          <CardContent className="flex flex-col items-center justify-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mb-4"></div>
            <p className="text-gray-600">Setting up your account...</p>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 via-white to-purple-50 px-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="flex justify-center mb-4">
            <div className="h-16 w-16 rounded-full bg-green-100 flex items-center justify-center">
              <CheckCircle className="h-8 w-8 text-green-600" />
            </div>
          </div>
          <CardTitle className="text-2xl font-bold text-green-800">
            {existing ? 'Welcome Back!' : 'Installation Complete!'}
          </CardTitle>
          <CardDescription>
            {existing 
              ? 'Your Biypod Customizer app has been updated successfully.'
              : 'Biypod Customizer has been installed to your Shopify store.'
            }
          </CardDescription>
        </CardHeader>
        
        <CardContent className="space-y-6">
          <div className="bg-blue-50 p-4 rounded-lg">
            <h3 className="font-semibold text-blue-800 mb-2">What's Next?</h3>
            <ul className="space-y-2 text-sm text-blue-700">
              {existing ? (
                <>
                  <li className="flex items-start space-x-2">
                    <span className="text-blue-600 mt-0.5">•</span>
                    <span>Sign in to your Biypod Customizer account</span>
                  </li>
                  <li className="flex items-start space-x-2">
                    <span className="text-blue-600 mt-0.5">•</span>
                    <span>Your app permissions have been updated</span>
                  </li>
                </>
              ) : (
                <>
                  <li className="flex items-start space-x-2">
                    <span className="text-blue-600 mt-0.5">•</span>
                    <span>Check your email for account credentials</span>
                  </li>
                  <li className="flex items-start space-x-2">
                    <span className="text-blue-600 mt-0.5">•</span>
                    <span>Sign in to configure your product catalog</span>
                  </li>
                  <li className="flex items-start space-x-2">
                    <span className="text-blue-600 mt-0.5">•</span>
                    <span>Add the customizer to your product pages</span>
                  </li>
                </>
              )}
            </ul>
          </div>

          {shop && (
            <div className="bg-gray-50 p-4 rounded-lg">
              <h4 className="font-medium text-gray-800 mb-2">Store Information</h4>
              <p className="text-sm text-gray-600">
                <strong>Shop:</strong> {shop}
              </p>
            </div>
          )}

          <div className="space-y-3">
            <Button 
              onClick={handleContinue}
              className="w-full"
            >
              <Mail className="h-4 w-4 mr-2" />
              Continue to Sign In
            </Button>
            
            {shop && (
              <Button 
                variant="outline"
                onClick={openShopifyAdmin}
                className="w-full"
              >
                <ExternalLink className="h-4 w-4 mr-2" />
                Open Shopify Admin
              </Button>
            )}
          </div>

          <div className="text-center">
            <p className="text-sm text-gray-600 mb-2">
              Need help getting started?
            </p>
            <div className="flex justify-center space-x-4 text-sm">
              <a 
                href="/docs" 
                className="text-blue-600 hover:text-blue-500"
              >
                Documentation
              </a>
              <a 
                href="/contact" 
                className="text-blue-600 hover:text-blue-500"
              >
                Contact Support
              </a>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

export default function ShopifySuccessPage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <ShopifySuccessContent />
    </Suspense>
  )
}
