'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { createSupabaseBrowserClient } from '@/lib/supabase'
import { Card, CardContent } from '@/components/ui/card'

export default function SignOutPage() {
  const router = useRouter()
  const supabase = createSupabaseBrowserClient()

  useEffect(() => {
    const signOut = async () => {
      await supabase.auth.signOut()
      
      // Small delay for better UX
      setTimeout(() => {
        router.push('/auth/signin')
      }, 1500)
    }

    signOut()
  }, [router, supabase.auth])

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 via-white to-purple-50">
      <Card className="w-full max-w-md">
        <CardContent className="flex flex-col items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mb-4"></div>
          <p className="text-gray-600">Signing you out...</p>
        </CardContent>
      </Card>
    </div>
  )
}
