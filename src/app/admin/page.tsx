import { Suspense } from 'react'
import { requireSuperAdmin } from '@/lib/auth'
import SuperAdminLayout from '@/components/admin/super-admin-layout'
import SuperAdminOverview from '@/components/admin/super-admin-overview'

export default async function AdminPage() {
  // Require super admin access
  const user = await requireSuperAdmin()

  return (
    <SuperAdminLayout user={user}>
      <Suspense fallback={
        <div className="flex items-center justify-center min-h-screen">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        </div>
      }>
        <SuperAdminOverview />
      </Suspense>
    </SuperAdminLayout>
  )
}
