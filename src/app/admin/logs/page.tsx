import { Suspense } from 'react'
import { requireSuperAdmin } from '@/lib/auth'
import SuperAdminLayout from '@/components/admin/super-admin-layout'
import ActivityLogsTable from '@/components/admin/activity-logs-table'

async function getActivityLogs() {
  try {
    // Use the same service client that logActivity uses
    const { createSupabaseServiceClient } = await import('@/lib/supabase-server')
    const supabase = createSupabaseServiceClient()

    const { data: logs, error } = await supabase
      .from('activity_logs')
      .select('*')
      .order('created_at', { ascending: false })
      .limit(100)

    if (error) {
      console.error('Failed to fetch activity logs:', error)
      return []
    }

  // Now let's try to get the related data separately to avoid join issues
  if (logs && logs.length > 0) {
    // Get unique user IDs and merchant IDs, filtering out nulls
    const userIds = [...new Set(logs.map(log => log.user_id).filter(Boolean))] as string[]
    const merchantIds = [...new Set(logs.map(log => log.merchant_id).filter(Bo<PERSON>an))] as string[]

    // Fetch users and merchants separately (only if we have IDs)
    let users = null
    let merchants = null

    if (userIds.length > 0) {
      const { data: usersData } = await supabase
        .from('users')
        .select('id, email, role')
        .in('id', userIds)
      users = usersData
    }

    if (merchantIds.length > 0) {
      const { data: merchantsData } = await supabase
        .from('merchants')
        .select('id, shop_name, shop_domain')
        .in('id', merchantIds)
      merchants = merchantsData
    }

    // Combine the data
    const enrichedLogs = logs.map(log => ({
      ...log,
      users: users?.find(u => u.id === log.user_id) || null,
      merchants: merchants?.find(m => m.id === log.merchant_id) || null
    }))

    return enrichedLogs
  }

  // If no logs, return empty array with proper structure
  return logs?.map(log => ({
    ...log,
    users: null,
    merchants: null
  })) || []

  } catch (error) {
    console.error('Error fetching activity logs:', error)
    return []
  }
}

export default async function AdminLogsPage() {
  const user = await requireSuperAdmin()
  const logs = await getActivityLogs()

  return (
    <SuperAdminLayout user={user}>
      <div>
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">System Logs</h1>
          <p className="text-gray-600">Monitor system activity and troubleshoot issues</p>
        </div>



        <Suspense fallback={
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8">
            <div className="animate-pulse">
              <div className="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
              <div className="space-y-3">
                <div className="h-4 bg-gray-200 rounded"></div>
                <div className="h-4 bg-gray-200 rounded w-5/6"></div>
                <div className="h-4 bg-gray-200 rounded w-4/6"></div>
              </div>
            </div>
          </div>
        }>
          <ActivityLogsTable logs={logs} />
        </Suspense>
      </div>
    </SuperAdminLayout>
  )
}
