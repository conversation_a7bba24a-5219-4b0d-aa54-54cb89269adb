export default function ApiDocsPage() {
  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-8">API Documentation</h1>
          <p className="text-xl text-gray-600">Complete reference for the Biypod Customizer API</p>
        </div>
        
        <div className="bg-white rounded-lg shadow-lg p-8">
          <h2 className="text-2xl font-semibold text-gray-800 mb-6">Getting Started</h2>
          <p className="text-gray-600 mb-8">
            The Biypod Customizer API allows you to integrate our customization tools into your applications.
          </p>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div>
              <h3 className="text-xl font-semibold text-gray-800 mb-4">Authentication</h3>
              <div className="bg-gray-100 rounded-lg p-4 mb-4">
                <code className="text-sm">
                  Authorization: Bearer YOUR_API_KEY
                </code>
              </div>
              
              <h3 className="text-xl font-semibold text-gray-800 mb-4">Base URL</h3>
              <div className="bg-gray-100 rounded-lg p-4">
                <code className="text-sm">
                  https://api.biypod.com/v1
                </code>
              </div>
            </div>
            
            <div>
              <h3 className="text-xl font-semibold text-gray-800 mb-4">Endpoints</h3>
              <ul className="space-y-2 text-gray-600">
                <li><code className="bg-gray-100 px-2 py-1 rounded">GET /products</code> - List products</li>
                <li><code className="bg-gray-100 px-2 py-1 rounded">POST /products</code> - Create product</li>
                <li><code className="bg-gray-100 px-2 py-1 rounded">GET /orders</code> - List orders</li>
                <li><code className="bg-gray-100 px-2 py-1 rounded">POST /customizations</code> - Create customization</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
