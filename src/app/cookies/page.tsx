export default function CookiesPage() {
  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-8">Cookie Policy</h1>
          <p className="text-xl text-gray-600">How we use cookies on our website</p>
        </div>
        
        <div className="bg-white rounded-lg shadow-lg p-8">
          <div className="prose max-w-none">
            <h2 className="text-2xl font-semibold text-gray-800 mb-4">What are cookies?</h2>
            <p className="text-gray-600 mb-6">
              Cookies are small text files that are stored on your device when you visit our website. 
              They help us provide you with a better experience by remembering your preferences and settings.
            </p>
            
            <h2 className="text-2xl font-semibold text-gray-800 mb-4">How we use cookies</h2>
            <ul className="list-disc list-inside text-gray-600 mb-6 space-y-2">
              <li>Essential cookies for website functionality</li>
              <li>Analytics cookies to understand how you use our site</li>
              <li>Preference cookies to remember your settings</li>
              <li>Authentication cookies to keep you logged in</li>
            </ul>
            
            <h2 className="text-2xl font-semibold text-gray-800 mb-4">Managing cookies</h2>
            <p className="text-gray-600 mb-6">
              You can control and manage cookies through your browser settings. 
              Please note that disabling certain cookies may affect the functionality of our website.
            </p>
            
            <h2 className="text-2xl font-semibold text-gray-800 mb-4">Contact us</h2>
            <p className="text-gray-600">
              If you have any questions about our use of cookies, please contact <NAME_EMAIL>.
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}
