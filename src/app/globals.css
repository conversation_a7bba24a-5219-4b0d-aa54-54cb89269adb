@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

/* Customizer Grid Pattern */
.bg-grid-pattern {
  background-image:
    linear-gradient(rgba(0, 0, 0, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(0, 0, 0, 0.1) 1px, transparent 1px);
  background-size: 20px 20px;
}

/* Enhanced Mobile Safari safe area support for iPhone */
/* Primary safe area support with env() */
@supports (padding: env(safe-area-inset-bottom)) {
  .pb-safe-bottom {
    padding-bottom: env(safe-area-inset-bottom);
  }

  .pt-safe-top {
    padding-top: env(safe-area-inset-top);
  }

  .pl-safe-left {
    padding-left: env(safe-area-inset-left);
  }

  .pr-safe-right {
    padding-right: env(safe-area-inset-right);
  }

  /* Additional safe area utilities */
  .mb-safe-bottom {
    margin-bottom: env(safe-area-inset-bottom);
  }

  .mt-safe-top {
    margin-top: env(safe-area-inset-top);
  }

  .ml-safe-left {
    margin-left: env(safe-area-inset-left);
  }

  .mr-safe-right {
    margin-right: env(safe-area-inset-right);
  }

  /* Safe area for full-height containers */
  .min-h-screen-safe {
    min-height: 100vh;
    min-height: calc(100vh - env(safe-area-inset-top) - env(safe-area-inset-bottom));
  }

  .h-screen-safe {
    height: 100vh;
    height: calc(100vh - env(safe-area-inset-top) - env(safe-area-inset-bottom));
  }
}

/* Fallback for devices that don't support env() but support max() */
@supports (padding: max(0px)) and (not (padding: env(safe-area-inset-bottom))) {
  .pb-safe-bottom {
    padding-bottom: 3rem;
  }

  .mb-safe-bottom {
    margin-bottom: 3rem;
  }
}

/* Additional fallback for older mobile browsers */
@media screen and (max-width: 768px) {
  .pb-safe-bottom {
    padding-bottom: 3rem !important;
  }

  .mb-safe-bottom {
    margin-bottom: 3rem !important;
  }
}

/* Ensure mobile browsers don't zoom on input focus */
@media screen and (max-width: 768px) {
  input[type="text"],
  input[type="email"],
  input[type="password"],
  textarea,
  select {
    font-size: 16px !important;
  }
}

/* Mobile responsive layout - Next.js best practices */
@media screen and (max-width: 767px) {
  /* Only apply to customizer pages */
  .customizer-container {
    /* Use dynamic viewport height for better mobile support */
    height: 100vh;
    height: 100dvh;
    overflow: hidden;
  }

  /* Prevent zoom on input focus */
  input, textarea, select {
    font-size: 16px !important;
    -webkit-appearance: none;
    border-radius: 0;
  }

  /* Prevent overscroll bounce */
  body {
    overscroll-behavior: none;
    -webkit-overflow-scrolling: touch;
  }
}

/* Prevent mobile browser UI from affecting layout */
html {
  height: 100%;
  height: -webkit-fill-available;
}

body {
  min-height: 100vh;
  min-height: -webkit-fill-available;
  /* Enhanced safe area support for iPhone Safari */
  padding-bottom: env(safe-area-inset-bottom);
}

/* Specific iPhone Safari fixes */
@media screen and (max-width: 768px) {
  /* Force minimum bottom padding on mobile devices */
  body {
    padding-bottom: 3rem !important;
  }

  /* Additional iPhone Safari specific fixes */
  @supports (padding: env(safe-area-inset-bottom)) {
    body {
      padding-bottom: env(safe-area-inset-bottom) !important;
    }
  }
}

/* Enhanced safe area support with JavaScript calculated values */
.ios-safari-safe-area-active {
  /* Use JavaScript-calculated safe area when available */
  --safe-area-bottom: var(--calculated-safe-area-bottom, 3rem);
}

.ios-safari-safe-area-active body {
  padding-bottom: var(--safe-area-bottom) !important;
}

.ios-safari-safe-area-active .pb-safe-bottom {
  padding-bottom: var(--safe-area-bottom) !important;
}

.ios-safari-safe-area-active .mb-safe-bottom {
  margin-bottom: var(--safe-area-bottom) !important;
}

/* Force safe area on all main containers for iOS Safari */
.ios-safari-safe-area-active main,
.ios-safari-safe-area-active .min-h-screen,
.ios-safari-safe-area-active [class*="min-h-screen"] {
  padding-bottom: var(--safe-area-bottom) !important;
}

/* Ultimate fallback for iPhone Safari - CSS only solution */
@media screen and (max-width: 768px) and (-webkit-min-device-pixel-ratio: 2) {
  /* Target high-DPI mobile devices (likely iPhones) */
  body {
    padding-bottom: 4rem !important; /* Even more generous fallback */
  }

  .pb-safe-bottom {
    padding-bottom: 4rem !important;
  }

  .mb-safe-bottom {
    margin-bottom: 4rem !important;
  }
}

/* iPhone X and newer specific targeting */
@media screen and (max-width: 768px) and (min-height: 812px) and (-webkit-min-device-pixel-ratio: 3) {
  /* iPhone X, XS, 11 Pro, 12 mini, 13 mini */
  body {
    padding-bottom: 60px !important; /* Increased from 34px */
  }

  .pb-safe-bottom {
    padding-bottom: 60px !important;
  }

  .mb-safe-bottom {
    margin-bottom: 60px !important;
  }
}

@media screen and (max-width: 768px) and (min-height: 896px) and (-webkit-min-device-pixel-ratio: 2) {
  /* iPhone XR, 11, 12, 13, 14 */
  body {
    padding-bottom: 60px !important;
  }

  .pb-safe-bottom {
    padding-bottom: 60px !important;
  }

  .mb-safe-bottom {
    margin-bottom: 60px !important;
  }
}
