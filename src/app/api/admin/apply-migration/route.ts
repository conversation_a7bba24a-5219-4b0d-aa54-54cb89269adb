import { NextRequest, NextResponse } from 'next/server'
import { createSupabaseServerClient } from '@/lib/supabase-server'

export async function POST(request: NextRequest) {
  try {
    // Check for super admin authorization
    const supabase = await createSupabaseServerClient()

    // Get current user
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized - No valid session' },
        { status: 401 }
      )
    }

    // Check if user is super admin
    const { data: userProfile, error: profileError } = await supabase
      .from('users')
      .select('role')
      .eq('id', user.id)
      .single()

    if (profileError || !userProfile || userProfile.role !== 'super_admin') {
      return NextResponse.json(
        { error: 'Unauthorized - Super admin access required' },
        { status: 403 }
      )
    }

    // Use service role key to execute SQL
    const { createSupabaseServiceClient } = await import('@/lib/supabase-server')
    const serviceSupabase = createSupabaseServiceClient()

    // Execute the migration SQL directly - including merchant constraint fixes
    const migrationSQL = `
      -- Fix 1: Remove unique constraint on user_id to allow multiple shops per user
      ALTER TABLE public.merchants DROP CONSTRAINT IF EXISTS merchants_user_id_key;

      -- Fix 2: Update log_activity function to handle string parameters
      DROP FUNCTION IF EXISTS log_activity(UUID, UUID, TEXT, TEXT, UUID, JSONB);

      CREATE OR REPLACE FUNCTION log_activity(
          p_user_id TEXT DEFAULT NULL,
          p_merchant_id TEXT DEFAULT NULL,
          p_action TEXT,
          p_resource_type TEXT,
          p_resource_id TEXT DEFAULT NULL,
          p_details JSONB DEFAULT '{}'::jsonb
      )
      RETURNS UUID AS $$
      DECLARE
          log_id UUID;
          final_user_id UUID;
          final_merchant_id UUID;
          final_resource_id UUID;
      BEGIN
          -- Convert empty strings to NULL and validate UUIDs
          IF p_user_id IS NOT NULL AND p_user_id != '' THEN
              BEGIN
                  final_user_id := p_user_id::UUID;
              EXCEPTION WHEN invalid_text_representation THEN
                  final_user_id := NULL;
              END;
          ELSE
              final_user_id := NULL;
          END IF;

          IF p_merchant_id IS NOT NULL AND p_merchant_id != '' THEN
              BEGIN
                  final_merchant_id := p_merchant_id::UUID;
              EXCEPTION WHEN invalid_text_representation THEN
                  final_merchant_id := NULL;
              END;
          ELSE
              final_merchant_id := NULL;
          END IF;

          IF p_resource_id IS NOT NULL AND p_resource_id != '' THEN
              BEGIN
                  final_resource_id := p_resource_id::UUID;
              EXCEPTION WHEN invalid_text_representation THEN
                  final_resource_id := NULL;
              END;
          ELSE
              final_resource_id := NULL;
          END IF;

          -- Insert the activity log
          INSERT INTO public.activity_logs (
              user_id,
              merchant_id,
              action,
              resource_type,
              resource_id,
              details
          ) VALUES (
              final_user_id,
              final_merchant_id,
              p_action,
              p_resource_type,
              final_resource_id,
              p_details
          ) RETURNING id INTO log_id;

          RETURN log_id;
      END;
      $$ LANGUAGE plpgsql SECURITY DEFINER;

      -- Create a function to check if the request is from our application
      CREATE OR REPLACE FUNCTION is_system_request()
      RETURNS BOOLEAN AS $$
      BEGIN
          RETURN current_setting('request.jwt.claims', true)::json->>'role' = 'service_role'
                 OR current_setting('request.jwt.claims', true)::json->>'iss' = 'supabase';
      END;
      $$ LANGUAGE plpgsql SECURITY DEFINER;

      -- Allow system to create merchants without authentication
      DROP POLICY IF EXISTS "System can create merchants" ON public.merchants;
      CREATE POLICY "System can create merchants" ON public.merchants
          FOR INSERT WITH CHECK (is_system_request());

      -- Allow system to create users without authentication
      DROP POLICY IF EXISTS "System can create users" ON public.users;
      CREATE POLICY "System can create users" ON public.users
          FOR INSERT WITH CHECK (is_system_request());

      -- Allow system to create user profiles without authentication
      DROP POLICY IF EXISTS "System can create user profiles" ON public.user_profiles;
      CREATE POLICY "System can create user profiles" ON public.user_profiles
          FOR INSERT WITH CHECK (is_system_request());

      -- Allow system to read merchants by shop domain
      DROP POLICY IF EXISTS "System can read merchants by shop domain" ON public.merchants;
      CREATE POLICY "System can read merchants by shop domain" ON public.merchants
          FOR SELECT USING (is_system_request());

      -- Allow system to read users for merchant creation
      DROP POLICY IF EXISTS "System can read users for merchant creation" ON public.users;
      CREATE POLICY "System can read users for merchant creation" ON public.users
          FOR SELECT USING (is_system_request());
    `

    // Execute the SQL - exec_sql function not available in current database schema
    // This would need to be implemented as a database function first
    const error = new Error('exec_sql function not implemented in database schema')

    if (error) {
      console.error('Error executing migration SQL:', error)
      return NextResponse.json(
        { 
          success: false,
          error: 'Failed to execute migration',
          details: error.message 
        },
        { status: 500 }
      )
    }

    // Now create the function
    const functionSQL = `
      CREATE OR REPLACE FUNCTION create_merchant_with_user(
          p_shop_domain TEXT,
          p_shop_name TEXT,
          p_access_token TEXT DEFAULT NULL,
          p_email TEXT DEFAULT NULL
      )
      RETURNS JSON AS $$
      DECLARE
          v_user_id UUID;
          v_merchant_id UUID;
          v_result JSON;
      BEGIN
          IF p_email IS NULL THEN
              p_email := p_shop_domain || '@merchant.biypod.com';
          END IF;

          SELECT id INTO v_merchant_id 
          FROM public.merchants 
          WHERE shop_domain = p_shop_domain;
          
          IF v_merchant_id IS NOT NULL THEN
              SELECT json_build_object(
                  'success', true,
                  'message', 'Merchant already exists',
                  'merchant', row_to_json(m.*)
              ) INTO v_result
              FROM public.merchants m
              WHERE m.id = v_merchant_id;
              
              RETURN v_result;
          END IF;

          INSERT INTO public.users (id, email, role)
          VALUES (gen_random_uuid(), p_email, 'merchant')
          RETURNING id INTO v_user_id;

          INSERT INTO public.user_profiles (user_id, first_name, last_name, company)
          VALUES (v_user_id, 'Merchant', 'User', p_shop_name);

          INSERT INTO public.merchants (
              user_id, shop_domain, shop_name, access_token,
              subscription_tier, subscription_status, settings
          ) VALUES (
              v_user_id, p_shop_domain, p_shop_name,
              COALESCE(p_access_token, 'pending-' || extract(epoch from now())::text),
              'free', 'active',
              '{"customizer_enabled": true, "auto_fulfill": false}'::jsonb
          ) RETURNING id INTO v_merchant_id;

          SELECT json_build_object(
              'success', true,
              'message', 'Merchant created successfully',
              'merchant', row_to_json(m.*)
          ) INTO v_result
          FROM public.merchants m
          WHERE m.id = v_merchant_id;

          RETURN v_result;
      EXCEPTION
          WHEN OTHERS THEN
              RETURN json_build_object(
                  'success', false,
                  'error', 'Failed to create merchant',
                  'details', SQLERRM
              );
      END;
      $$ LANGUAGE plpgsql SECURITY DEFINER;

      GRANT EXECUTE ON FUNCTION create_merchant_with_user TO anon, authenticated;
      GRANT EXECUTE ON FUNCTION is_system_request TO anon, authenticated;
    `

    // exec_sql function not available in current database schema
    const functionError = new Error('exec_sql function not implemented in database schema')

    if (functionError) {
      console.error('Error creating function:', functionError)
      return NextResponse.json(
        { 
          success: false,
          error: 'Failed to create function',
          details: functionError.message 
        },
        { status: 500 }
      )
    }

    console.log('Migration completed successfully - applied merchant constraint fixes')

    return NextResponse.json({
      success: true,
      message: 'Migration applied successfully',
      fixes_applied: [
        'Removed unique constraint on merchants.user_id (allows multiple shops per user)',
        'Updated log_activity function to handle string parameters (fixes logging)',
        'Applied original merchant creation policies'
      ]
    })

  } catch (error) {
    console.error('Error in migration API:', error)
    return NextResponse.json(
      { 
        success: false,
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'Migration API',
    usage: 'POST with valid super admin session',
    description: 'Applies database migration for merchant creation'
  })
}
