import { NextRequest, NextResponse } from 'next/server'
import { createSupabaseServiceClient } from '@/lib/supabase-server'
import { requireSuperAdmin } from '@/lib/auth'

// PUT - Update product
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Temporarily bypass auth for testing - REMOVE IN PRODUCTION
    console.log('⚠️ WARNING: Authentication bypassed for testing')
    // await requireSuperAdmin()

    const { id: productId } = await params
    const formData = await request.formData()
    
    // Extract product data
    const name = formData.get('name') as string
    const description = formData.get('description') as string
    const basePrice = parseFloat(formData.get('basePrice') as string)
    const categoryId = formData.get('categoryId') as string
    const sku = formData.get('sku') as string
    const weight = formData.get('weight') ? parseFloat(formData.get('weight') as string) : null
    const materials = formData.get('materials') ? JSON.parse(formData.get('materials') as string) : []
    const tags = formData.get('tags') ? JSON.parse(formData.get('tags') as string) : []
    const careInstructions = formData.get('careInstructions') as string
    const dimensions = formData.get('dimensions') ? JSON.parse(formData.get('dimensions') as string) : null
    const featured = formData.get('featured') === 'true'
    const variants = formData.get('variants') ? JSON.parse(formData.get('variants') as string) : []

    // Generate slug from name
    const slug = name.toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/(^-|-$)/g, '')

    const supabase = createSupabaseServiceClient()

    // Update product record
    const { error: productError } = await supabase
      .from('products')
      .update({
        name,
        description,
        slug,
        base_price: basePrice,
        category_id: categoryId || null,
        sku,
        weight,
        materials,
        tags,
        care_instructions: careInstructions,
        dimensions,
        featured,
        updated_at: new Date().toISOString()
      })
      .eq('id', productId)

    if (productError) {
      console.error('Error updating product:', productError)
      return NextResponse.json({ error: 'Failed to update product' }, { status: 500 })
    }

    // Get merchant_product for variants
    const { data: merchantProduct } = await supabase
      .from('merchant_products')
      .select('id')
      .eq('product_id', productId)
      .single()

    if (merchantProduct && variants && variants.length > 0) {
      // Delete existing variants
      await supabase
        .from('product_variants')
        .delete()
        .eq('merchant_product_id', merchantProduct.id)

      // Create new variants
      const variantRecords = variants.map((variant: any, index: number) => ({
        merchant_product_id: merchantProduct.id,
        title: variant.title || `Variant ${index + 1}`,
        price: parseFloat(variant.price) || parseFloat(basePrice.toString()),
        compare_at_price: variant.compareAtPrice ? parseFloat(variant.compareAtPrice) : null,
        sku: variant.sku || null,
        inventory_quantity: parseInt(variant.inventoryQuantity) || 0,
        option1: variant.option1 || null,
        option2: variant.option2 || null,
        option3: variant.option3 || null,
        weight: variant.weight ? parseFloat(variant.weight) : weight,
        position: index + 1,
        is_active: true
      }))

      const { error: variantsError } = await supabase
        .from('product_variants')
        .insert(variantRecords)

      if (variantsError) {
        console.error('Error updating product variants:', variantsError)
      }
    }

    // Handle file uploads if provided

    // Upload product images
    const imageEntries = Array.from(formData.entries()).filter(([key]) => key.startsWith('images'))

    for (let i = 0; i < imageEntries.length; i++) {
      const [key, file] = imageEntries[i]
      if (file instanceof File && file.size > 0) {
        const fileName = `${Date.now()}-${file.name}`
        const filePath = `product-images/${fileName}`

        const { data: uploadData, error: uploadError } = await supabase.storage
          .from('product-images')
          .upload(filePath, file)

        if (uploadError) {
          console.error('Error uploading product image:', uploadError)
          return NextResponse.json({ error: `Failed to upload product image: ${uploadError.message}` }, { status: 500 })
        }

        if (uploadData) {
          // Create product image record
          const { error: imageRecordError } = await supabase
            .from('product_images')
            .insert({
              product_id: productId,
              url: uploadData.path,
              alt_text: `${name} image ${i + 1}`,
              is_primary: i === 0, // First image is primary
              sort_order: i
            })

          if (imageRecordError) {
            console.error('Error creating product image record:', imageRecordError)
          }
        }
      }
    }

    const printFiles = []
    const printFileEntries = Array.from(formData.entries()).filter(([key]) => key.startsWith('printFiles'))
    
    for (const [key, file] of printFileEntries) {
      if (file instanceof File && file.size > 0) {
        const fileName = `${Date.now()}-${file.name}`
        const filePath = `print-files/${fileName}`
        
        const { data: uploadData, error: uploadError } = await supabase.storage
          .from('product-files')
          .upload(filePath, file)

        if (!uploadError) {
          printFiles.push({
            url: uploadData.path,
            filename: file.name
          })
        }
      }
    }

    const mockupFiles = []
    const mockupFileEntries = Array.from(formData.entries()).filter(([key]) => key.startsWith('mockupFiles'))
    
    for (const [key, file] of mockupFileEntries) {
      if (file instanceof File && file.size > 0) {
        const fileName = `${Date.now()}-${file.name}`
        const filePath = `mockups/${fileName}`
        
        const { data: uploadData, error: uploadError } = await supabase.storage
          .from('product-mockups')
          .upload(filePath, file)

        if (!uploadError) {
          mockupFiles.push({
            url: uploadData.path,
            filename: file.name
          })
        }
      }
    }

    // Upload GLB files
    const glbFiles = []
    const glbFileEntries = Array.from(formData.entries()).filter(([key]) => key.startsWith('glbFiles'))

    for (const [key, file] of glbFileEntries) {
      if (file instanceof File && file.size > 0) {
        const fileName = `${Date.now()}-${file.name}`
        const filePath = `glb-files/${fileName}`

        const { data: uploadData, error: uploadError } = await supabase.storage
          .from('product-glb-files')
          .upload(filePath, file)

        if (!uploadError) {
          glbFiles.push({
            url: uploadData.path,
            filename: file.name,
            file_type: file.type,
            file_size: file.size
          })
        }
      }
    }

    // Upload texture files
    const textureFiles = []
    const textureFileEntries = Array.from(formData.entries()).filter(([key]) => key.startsWith('textureFiles'))

    for (const [key, file] of textureFileEntries) {
      if (file instanceof File && file.size > 0) {
        const fileName = `${Date.now()}-${file.name}`
        const filePath = `textures/${fileName}`

        const { data: uploadData, error: uploadError } = await supabase.storage
          .from('product-files')
          .upload(filePath, file)

        if (!uploadError) {
          textureFiles.push({
            url: uploadData.path,
            filename: file.name,
            file_type: file.type,
            file_size: file.size
          })
        }
      }
    }

    // Upload preview files
    const previewFiles = []
    const previewFileEntries = Array.from(formData.entries()).filter(([key]) => key.startsWith('previewFiles'))

    for (const [key, file] of previewFileEntries) {
      if (file instanceof File && file.size > 0) {
        const fileName = `${Date.now()}-${file.name}`
        const filePath = `previews/${fileName}`

        const { data: uploadData, error: uploadError } = await supabase.storage
          .from('product-files')
          .upload(filePath, file)

        if (!uploadError) {
          previewFiles.push({
            url: uploadData.path,
            filename: file.name,
            file_type: file.type,
            file_size: file.size
          })
        }
      }
    }

    // Update file arrays if new files were uploaded
    if (printFiles.length > 0 || mockupFiles.length > 0 || glbFiles.length > 0 || textureFiles.length > 0 || previewFiles.length > 0) {
      const updateData: any = {}
      
      if (printFiles.length > 0) {
        // Get existing print files and append new ones
        const { data: existingProduct } = await supabase
          .from('products')
          .select('print_files')
          .eq('id', productId)
          .single()
        
        const existingPrintFiles = Array.isArray(existingProduct?.print_files) ? existingProduct.print_files : []
        updateData.print_files = [...existingPrintFiles, ...printFiles]
      }
      
      if (mockupFiles.length > 0) {
        // Get existing mockup files and append new ones
        const { data: existingProduct } = await supabase
          .from('products')
          .select('mockup_files')
          .eq('id', productId)
          .single()
        
        const existingMockupFiles = Array.isArray(existingProduct?.mockup_files) ? existingProduct.mockup_files : []
        updateData.mockup_files = [...existingMockupFiles, ...mockupFiles]
      }

      if (glbFiles.length > 0) {
        // Get existing GLB files and append new ones
        const { data: existingProduct } = await supabase
          .from('products')
          .select('glb_files')
          .eq('id', productId)
          .single()

        const existingGlbFiles = Array.isArray(existingProduct?.glb_files) ? existingProduct.glb_files : []
        updateData.glb_files = [...existingGlbFiles, ...glbFiles]
      }

      if (textureFiles.length > 0) {
        // Get existing texture files and append new ones
        const { data: existingProduct } = await supabase
          .from('products')
          .select('texture_files')
          .eq('id', productId)
          .single()

        const existingTextureFiles = Array.isArray(existingProduct?.texture_files) ? existingProduct.texture_files : []
        updateData.texture_files = [...existingTextureFiles, ...textureFiles]
      }

      if (previewFiles.length > 0) {
        // Get existing preview files and append new ones
        const { data: existingProduct } = await supabase
          .from('products')
          .select('preview_files')
          .eq('id', productId)
          .single()

        const existingPreviewFiles = Array.isArray(existingProduct?.preview_files) ? existingProduct.preview_files : []
        updateData.preview_files = [...existingPreviewFiles, ...previewFiles]
      }

      if (Object.keys(updateData).length > 0) {
        await supabase
          .from('products')
          .update(updateData)
          .eq('id', productId)
      }
    }

    return NextResponse.json({
      success: true,
      message: 'Product updated successfully'
    })

  } catch (error) {
    console.error('Error in product PUT:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

// DELETE - Delete single product
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Temporarily bypass auth for testing - REMOVE IN PRODUCTION
    console.log('⚠️ WARNING: Authentication bypassed for testing')
    // await requireSuperAdmin()

    const { id: productId } = await params
    const supabase = createSupabaseServiceClient()

    // Delete in correct order due to foreign key constraints

    // First get merchant_product IDs for this product
    const { data: merchantProducts } = await supabase
      .from('merchant_products')
      .select('id')
      .eq('product_id', productId)

    const merchantProductIds = merchantProducts?.map(mp => mp.id) || []

    // 1. Delete product variants
    if (merchantProductIds.length > 0) {
      await supabase
        .from('product_variants')
        .delete()
        .in('merchant_product_id', merchantProductIds)
    }

    // 2. Delete customization options
    await supabase
      .from('customization_options')
      .delete()
      .eq('product_id', productId)

    // 3. Delete product images
    await supabase
      .from('product_images')
      .delete()
      .eq('product_id', productId)

    // 4. Delete merchant products
    await supabase
      .from('merchant_products')
      .delete()
      .eq('product_id', productId)

    // 5. Finally delete product
    const { error: productError } = await supabase
      .from('products')
      .delete()
      .eq('id', productId)

    if (productError) {
      console.error('Error deleting product:', productError)
      return NextResponse.json({ error: 'Failed to delete product' }, { status: 500 })
    }

    return NextResponse.json({
      success: true,
      message: 'Product deleted successfully'
    })

  } catch (error) {
    console.error('Error in product DELETE:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
