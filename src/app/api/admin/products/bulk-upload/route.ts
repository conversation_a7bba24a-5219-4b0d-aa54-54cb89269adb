import { NextRequest, NextResponse } from 'next/server'
import { createSupabaseServiceClient } from '@/lib/supabase-server'
import { requireSuperAdmin } from '@/lib/auth'

interface FileMapping {
  glbName: string
  previewName?: string
  textureName?: string
}

// POST - Bulk upload product with folder structure
export async function POST(request: NextRequest) {
  try {
    // Temporarily bypass auth for testing - REMOVE IN PRODUCTION
    console.log('⚠️ WARNING: Authentication bypassed for testing')
    // await requireSuperAdmin()
    
    const formData = await request.formData()
    
    // Extract product data
    const name = formData.get('name') as string
    const description = formData.get('description') as string || ''
    const basePrice = parseFloat(formData.get('basePrice') as string)
    const categoryId = formData.get('categoryId') as string || null
    const sku = formData.get('sku') as string || ''
    const weight = formData.get('weight') ? parseFloat(formData.get('weight') as string) : null
    const materials = formData.get('materials') as string || ''
    const tags = formData.get('tags') as string || ''
    const careInstructions = formData.get('careInstructions') as string || ''
    const dimensions = formData.get('dimensions') as string || ''
    const featured = formData.get('featured') === 'true'

    // Parse file mappings and product images order
    const fileMappings: FileMapping[] = formData.get('fileMappings') 
      ? JSON.parse(formData.get('fileMappings') as string) 
      : []
    const productImagesOrder: string[] = formData.get('productImagesOrder')
      ? JSON.parse(formData.get('productImagesOrder') as string)
      : []

    // Generate slug from name
    const slug = name.toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/(^-|-$)/g, '')

    const supabase = createSupabaseServiceClient()

    // Upload GLB files
    interface FileInfo {
      url: string
      filename: string
      file_type: string
      file_size: number
      original_name: string
    }
    const glbFiles: FileInfo[] = []
    const glbFileEntries = Array.from(formData.entries()).filter(([key]) => key.startsWith('glbFiles'))
    
    for (const [key, file] of glbFileEntries) {
      if (file instanceof File && file.size > 0) {
        const fileName = `${Date.now()}-${file.name}`
        const filePath = `glb-files/${fileName}`
        
        const { data: uploadData, error: uploadError } = await supabase.storage
          .from('product-glb-files')
          .upload(filePath, file)

        if (uploadError) {
          console.error('Error uploading GLB file:', uploadError)
          return NextResponse.json({ error: `Failed to upload GLB file: ${uploadError.message}` }, { status: 500 })
        }

        glbFiles.push({
          url: uploadData.path,
          filename: file.name,
          file_type: file.type,
          file_size: file.size,
          original_name: file.name
        })
      }
    }

    // Upload print files
    const printFiles = []
    const printFileEntries = Array.from(formData.entries()).filter(([key]) => key.startsWith('printFiles'))
    
    for (const [key, file] of printFileEntries) {
      if (file instanceof File && file.size > 0) {
        const fileName = `${Date.now()}-${file.name}`
        const filePath = `print-files/${fileName}`
        
        const { data: uploadData, error: uploadError } = await supabase.storage
          .from('product-files')
          .upload(filePath, file)

        if (uploadError) {
          console.error('Error uploading print file:', uploadError)
          return NextResponse.json({ error: `Failed to upload print file: ${uploadError.message}` }, { status: 500 })
        }

        printFiles.push({
          url: uploadData.path,
          filename: file.name,
          file_type: file.type,
          file_size: file.size,
          original_name: file.name
        })
      }
    }

    // Upload texture files
    const textureFiles: FileInfo[] = []
    const textureFileEntries = Array.from(formData.entries()).filter(([key]) => key.startsWith('textureFiles'))
    
    for (const [key, file] of textureFileEntries) {
      if (file instanceof File && file.size > 0) {
        const fileName = `${Date.now()}-${file.name}`
        const filePath = `textures/${fileName}`
        
        const { data: uploadData, error: uploadError } = await supabase.storage
          .from('product-files')
          .upload(filePath, file)

        if (uploadError) {
          console.error('Error uploading texture file:', uploadError)
          return NextResponse.json({ error: `Failed to upload texture file: ${uploadError.message}` }, { status: 500 })
        }

        textureFiles.push({
          url: uploadData.path,
          filename: file.name,
          file_type: file.type,
          file_size: file.size,
          original_name: file.name
        })
      }
    }

    // Upload preview files
    const previewFiles: FileInfo[] = []
    const previewFileEntries = Array.from(formData.entries()).filter(([key]) => key.startsWith('previewFiles'))
    
    for (const [key, file] of previewFileEntries) {
      if (file instanceof File && file.size > 0) {
        const fileName = `${Date.now()}-${file.name}`
        const filePath = `previews/${fileName}`
        
        const { data: uploadData, error: uploadError } = await supabase.storage
          .from('product-files')
          .upload(filePath, file)

        if (uploadError) {
          console.error('Error uploading preview file:', uploadError)
          return NextResponse.json({ error: `Failed to upload preview file: ${uploadError.message}` }, { status: 500 })
        }

        previewFiles.push({
          url: uploadData.path,
          filename: file.name,
          file_type: file.type,
          file_size: file.size,
          original_name: file.name
        })
      }
    }

    // Create enhanced file mappings with uploaded file data
    const enhancedMappings = fileMappings.map(mapping => {
      const glbFile = glbFiles.find(f => f.original_name === mapping.glbName)
      const previewFile = previewFiles.find(f => f.original_name === mapping.previewName)
      const textureFile = textureFiles.find(f => f.original_name === mapping.textureName)

      return {
        glb: glbFile,
        preview: previewFile,
        texture: textureFile,
        name: mapping.glbName.replace('.glb', '')
      }
    })

    // Create product record
    const { data: product, error: productError } = await supabase
      .from('products')
      .insert({
        name,
        description,
        slug,
        base_price: basePrice,
        category_id: categoryId,
        sku,
        weight,
        materials: materials ? materials.split(',').map(m => m.trim()) : [],
        tags: tags ? tags.split(',').map(t => t.trim()) : [],
        care_instructions: careInstructions,
        dimensions,
        featured,
        print_files: printFiles as any,
        glb_files: glbFiles as any,
        texture_files: textureFiles as any,
        preview_files: previewFiles as any,
        is_active: true,
        canvas_config: {
          file_mappings: enhancedMappings,
          product_images_order: productImagesOrder
        } as any
      })
      .select()
      .single()

    if (productError) {
      console.error('Error creating product:', productError)
      return NextResponse.json({ error: 'Failed to create product' }, { status: 500 })
    }

    // Create product images from selected preview files in specified order
    // If no specific order is provided, use the first few preview files as product images
    const imagesToCreate = productImagesOrder.length > 0
      ? productImagesOrder
      : previewFiles.slice(0, 5).map(f => f.original_name) // Use first 5 preview files as fallback

    for (let i = 0; i < imagesToCreate.length; i++) {
      const imageName = imagesToCreate[i]
      const previewFile = previewFiles.find(f => f.original_name === imageName)

      if (previewFile) {
        // Copy preview file to product-images bucket
        const { data: previewData } = await supabase.storage
          .from('product-files')
          .download(previewFile.url)

        if (previewData) {
          const imageFileName = `${Date.now()}-${imageName}`
          const imageFilePath = `product-images/${imageFileName}`

          const { data: imageUploadData, error: imageUploadError } = await supabase.storage
            .from('product-images')
            .upload(imageFilePath, previewData)

          if (!imageUploadError && imageUploadData) {
            await supabase
              .from('product_images')
              .insert({
                product_id: product.id,
                url: imageUploadData.path,
                alt_text: `${name} - ${imageName}`,
                is_primary: i === 0,
                sort_order: i
              })
          }
        }
      }
    }

    return NextResponse.json({
      success: true,
      product,
      summary: {
        glb_files: glbFiles.length,
        print_files: printFiles.length,
        texture_files: textureFiles.length,
        preview_files: previewFiles.length,
        product_images: imagesToCreate.length,
        file_mappings: enhancedMappings.length
      }
    })

  } catch (error) {
    console.error('Error in bulk upload:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
