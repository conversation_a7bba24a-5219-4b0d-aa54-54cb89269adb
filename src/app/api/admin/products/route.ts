import { NextRequest, NextResponse } from 'next/server'
import { createSupabaseServiceClient } from '@/lib/supabase-server'
import { requireSuperAdmin } from '@/lib/auth'

// GET - Fetch all products
export async function GET(request: NextRequest) {
  try {
    // Temporarily bypass auth for testing - REMOVE IN PRODUCTION
    console.log('⚠️ WARNING: Authentication bypassed for testing')
    // await requireSuperAdmin()
    
    const { searchParams } = new URL(request.url)
    const search = searchParams.get('search')
    const categoryId = searchParams.get('categoryId')
    const limit = parseInt(searchParams.get('limit') || '20')
    const offset = parseInt(searchParams.get('offset') || '0')

    const supabase = createSupabaseServiceClient()
    
    let query = supabase
      .from('products')
      .select(`
        *,
        product_images(*),
        product_categories(*),
        merchant_products(
          id,
          product_variants(*)
        )
      `)
      .order('featured', { ascending: false })
      .order('sort_order', { ascending: true })

    if (search) {
      query = query.or(`name.ilike.%${search}%,description.ilike.%${search}%`)
    }

    if (categoryId) {
      query = query.eq('category_id', categoryId)
    }

    query = query.range(offset, offset + limit - 1)

    const { data: products, error } = await query

    if (error) {
      console.error('Error fetching products:', error)
      return NextResponse.json({ error: 'Failed to fetch products' }, { status: 500 })
    }

    // Get total count for pagination
    const { count } = await supabase
      .from('products')
      .select('*', { count: 'exact', head: true })

    return NextResponse.json({
      products,
      total: count || 0,
      limit,
      offset
    })

  } catch (error) {
    console.error('Error in products GET:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

// POST - Create new product
export async function POST(request: NextRequest) {
  try {
    // Temporarily bypass auth for testing - REMOVE IN PRODUCTION
    console.log('⚠️ WARNING: Authentication bypassed for testing')
    // await requireSuperAdmin()
    
    const formData = await request.formData()
    
    // Extract product data
    const name = formData.get('name') as string
    const description = formData.get('description') as string
    const basePrice = parseFloat(formData.get('basePrice') as string)
    const categoryId = formData.get('categoryId') as string
    const sku = formData.get('sku') as string
    const weight = formData.get('weight') ? parseFloat(formData.get('weight') as string) : null
    const materials = formData.get('materials') ? JSON.parse(formData.get('materials') as string) : []
    const tags = formData.get('tags') ? JSON.parse(formData.get('tags') as string) : []
    const careInstructions = formData.get('careInstructions') as string
    const dimensions = formData.get('dimensions') ? JSON.parse(formData.get('dimensions') as string) : null
    const featured = formData.get('featured') === 'true'
    const variants = formData.get('variants') ? JSON.parse(formData.get('variants') as string) : []

    // Generate slug from name
    const slug = name.toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/(^-|-$)/g, '')

    const supabase = createSupabaseServiceClient()

    // Upload print files
    const printFiles = []
    const printFileEntries = Array.from(formData.entries()).filter(([key]) => key.startsWith('printFiles'))
    
    for (const [key, file] of printFileEntries) {
      if (file instanceof File && file.size > 0) {
        const fileName = `${Date.now()}-${file.name}`
        const filePath = `print-files/${fileName}`
        
        const { data: uploadData, error: uploadError } = await supabase.storage
          .from('product-files')
          .upload(filePath, file)

        if (uploadError) {
          console.error('Error uploading print file:', uploadError)
          return NextResponse.json({ error: `Failed to upload print file: ${uploadError.message}` }, { status: 500 })
        }

        printFiles.push({
          url: uploadData.path,
          filename: file.name,
          file_type: file.type,
          file_size: file.size
        })
      }
    }

    // Upload mockup files
    const mockupFiles = []
    const mockupFileEntries = Array.from(formData.entries()).filter(([key]) => key.startsWith('mockupFiles'))
    
    for (const [key, file] of mockupFileEntries) {
      if (file instanceof File && file.size > 0) {
        const fileName = `${Date.now()}-${file.name}`
        const filePath = `mockups/${fileName}`
        
        const { data: uploadData, error: uploadError } = await supabase.storage
          .from('product-mockups')
          .upload(filePath, file)

        if (uploadError) {
          console.error('Error uploading mockup file:', uploadError)
          return NextResponse.json({ error: `Failed to upload mockup file: ${uploadError.message}` }, { status: 500 })
        }

        mockupFiles.push({
          url: uploadData.path,
          filename: file.name,
          file_type: file.type,
          file_size: file.size
        })
      }
    }

    // Upload GLB files
    const glbFiles = []
    const glbFileEntries = Array.from(formData.entries()).filter(([key]) => key.startsWith('glbFiles'))

    for (const [key, file] of glbFileEntries) {
      if (file instanceof File && file.size > 0) {
        const fileName = `${Date.now()}-${file.name}`
        const filePath = `glb-files/${fileName}`

        const { data: uploadData, error: uploadError } = await supabase.storage
          .from('product-glb-files')
          .upload(filePath, file)

        if (uploadError) {
          console.error('Error uploading GLB file:', uploadError)
          return NextResponse.json({ error: `Failed to upload GLB file: ${uploadError.message}` }, { status: 500 })
        }

        glbFiles.push({
          url: uploadData.path,
          filename: file.name,
          file_type: file.type,
          file_size: file.size
        })
      }
    }

    // Upload texture files
    const textureFiles = []
    const textureFileEntries = Array.from(formData.entries()).filter(([key]) => key.startsWith('textureFiles'))

    for (const [key, file] of textureFileEntries) {
      if (file instanceof File && file.size > 0) {
        const fileName = `${Date.now()}-${file.name}`
        const filePath = `textures/${fileName}`

        const { data: uploadData, error: uploadError } = await supabase.storage
          .from('product-files')
          .upload(filePath, file)

        if (uploadError) {
          console.error('Error uploading texture file:', uploadError)
          return NextResponse.json({ error: `Failed to upload texture file: ${uploadError.message}` }, { status: 500 })
        }

        textureFiles.push({
          url: uploadData.path,
          filename: file.name,
          file_type: file.type,
          file_size: file.size
        })
      }
    }

    // Upload preview files
    const previewFiles = []
    const previewFileEntries = Array.from(formData.entries()).filter(([key]) => key.startsWith('previewFiles'))

    for (const [key, file] of previewFileEntries) {
      if (file instanceof File && file.size > 0) {
        const fileName = `${Date.now()}-${file.name}`
        const filePath = `previews/${fileName}`

        const { data: uploadData, error: uploadError } = await supabase.storage
          .from('product-files')
          .upload(filePath, file)

        if (uploadError) {
          console.error('Error uploading preview file:', uploadError)
          return NextResponse.json({ error: `Failed to upload preview file: ${uploadError.message}` }, { status: 500 })
        }

        previewFiles.push({
          url: uploadData.path,
          filename: file.name,
          file_type: file.type,
          file_size: file.size
        })
      }
    }

    // Create product record
    const { data: product, error: productError } = await supabase
      .from('products')
      .insert({
        name,
        description,
        slug,
        base_price: basePrice,
        category_id: categoryId || null,
        sku,
        weight,
        materials,
        tags,
        care_instructions: careInstructions,
        dimensions,
        featured,
        print_files: printFiles,
        mockup_files: mockupFiles,
        glb_files: glbFiles,
        texture_files: textureFiles,
        preview_files: previewFiles,
        is_active: true
      })
      .select()
      .single()

    if (productError) {
      console.error('Error creating product:', productError)
      return NextResponse.json({ error: 'Failed to create product' }, { status: 500 })
    }

    // For platform products, we'll create variants directly linked to a special merchant_product
    // First, let's get or create a "platform" merchant for super admin products
    const { data: { user } } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 })
    }

    // Get the super admin's merchant account or create a platform merchant
    let { data: platformMerchant } = await supabase
      .from('merchants')
      .select('id')
      .eq('user_id', user.id)
      .single()

    if (!platformMerchant) {
      // Create a platform merchant for super admin
      const { data: newMerchant, error: merchantError } = await supabase
        .from('merchants')
        .insert({
          user_id: user.id,
          shop_domain: 'platform.biypod.com',
          shop_name: 'Biypod Platform',
          access_token: 'platform-token',
          subscription_tier: 'enterprise'
        })
        .select()
        .single()

      if (merchantError) {
        console.error('Error creating platform merchant:', merchantError)
        return NextResponse.json({ error: 'Failed to create platform merchant' }, { status: 500 })
      }
      platformMerchant = newMerchant
    }

    // Create merchant_product record
    const { data: merchantProduct, error: merchantProductError } = await supabase
      .from('merchant_products')
      .insert({
        merchant_id: platformMerchant.id,
        product_id: product.id,
        is_published: true,
        published_at: new Date().toISOString()
      })
      .select()
      .single()

    if (merchantProductError) {
      console.error('Error creating merchant product:', merchantProductError)
      return NextResponse.json({ error: 'Failed to create merchant product' }, { status: 500 })
    }

    // Create product variants
    if (variants && variants.length > 0) {
      const variantRecords = variants.map((variant: any, index: number) => ({
        merchant_product_id: merchantProduct.id,
        title: variant.title || `Variant ${index + 1}`,
        price: parseFloat(variant.price) || parseFloat(basePrice.toString()),
        compare_at_price: variant.compareAtPrice ? parseFloat(variant.compareAtPrice) : null,
        sku: variant.sku || null,
        inventory_quantity: parseInt(variant.inventoryQuantity) || 0,
        option1: variant.option1 || null,
        option2: variant.option2 || null,
        option3: variant.option3 || null,
        weight: variant.weight ? parseFloat(variant.weight) : weight,
        position: index + 1,
        is_active: true
      }))

      const { error: variantsError } = await supabase
        .from('product_variants')
        .insert(variantRecords)

      if (variantsError) {
        console.error('Error creating product variants:', variantsError)
        // Don't fail the entire request for variants
      }
    }

    // Upload product images
    const imageEntries = Array.from(formData.entries()).filter(([key]) => key.startsWith('images'))
    
    for (let i = 0; i < imageEntries.length; i++) {
      const [key, file] = imageEntries[i]
      if (file instanceof File && file.size > 0) {
        const fileName = `${Date.now()}-${file.name}`
        const filePath = `product-images/${fileName}`
        
        const { data: uploadData, error: uploadError } = await supabase.storage
          .from('product-images')
          .upload(filePath, file)

        if (!uploadError) {
          // Create product image record
          await supabase
            .from('product_images')
            .insert({
              product_id: product.id,
              url: uploadData.path,
              alt_text: `${name} image ${i + 1}`,
              is_primary: i === 0,
              sort_order: i
            })
        }
      }
    }

    return NextResponse.json({
      success: true,
      product
    })

  } catch (error) {
    console.error('Error in products POST:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

// DELETE - Delete products (single or bulk)
export async function DELETE(request: NextRequest) {
  try {
    // Temporarily bypass auth for testing - REMOVE IN PRODUCTION
    console.log('⚠️ WARNING: Authentication bypassed for testing')
    // await requireSuperAdmin()

    const { searchParams } = new URL(request.url)
    const productIds = searchParams.get('ids')?.split(',') || []

    if (productIds.length === 0) {
      return NextResponse.json({ error: 'No product IDs provided' }, { status: 400 })
    }

    const supabase = createSupabaseServiceClient()

    // Delete in correct order due to foreign key constraints

    // First get merchant_product IDs for these products
    const { data: merchantProducts } = await supabase
      .from('merchant_products')
      .select('id')
      .in('product_id', productIds)

    const merchantProductIds = merchantProducts?.map(mp => mp.id) || []

    // 1. Delete product variants
    if (merchantProductIds.length > 0) {
      await supabase
        .from('product_variants')
        .delete()
        .in('merchant_product_id', merchantProductIds)
    }

    // 2. Delete customization options
    const { error: customizationError } = await supabase
      .from('customization_options')
      .delete()
      .in('product_id', productIds)

    if (customizationError) {
      console.error('Error deleting customization options:', customizationError)
    }

    // 3. Delete product images
    const { error: imagesError } = await supabase
      .from('product_images')
      .delete()
      .in('product_id', productIds)

    if (imagesError) {
      console.error('Error deleting product images:', imagesError)
    }

    // 4. Delete merchant products
    const { error: merchantProductsError } = await supabase
      .from('merchant_products')
      .delete()
      .in('product_id', productIds)

    if (merchantProductsError) {
      console.error('Error deleting merchant products:', merchantProductsError)
    }

    // 5. Finally delete products
    const { error: productsError } = await supabase
      .from('products')
      .delete()
      .in('id', productIds)

    if (productsError) {
      console.error('Error deleting products:', productsError)
      return NextResponse.json({ error: 'Failed to delete products' }, { status: 500 })
    }

    return NextResponse.json({
      success: true,
      message: `Successfully deleted ${productIds.length} product(s)`
    })

  } catch (error) {
    console.error('Error in products DELETE:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
