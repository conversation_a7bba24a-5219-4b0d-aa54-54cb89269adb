import { NextRequest, NextResponse } from 'next/server'
import { createSupabaseServiceClient } from '@/lib/supabase-server'
import { ShopifyClient } from '@/lib/shopify'

export async function POST(request: NextRequest) {
  try {
    const supabase = createSupabaseServiceClient()

    // Get all merchants with user profiles that have NULL first_name or last_name
    const { data: merchantsWithIncompleteProfiles, error: fetchError } = await supabase
      .from('merchants')
      .select(`
        id,
        user_id,
        shop_domain,
        shop_name,
        access_token
      `)

    if (fetchError) {
      console.error('Error fetching merchants:', fetchError)
      return NextResponse.json({ error: 'Failed to fetch merchants' }, { status: 500 })
    }

    console.log(`Found ${merchantsWithIncompleteProfiles?.length || 0} merchants with incomplete profiles`)

    let updatedCount = 0
    let errorCount = 0

    for (const merchant of merchantsWithIncompleteProfiles || []) {
      try {
        // Get current user profile
        const { data: currentProfile } = await supabase
          .from('user_profiles')
          .select('*')
          .eq('user_id', merchant.user_id)
          .single()

        // Skip if profile doesn't exist or already has complete data
        if (!currentProfile || (currentProfile.first_name && currentProfile.last_name)) {
          continue
        }

        // Get fresh shop info from Shopify
        const shopifyClient = new ShopifyClient(merchant.shop_domain, merchant.access_token)
        const shopInfo = await shopifyClient.getShopInfo()

        // Parse owner name
        const ownerNameParts = (shopInfo.shop_owner || '').split(' ')
        const firstName = ownerNameParts[0] || 'Merchant'
        const lastName = ownerNameParts.slice(1).join(' ') || 'User'

        // Update user profile with Shopify data
        const { error: updateError } = await supabase
          .from('user_profiles')
          .update({
            first_name: currentProfile.first_name || firstName,
            last_name: currentProfile.last_name || lastName,
            company: currentProfile.company || shopInfo.name || merchant.shop_name,
            phone: currentProfile.phone || shopInfo.phone,
            updated_at: new Date().toISOString(),
          })
          .eq('user_id', merchant.user_id)

        if (updateError) {
          console.error(`Failed to update profile for merchant ${merchant.id}:`, updateError)
          errorCount++
        } else {
          console.log(`Updated profile for merchant ${merchant.id}: ${firstName} ${lastName}`)
          updatedCount++
        }

        // Add small delay to avoid rate limiting
        await new Promise(resolve => setTimeout(resolve, 100))

      } catch (error) {
        console.error(`Error processing merchant ${merchant.id}:`, error)
        errorCount++
      }
    }

    return NextResponse.json({
      success: true,
      message: `Backfill completed. Updated ${updatedCount} profiles, ${errorCount} errors.`,
      stats: {
        total: merchantsWithIncompleteProfiles?.length || 0,
        updated: updatedCount,
        errors: errorCount
      }
    })

  } catch (error) {
    console.error('Backfill profiles error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
