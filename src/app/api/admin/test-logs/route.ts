import { NextRequest, NextResponse } from 'next/server'
import { createSupabaseServiceClient } from '@/lib/supabase-server'

export async function GET(request: NextRequest) {
  try {
    const supabase = createSupabaseServiceClient()

    // Test 1: Check if we can connect to Supabase
    console.log('Testing Supabase connection...')

    // Test 2: Try to fetch activity logs
    const { data: logs, error } = await supabase
      .from('activity_logs')
      .select('*')
      .order('created_at', { ascending: false })
      .limit(10)

    console.log('Activity logs query result:', { logs, error })

    if (error) {
      return NextResponse.json({
        success: false,
        error: error.message,
        details: error
      }, { status: 500 })
    }

    // Test 3: Try to fetch users and merchants
    const { data: users, error: usersError } = await supabase
      .from('users')
      .select('id, email, role')
      .limit(5)

    const { data: merchants, error: merchantsError } = await supabase
      .from('merchants')
      .select('id, shop_name, shop_domain')
      .limit(5)

    console.log('Users query result:', { users, usersError })
    console.log('Merchants query result:', { merchants, merchantsError })

    return NextResponse.json({
      success: true,
      data: {
        activity_logs: logs || [],
        activity_logs_count: logs?.length || 0,
        users: users || [],
        users_count: users?.length || 0,
        merchants: merchants || [],
        merchants_count: merchants?.length || 0,
        errors: {
          logs_error: error ? JSON.stringify(error) : null,
          users_error: usersError ? JSON.stringify(usersError) : null,
          merchants_error: merchantsError ? JSON.stringify(merchantsError) : null
        }
      }
    })

  } catch (error) {
    console.error('Test logs API error:', error)
    return NextResponse.json({
      success: false,
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}
