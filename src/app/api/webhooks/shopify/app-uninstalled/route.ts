import { NextRequest, NextResponse } from 'next/server'
import { createSupabaseServiceClient } from '@/lib/supabase-server'
import { ShopifyOAuth } from '@/lib/shopify'
import { logActivity } from '@/lib/auth'

export async function POST(request: NextRequest) {
  try {
    const body = await request.text()
    const signature = request.headers.get('x-shopify-hmac-sha256')

    if (!signature) {
      return NextResponse.json(
        { error: 'Missing signature' },
        { status: 401 }
      )
    }

    // Verify webhook signature
    if (!ShopifyOAuth.verifyWebhook(body, signature)) {
      return NextResponse.json(
        { error: 'Invalid signature' },
        { status: 401 }
      )
    }

    const data = JSON.parse(body)
    const shopDomain = data.domain

    if (!shopDomain) {
      return NextResponse.json(
        { error: 'Missing shop domain' },
        { status: 400 }
      )
    }

    // Update merchant account to mark as uninstalled
    const supabase = createSupabaseServiceClient()
    
    const { data: merchant, error: fetchError } = await supabase
      .from('merchants')
      .select('*')
      .eq('shop_domain', shopDomain)
      .single()

    if (fetchError) {
      console.error('Failed to find merchant:', fetchError)
      return NextResponse.json(
        { error: 'Merchant not found' },
        { status: 404 }
      )
    }

    // Mark merchant as uninstalled
    const { error: updateError } = await supabase
      .from('merchants')
      .update({
        is_active: false,
        access_token: '', // Clear access token for security
      })
      .eq('id', merchant.id)

    if (updateError) {
      console.error('Failed to update merchant:', updateError)
      return NextResponse.json(
        { error: 'Failed to process uninstall' },
        { status: 500 }
      )
    }

    // Log the uninstallation with explicit user and merchant context
    await logActivity(
      'shopify_app_uninstalled',
      'merchant',
      merchant.id,
      {
        shop_domain: shopDomain,
        shop_name: merchant.shop_name,
        uninstalled_at: new Date().toISOString(),
      },
      merchant.user_id, // explicit user ID
      merchant.id       // explicit merchant ID
    )

    console.log(`App uninstalled for shop: ${shopDomain}`)

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('App uninstall webhook error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
