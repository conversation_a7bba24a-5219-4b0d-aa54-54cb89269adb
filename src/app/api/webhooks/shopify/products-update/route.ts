import { NextRequest, NextResponse } from 'next/server'
import { createSupabaseServerClient } from '@/lib/supabase-server'
import { ShopifyOAuth } from '@/lib/shopify'
import { logActivity } from '@/lib/auth'

export async function POST(request: NextRequest) {
  try {
    const body = await request.text()
    const signature = request.headers.get('x-shopify-hmac-sha256')
    const shopDomain = request.headers.get('x-shopify-shop-domain')

    if (!signature) {
      return NextResponse.json(
        { error: 'Missing signature' },
        { status: 401 }
      )
    }

    // Verify webhook signature
    if (!ShopifyOAuth.verifyWebhook(body, signature)) {
      return NextResponse.json(
        { error: 'Invalid signature' },
        { status: 401 }
      )
    }

    const productData = JSON.parse(body)

    if (!shopDomain) {
      return NextResponse.json(
        { error: 'Missing shop domain' },
        { status: 400 }
      )
    }

    // Get merchant account
    const supabase = await createSupabaseServerClient()
    
    const { data: merchant, error: fetchError } = await supabase
      .from('merchants')
      .select('*')
      .eq('shop_domain', shopDomain)
      .single()

    if (fetchError) {
      console.error('Failed to find merchant:', fetchError)
      return NextResponse.json(
        { error: 'Merchant not found' },
        { status: 404 }
      )
    }

    // Check if we have this product in our database
    const { data: existingMerchantProduct, error: productFetchError } = await supabase
      .from('merchant_products')
      .select('*, products(*)')
      .eq('shopify_product_id', productData.id.toString())
      .eq('merchant_id', merchant.id)
      .single()

    if (productFetchError && productFetchError.code !== 'PGRST116') {
      console.error('Failed to fetch product:', productFetchError)
      return NextResponse.json(
        { error: 'Failed to fetch product' },
        { status: 500 }
      )
    }

    // Check if product is customizable
    const isCustomizable = productData.tags?.includes('customizable') ||
                          productData.tags?.includes('biypod') ||
                          productData.product_type?.toLowerCase().includes('custom')

    if (existingMerchantProduct) {
      // Update existing product
      const { error: updateError } = await supabase
        .from('products')
        .update({
          name: productData.title,
          description: productData.body_html,
          base_price: parseFloat(productData.variants?.[0]?.price || '0'),
          tags: productData.tags?.split(',').map((tag: string) => tag.trim()) || [],
          is_active: productData.status === 'active',
          updated_at: new Date().toISOString(),
        })
        .eq('id', existingMerchantProduct.product_id)

      if (updateError) {
        console.error('Failed to update product:', updateError)
        return NextResponse.json(
          { error: 'Failed to update product' },
          { status: 500 }
        )
      }

      // Update merchant product relationship
      await supabase
        .from('merchant_products')
        .update({
          is_published: productData.status === 'active',
          published_at: productData.status === 'active' ? new Date().toISOString() : null,
          updated_at: new Date().toISOString(),
        })
        .eq('id', existingMerchantProduct.id)

      // Update product variants
      if (productData.variants && productData.variants.length > 0) {
        // First, mark all existing variants as inactive
        await supabase
          .from('product_variants')
          .update({ is_active: false })
          .eq('merchant_product_id', existingMerchantProduct.id)

        // Then upsert the current variants
        for (const variant of productData.variants) {
          const variantData = {
            merchant_product_id: existingMerchantProduct.id,
            shopify_variant_id: variant.id?.toString(),
            title: variant.title || 'Default Title',
            price: parseFloat(variant.price || '0'),
            compare_at_price: variant.compare_at_price ? parseFloat(variant.compare_at_price) : null,
            sku: variant.sku || null,
            barcode: variant.barcode || null,
            inventory_quantity: variant.inventory_quantity || 0,
            inventory_policy: variant.inventory_policy || 'deny',
            fulfillment_service: variant.fulfillment_service || 'manual',
            inventory_management: variant.inventory_management || null,
            option1: variant.option1 || null,
            option2: variant.option2 || null,
            option3: variant.option3 || null,
            weight: variant.weight ? parseFloat(variant.weight) : null,
            weight_unit: variant.weight_unit || 'kg',
            requires_shipping: variant.requires_shipping !== false,
            taxable: variant.taxable !== false,
            position: variant.position || 1,
            image_url: variant.image_id ? `https://cdn.shopify.com/s/files/1/variant_${variant.image_id}.jpg` : null,
            is_active: true,
            updated_at: new Date().toISOString(),
          }

          // Try to update existing variant, or insert new one
          const { data: existingVariant } = await supabase
            .from('product_variants')
            .select('id')
            .eq('shopify_variant_id', variant.id?.toString())
            .eq('merchant_product_id', existingMerchantProduct.id)
            .single()

          if (existingVariant) {
            await supabase
              .from('product_variants')
              .update(variantData)
              .eq('id', existingVariant.id)
          } else {
            await supabase
              .from('product_variants')
              .insert(variantData)
          }
        }
      }

      // Log the product update
      await logActivity(
        'product_updated',
        'product',
        existingMerchantProduct.product_id,
        {
          shop_domain: shopDomain,
          shopify_product_id: productData.id,
          product_title: productData.title,
          is_customizable: isCustomizable,
        }
      )

      console.log(`Product updated for shop: ${shopDomain}, product: ${productData.title}`)
    } else if (isCustomizable) {
      // Create new customizable product if it doesn't exist
      const { data: product, error: productError } = await supabase
        .from('products')
        .insert({
          name: productData.title,
          description: productData.body_html,
          base_price: parseFloat(productData.variants?.[0]?.price || '0'),
          sku: productData.variants?.[0]?.sku || null,
          slug: productData.handle || productData.title.toLowerCase().replace(/\s+/g, '-'),
          tags: productData.tags?.split(',').map((tag: string) => tag.trim()) || [],
          is_active: productData.status === 'active',
        })
        .select()
        .single()

      if (!productError) {
        // Create merchant-product relationship
        await supabase
          .from('merchant_products')
          .insert({
            merchant_id: merchant.id,
            product_id: product.id,
            shopify_product_id: productData.id.toString(),
            is_published: productData.status === 'active',
            published_at: productData.status === 'active' ? new Date().toISOString() : null,
          })

        console.log(`New customizable product created for shop: ${shopDomain}, product: ${productData.title}`)
      }
    }

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Product update webhook error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
