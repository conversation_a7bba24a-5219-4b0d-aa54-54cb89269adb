import { NextRequest, NextResponse } from 'next/server'
import { createSupabaseServerClient } from '@/lib/supabase-server'
import { ShopifyOAuth } from '@/lib/shopify'
import { logActivity } from '@/lib/auth'

export async function POST(request: NextRequest) {
  try {
    const body = await request.text()
    const signature = request.headers.get('x-shopify-hmac-sha256')
    const shopDomain = request.headers.get('x-shopify-shop-domain')

    if (!signature) {
      return NextResponse.json(
        { error: 'Missing signature' },
        { status: 401 }
      )
    }

    // Verify webhook signature
    if (!ShopifyOAuth.verifyWebhook(body, signature)) {
      return NextResponse.json(
        { error: 'Invalid signature' },
        { status: 401 }
      )
    }

    const orderData = JSON.parse(body)

    if (!shopDomain) {
      return NextResponse.json(
        { error: 'Missing shop domain' },
        { status: 400 }
      )
    }

    // Get merchant account
    const supabase = await createSupabaseServerClient()
    
    const { data: merchant, error: fetchError } = await supabase
      .from('merchants')
      .select('*')
      .eq('shop_domain', shopDomain)
      .single()

    if (fetchError) {
      console.error('Failed to find merchant:', fetchError)
      return NextResponse.json(
        { error: 'Merchant not found' },
        { status: 404 }
      )
    }

    // Check if order contains customizable products
    const hasCustomizableProducts = orderData.line_items?.some((item: any) => 
      item.properties?.some((prop: any) => 
        prop.name?.toLowerCase().includes('custom') || 
        prop.name?.toLowerCase().includes('design')
      )
    )

    if (hasCustomizableProducts) {
      // Create order record in our database
      const { data: order, error: orderError } = await supabase
        .from('orders')
        .insert({
          merchant_id: merchant.id,
          shopify_order_id: orderData.id.toString(),
          order_number: orderData.order_number || orderData.name,
          customer_email: orderData.email,
          customer_name: `${orderData.billing_address?.first_name || ''} ${orderData.billing_address?.last_name || ''}`.trim(),
          total_amount: parseFloat(orderData.total_price || '0'),
          subtotal: parseFloat(orderData.subtotal_price || '0'),
          tax_amount: parseFloat(orderData.total_tax || '0'),
          status: 'pending',
          billing_address: orderData.billing_address,
          shipping_address: orderData.shipping_address,
        })
        .select()
        .single()

      if (orderError) {
        console.error('Failed to create order:', orderError)
        return NextResponse.json(
          { error: 'Failed to process order' },
          { status: 500 }
        )
      }

      // Create order items for each line item
      if (orderData.line_items && orderData.line_items.length > 0) {
        const orderItems = []

        for (const lineItem of orderData.line_items) {
          // Try to find the corresponding merchant product
          const { data: merchantProduct } = await supabase
            .from('merchant_products')
            .select('id')
            .eq('merchant_id', merchant.id)
            .eq('shopify_product_id', lineItem.product_id?.toString())
            .single()

          orderItems.push({
            order_id: order.id,
            merchant_product_id: merchantProduct?.id || null,
            shopify_line_item_id: lineItem.id?.toString(),
            product_name: lineItem.name || 'Unknown Product',
            variant_title: lineItem.variant_title || null,
            quantity: lineItem.quantity || 1,
            price: parseFloat(lineItem.price || '0'),
            total_price: parseFloat(lineItem.price || '0') * (lineItem.quantity || 1),
            sku: lineItem.sku || null,
            customization_data: lineItem.properties ? JSON.stringify(lineItem.properties) : null,
            // Required legacy fields for the updated schema
            legacy_product_id: lineItem.product_id?.toString() || null,
            legacy_unit_price: parseFloat(lineItem.price || '0'),
          })
        }

        if (orderItems.length > 0) {
          const { error: itemsError } = await supabase
            .from('order_items')
            .insert(orderItems)

          if (itemsError) {
            console.error('Failed to create order items:', itemsError)
            // Don't fail the entire webhook for order items
          }
        }
      }

      // Log the order creation
      await logActivity(
        'order_created',
        'order',
        order.id,
        {
          shop_domain: shopDomain,
          shopify_order_id: orderData.id,
          order_number: orderData.order_number || orderData.name,
          has_customizations: true,
        }
      )

      console.log(`Customizable order created for shop: ${shopDomain}, order: ${orderData.order_number}`)
    }

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Order create webhook error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
