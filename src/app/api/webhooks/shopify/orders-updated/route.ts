import { NextRequest, NextResponse } from 'next/server'
import { createSupabaseServerClient } from '@/lib/supabase-server'
import { ShopifyOAuth } from '@/lib/shopify'
import { logActivity } from '@/lib/auth'

export async function POST(request: NextRequest) {
  try {
    const body = await request.text()
    const signature = request.headers.get('x-shopify-hmac-sha256')
    const shopDomain = request.headers.get('x-shopify-shop-domain')

    if (!signature) {
      return NextResponse.json(
        { error: 'Missing signature' },
        { status: 401 }
      )
    }

    // Verify webhook signature
    if (!ShopifyOAuth.verifyWebhook(body, signature)) {
      return NextResponse.json(
        { error: 'Invalid signature' },
        { status: 401 }
      )
    }

    const orderData = JSON.parse(body)

    if (!shopDomain) {
      return NextResponse.json(
        { error: 'Missing shop domain' },
        { status: 400 }
      )
    }

    // Get merchant account
    const supabase = await createSupabaseServerClient()
    
    const { data: merchant, error: fetchError } = await supabase
      .from('merchants')
      .select('*')
      .eq('shop_domain', shopDomain)
      .single()

    if (fetchError) {
      console.error('Failed to find merchant:', fetchError)
      return NextResponse.json(
        { error: 'Merchant not found' },
        { status: 404 }
      )
    }

    // Check if we have this order in our database
    const { data: existingOrder, error: orderFetchError } = await supabase
      .from('orders')
      .select('*')
      .eq('shopify_order_id', orderData.id.toString())
      .eq('merchant_id', merchant.id)
      .single()

    if (orderFetchError && orderFetchError.code !== 'PGRST116') {
      console.error('Failed to fetch order:', orderFetchError)
      return NextResponse.json(
        { error: 'Failed to fetch order' },
        { status: 500 }
      )
    }

    if (existingOrder) {
      // Update existing order
      const { error: updateError } = await supabase
        .from('orders')
        .update({
          customer_email: orderData.email,
          customer_name: `${orderData.billing_address?.first_name || ''} ${orderData.billing_address?.last_name || ''}`.trim(),
          total_amount: parseFloat(orderData.total_price || '0'),
          subtotal: parseFloat(orderData.subtotal_price || '0'),
          tax_amount: parseFloat(orderData.total_tax || '0'),
          billing_address: orderData.billing_address,
          shipping_address: orderData.shipping_address,
          updated_at: new Date().toISOString(),
        })
        .eq('id', existingOrder.id)

      if (updateError) {
        console.error('Failed to update order:', updateError)
        return NextResponse.json(
          { error: 'Failed to update order' },
          { status: 500 }
        )
      }

      // Log the order update
      await logActivity(
        'order_updated',
        'order',
        existingOrder.id,
        {
          shop_domain: shopDomain,
          shopify_order_id: orderData.id,
          order_number: orderData.order_number || orderData.name,
        }
      )

      console.log(`Order updated for shop: ${shopDomain}, order: ${orderData.order_number}`)
    }

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Order update webhook error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
