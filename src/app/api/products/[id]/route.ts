import { NextRequest, NextResponse } from 'next/server'
import { createSupabaseServerClient } from '@/lib/supabase-server'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    
    if (!id) {
      return NextResponse.json({ error: 'Product ID is required' }, { status: 400 })
    }

    const supabase = await createSupabaseServerClient()

    // Fetch product with all related files
    const { data: product, error } = await supabase
      .from('products')
      .select(`
        *,
        product_images(*)
      `)
      .eq('id', id)
      .single()

    if (error) {
      console.error('Error fetching product:', error)
      return NextResponse.json({ error: 'Product not found' }, { status: 404 })
    }

    if (!product) {
      return NextResponse.json({ error: 'Product not found' }, { status: 404 })
    }

    // Parse JSON fields if they're strings
    const processedProduct = {
      ...product,
      print_files: typeof product.print_files === 'string'
        ? JSON.parse(product.print_files)
        : product.print_files || [],
      mockup_files: typeof product.mockup_files === 'string'
        ? JSON.parse(product.mockup_files)
        : product.mockup_files || [],
      glb_files: typeof product.glb_files === 'string'
        ? JSON.parse(product.glb_files)
        : product.glb_files || [],
      texture_files: typeof product.texture_files === 'string'
        ? JSON.parse(product.texture_files)
        : product.texture_files || [],
      preview_files: typeof product.preview_files === 'string'
        ? JSON.parse(product.preview_files)
        : product.preview_files || [],
    }

    // Use GLB files from database (already parsed above)
    const responseData = {
      ...processedProduct
    }

    // Debug logging
    console.log('🔍 API Response - GLB files count:', responseData.glb_files?.length || 0)
    console.log('🔍 API Response - GLB files:', responseData.glb_files?.map((f: any) => f.filename || f.name) || [])
    console.log('🔍 API Response - Print files count:', responseData.print_files?.length || 0)
    console.log('🔍 API Response - Texture files count:', responseData.texture_files?.length || 0)
    console.log('🔍 API Response - Preview files count:', responseData.preview_files?.length || 0)

    return NextResponse.json(responseData)

  } catch (error) {
    console.error('Error in products API:', error)
    return NextResponse.json(
      { error: 'Internal server error' }, 
      { status: 500 }
    )
  }
}
