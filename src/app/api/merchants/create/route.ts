import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

export async function POST(request: NextRequest) {
  try {
    const { shop_domain } = await request.json()

    if (!shop_domain) {
      return NextResponse.json(
        { error: 'shop_domain is required' },
        { status: 400 }
      )
    }

    // Use service role key to bypass RLS for merchant creation
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    // Check if merchant already exists
    const { data: existingMerchant } = await supabase
      .from('merchants')
      .select('*')
      .eq('shop_domain', shop_domain)
      .maybeSingle()

    if (existingMerchant) {
      return NextResponse.json({
        success: true,
        message: 'Merchant already exists',
        merchant: existingMerchant
      })
    }

    // Create a temporary auth user for the merchant
    const tempEmail = shop_domain.replace('.myshopify.com', '') + '@merchant.biypod.com'
    const tempPassword = 'temp-' + Math.random().toString(36).substring(2, 15)

    try {
      // Create auth user
      const { data: authData, error: authError } = await supabase.auth.admin.createUser({
        email: tempEmail,
        password: tempPassword,
        email_confirm: true,
        user_metadata: {
          role: 'merchant',
          shop_domain: shop_domain,
          created_via: 'embedded_app_access'
        }
      })

      if (authError || !authData.user) {
        console.error('Error creating auth user:', authError)
        return NextResponse.json(
          {
            success: false,
            error: 'Failed to create user account',
            details: authError?.message
          },
          { status: 500 }
        )
      }

      // Create user record
      const { error: userError } = await supabase
        .from('users')
        .insert({
          id: authData.user.id,
          email: tempEmail,
          role: 'merchant'
        })

      if (userError) {
        console.error('Error creating user record:', userError)
        // Clean up auth user if user record creation fails
        await supabase.auth.admin.deleteUser(authData.user.id)
        return NextResponse.json(
          {
            success: false,
            error: 'Failed to create user record',
            details: userError.message
          },
          { status: 500 }
        )
      }

      // Create user profile
      const { error: profileError } = await supabase
        .from('user_profiles')
        .insert({
          user_id: authData.user.id,
          first_name: 'Merchant',
          last_name: 'User',
          company: shop_domain.replace('.myshopify.com', '')
        })

      if (profileError) {
        console.error('Error creating user profile:', profileError)
        // Continue anyway, profile is not critical
      }

      // Create merchant record
      const { data: newMerchant, error: merchantError } = await supabase
        .from('merchants')
        .insert({
          user_id: authData.user.id,
          shop_domain,
          shop_name: shop_domain.replace('.myshopify.com', ''),
          access_token: 'pending-' + Date.now(),
          subscription_tier: 'free',
          subscription_status: 'active',
          settings: {
            customizer_enabled: true,
            auto_fulfill: false,
            theme_settings: {
              primary_color: '#3B82F6',
              secondary_color: '#10B981'
            }
          }
        })
        .select()
        .single()

      if (merchantError) {
        console.error('Error creating merchant:', merchantError)
        // Clean up auth user and user record
        await supabase.auth.admin.deleteUser(authData.user.id)
        return NextResponse.json(
          {
            success: false,
            error: 'Failed to create merchant',
            details: merchantError.message
          },
          { status: 500 }
        )
      }

      return NextResponse.json({
        success: true,
        message: 'Merchant created successfully',
        merchant: newMerchant
      })

    } catch (error) {
      console.error('Unexpected error creating merchant:', error)
      return NextResponse.json(
        {
          success: false,
          error: 'Unexpected error',
          details: error instanceof Error ? error.message : 'Unknown error'
        },
        { status: 500 }
      )
    }

  } catch (error) {
    console.error('Error in create merchant API:', error)
    return NextResponse.json(
      { 
        success: false,
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'Merchant creation API',
    usage: 'POST with { shop_domain: "store.myshopify.com" }',
    description: 'Creates a merchant record if it does not exist'
  })
}
