import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const shop_domain = searchParams.get('shop_domain')

    if (!shop_domain) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Shop domain is required' 
        },
        { status: 400 }
      )
    }

    // Use service role key to bypass RLS
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    // Get merchant data
    const { data: merchant, error } = await supabase
      .from('merchants')
      .select('*')
      .eq('shop_domain', shop_domain)
      .single()

    if (error) {
      console.error('Error fetching merchant:', error)
      return NextResponse.json(
        { 
          success: false,
          error: 'Merchant not found',
          details: error.message 
        },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      merchant
    })

  } catch (error) {
    console.error('Error in merchant get API:', error)
    return NextResponse.json(
      { 
        success: false,
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
