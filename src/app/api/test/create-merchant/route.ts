import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

export async function POST(request: NextRequest) {
  try {
    const { shop_domain } = await request.json()

    if (!shop_domain) {
      return NextResponse.json(
        { error: 'shop_domain is required' },
        { status: 400 }
      )
    }

    // Use service role key to bypass RLS
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    // Check if merchant already exists
    const { data: existingMerchant } = await supabase
      .from('merchants')
      .select('*')
      .eq('shop_domain', shop_domain)
      .single()

    if (existingMerchant) {
      return NextResponse.json({
        message: 'Merchant already exists',
        merchant: existingMerchant
      })
    }

    // Create new merchant
    const { data: newMerchant, error } = await supabase
      .from('merchants')
      .insert({
        shop_domain,
        shop_name: shop_domain.replace('.myshopify.com', ''),
        access_token: 'test-token-' + Date.now(),
        subscription_tier: 'free',
        subscription_status: 'active',
        settings: {
          customizer_enabled: true,
          auto_fulfill: false
        }
      })
      .select()
      .single()

    if (error) {
      console.error('Error creating merchant:', error)
      return NextResponse.json(
        { error: 'Failed to create merchant' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      message: 'Merchant created successfully',
      merchant: newMerchant
    })

  } catch (error) {
    console.error('Error in create-merchant API:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'Use POST method to create a merchant',
    example: {
      shop_domain: 'example-store.myshopify.com'
    }
  })
}
