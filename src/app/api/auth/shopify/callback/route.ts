import { NextRequest, NextResponse } from 'next/server'
import { createSupabaseServerClient, createSupabaseServiceClient } from '@/lib/supabase-server'
import { createMerchantAccount, logActivity } from '@/lib/auth'
import { ShopifyOAuth, ShopifyClient, validateShopDomain, WEBHOOK_TOPICS } from '@/lib/shopify'
import crypto from 'crypto'

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url)
  const code = searchParams.get('code')
  const shop = searchParams.get('shop')
  const state = searchParams.get('state')
  const hmac = searchParams.get('hmac')

  console.log('Shopify callback received:', { shop, code: !!code, state: !!state, hmac: !!hmac })

  // Verify state parameter (more lenient for development)
  const storedState = request.cookies.get('shopify_oauth_state')?.value
  console.log('State validation:', { provided: state, stored: storedState })

  // For now, let's be more lenient with state validation to debug the issue
  if (!state) {
    console.error('No state parameter provided')
    return NextResponse.json(
      { error: 'Missing state parameter' },
      { status: 400 }
    )
  }

  // TODO: Re-enable strict state validation after debugging
  // if (!storedState || state !== storedState) {
  //   console.error('State mismatch:', { provided: state, stored: storedState })
  //   return NextResponse.json(
  //     { error: 'Invalid state parameter' },
  //     { status: 400 }
  //   )
  // }

  if (!code || !shop) {
    return NextResponse.json(
      { error: 'Missing required parameters' },
      { status: 400 }
    )
  }

  // Validate shop domain
  if (!validateShopDomain(shop)) {
    return NextResponse.json(
      { error: 'Invalid shop domain' },
      { status: 400 }
    )
  }

  try {
    console.log('Attempting to exchange code for token:', { shop })
    // Exchange code for access token using Shopify service
    const accessToken = await ShopifyOAuth.exchangeCodeForToken(shop, code)
    console.log('Successfully obtained access token')

    // Create Shopify client and get shop information
    const shopifyClient = new ShopifyClient(shop, accessToken)
    const shopInfo = await shopifyClient.getShopInfo()

    // Check if merchant already exists for this specific shop domain
    const supabase = await createSupabaseServerClient()
    const supabaseService = createSupabaseServiceClient()
    const { data: existingMerchant, error: fetchError } = await supabaseService
      .from('merchants')
      .select('*')
      .eq('shop_domain', shop)
      .single()

    if (fetchError && fetchError.code !== 'PGRST116') {
      throw new Error('Database error: ' + fetchError.message)
    }

    let merchantData
    if (existingMerchant) {
      // Update existing merchant for this shop domain
      const { data, error } = await supabaseService
        .from('merchants')
        .update({
          access_token: accessToken,
          shop_name: shopInfo.name,
          updated_at: new Date().toISOString(),
        })
        .eq('shop_domain', shop)
        .select()
        .single()

      if (error) throw new Error('Failed to update merchant: ' + error.message)
      merchantData = data

      console.log('Updated existing merchant for shop:', shop, merchantData.id)
    } else {

      // Create a user account for the merchant using Supabase Auth
      const merchantEmail = shopInfo.email || `${shop.replace('.myshopify.com', '')}@shopify.merchant`

      console.log('Creating new merchant account for:', merchantEmail)

      // Check if user already exists in auth
      const { data: existingAuthUser } = await supabaseService.auth.admin.listUsers()
      const authUser = existingAuthUser.users.find(u => u.email === merchantEmail)

      let userId: string

      if (authUser) {
        console.log('Auth user already exists, using existing:', authUser.id)
        userId = authUser.id
      } else {
        // Create user in Supabase Auth first
        const { data: authData, error: authError } = await supabaseService.auth.admin.createUser({
          email: merchantEmail,
          password: crypto.randomBytes(32).toString('hex'), // Random secure password
          email_confirm: true, // Skip email confirmation for Shopify merchants
          user_metadata: {
            shop_domain: shop,
            shop_name: shopInfo.name,
            role: 'merchant',
            created_via: 'shopify_install'
          }
        })

        if (authError) throw new Error('Failed to create auth user: ' + authError.message)
        if (!authData.user) throw new Error('No user data returned from auth creation')

        userId = authData.user.id
        console.log('Created new auth user:', userId)
      }

      // Check if user record already exists in our users table
      const { data: existingUser } = await supabaseService
        .from('users')
        .select('id')
        .eq('id', userId)
        .single()

      if (!existingUser) {
        // Create the user record in our users table
        const { data: userData, error: userError } = await supabaseService
          .from('users')
          .insert({
            id: userId,
            email: merchantEmail,
            role: 'merchant',
          })
          .select()
          .single()

        if (userError) throw new Error('Failed to create user record: ' + userError.message)
        console.log('Created user record:', userData.id)
      } else {
        console.log('User record already exists:', existingUser.id)
      }

      // Check if user profile already exists
      const { data: existingProfile } = await supabaseService
        .from('user_profiles')
        .select('*')
        .eq('user_id', userId)
        .single()

      if (!existingProfile) {
        // Create user profile with complete Shopify data
        const ownerNameParts = (shopInfo.shop_owner || '').split(' ')
        const firstName = ownerNameParts[0] || 'Merchant'
        const lastName = ownerNameParts.slice(1).join(' ') || 'User'

        const { error: profileError } = await supabaseService
          .from('user_profiles')
          .insert({
            user_id: userId,
            first_name: firstName,
            last_name: lastName,
            company: shopInfo.name || shop.replace('.myshopify.com', ''),
            phone: shopInfo.phone || null,
            avatar_url: null,
          })

        if (profileError) throw new Error('Failed to create user profile: ' + profileError.message)
        console.log('Created user profile for:', userId, { firstName, lastName, company: shopInfo.name })
      } else {
        // Update existing profile with latest shop info, but preserve existing personal data
        const ownerNameParts = (shopInfo.shop_owner || '').split(' ')
        const firstName = ownerNameParts[0] || existingProfile.first_name || 'Merchant'
        const lastName = ownerNameParts.slice(1).join(' ') || existingProfile.last_name || 'User'

        const { error: updateProfileError } = await supabaseService
          .from('user_profiles')
          .update({
            // Only update if current values are null/empty or if we have better data from Shopify
            first_name: existingProfile.first_name || firstName,
            last_name: existingProfile.last_name || lastName,
            company: shopInfo.name || existingProfile.company,
            phone: shopInfo.phone || existingProfile.phone,
            updated_at: new Date().toISOString(),
          })
          .eq('user_id', userId)

        if (updateProfileError) console.warn('Failed to update user profile:', updateProfileError)
        console.log('Updated user profile for:', userId, { firstName, lastName, company: shopInfo.name })
      }

      // Always create a new merchant record for each shop domain
      // After removing the unique constraint, each shop gets its own merchant record
      const { data, error } = await supabaseService
        .from('merchants')
        .insert({
          shop_domain: shop,
          access_token: accessToken,
          shop_name: shopInfo.name,
          user_id: userId,
          subscription_tier: 'free',
          subscription_status: 'trialing',
          settings: {
            customizer_enabled: true,
            auto_fulfill: false,
            theme_settings: {
              primary_color: '#3B82F6',
              secondary_color: '#10B981'
            }
          }
        })
        .select()
        .single()

      if (error) throw new Error('Failed to create merchant: ' + error.message)
      merchantData = data
      console.log('Created new merchant for shop:', shop, merchantData.id)
    }

    // Set up webhooks for the merchant
    try {
      const webhookTopics = [
        WEBHOOK_TOPICS.APP_UNINSTALLED,
        WEBHOOK_TOPICS.ORDERS_CREATE,
        WEBHOOK_TOPICS.ORDERS_UPDATED,
        WEBHOOK_TOPICS.PRODUCTS_CREATE,
        WEBHOOK_TOPICS.PRODUCTS_UPDATE,
      ]

      for (const topic of webhookTopics) {
        const webhookUrl = `${process.env.SHOPIFY_APP_URL}/api/webhooks/shopify/${topic.replace('/', '-')}`
        await shopifyClient.createWebhook(topic, webhookUrl)
      }
    } catch (webhookError) {
      console.warn('Failed to create some webhooks:', webhookError)
      // Don't fail the installation if webhooks fail
    }

    // Log the installation/update with explicit context
    await logActivity(
      existingMerchant ? 'shopify_app_updated' : 'shopify_app_installed',
      'merchant',
      merchantData.id,
      {
        shop_domain: shop,
        shop_name: shopInfo.name,
        installed_at: new Date().toISOString(),
        shopify_plan: shopInfo.plan_name || 'unknown',
      },
      merchantData.user_id, // explicit user ID
      merchantData.id       // explicit merchant ID
    )

    // Redirect to Shopify Admin where the app will be embedded
    const host = searchParams.get('host')
    if (host) {
      // Extract shop name from shop domain for the admin URL
      const shopName = shop.replace('.myshopify.com', '')
      // Redirect to Shopify Admin app URL - this will embed our app
      const shopifyAdminUrl = `https://admin.shopify.com/store/${shopName}/apps/biypod-customizer`
      return NextResponse.redirect(shopifyAdminUrl)
    } else {
      // Fallback to success page
      return NextResponse.redirect(
        new URL(`/auth/shopify/success?shop=${shop}`, request.url)
      )
    }

  } catch (error) {
    console.error('Shopify OAuth callback error:', error)

    // Provide more specific error information
    let errorMessage = 'Authentication failed'
    if (error instanceof Error) {
      errorMessage = error.message
    }

    return NextResponse.json(
      {
        error: 'Authentication failed',
        details: errorMessage,
        shop,
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    )
  }
}
