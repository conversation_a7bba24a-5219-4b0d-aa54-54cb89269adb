import { NextRequest, NextResponse } from 'next/server'
import { ShopifyOAuth, validateShopDomain, sanitizeShopDomain } from '@/lib/shopify'
import { createClient } from '@supabase/supabase-js'
import crypto from 'crypto'

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url)
  const shop = searchParams.get('shop')
  const hmac = searchParams.get('hmac')
  const host = searchParams.get('host')

  // Check if this shop already has the app installed with a valid access token
  console.log('Checking if app is already installed for shop:', shop)

  const supabase = createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!
  )

  const { data: existingMerchant } = await supabase
    .from('merchants')
    .select('*')
    .eq('shop_domain', shop)
    .single()

  // If merchant exists and has an access token, verify it's still valid
  if (existingMerchant && existingMerchant.access_token && shop) {
    console.log('Found existing merchant, verifying access token...')

    try {
      // Test the access token by making a simple API call to Shopify
      const shopifyResponse = await fetch(`https://${shop}/admin/api/2024-01/shop.json`, {
        headers: {
          'X-Shopify-Access-Token': existingMerchant.access_token,
          'Content-Type': 'application/json',
        },
      })

      if (shopifyResponse.ok) {
        console.log('Access token is valid, serving embedded app page')
        // Redirect to our embedded app page with proper parameters
        const host = searchParams.get('host') || ''
        return NextResponse.redirect(
          new URL(`/shopify?shop=${shop}&host=${encodeURIComponent(host)}`, request.url)
        )
      } else {
        console.log('Access token is invalid, removing merchant record and starting OAuth')
        // Remove the invalid merchant record
        await supabase
          .from('merchants')
          .delete()
          .eq('shop_domain', shop)

        // Continue to OAuth flow below
      }
    } catch (error) {
      console.log('Error verifying access token, starting OAuth flow:', error)
      // Continue to OAuth flow below
    }
  }

  console.log('App not installed or no valid token, starting OAuth flow')

  // If no shop parameter, show shop input form
  if (!shop) {
    return new NextResponse(`
      <!DOCTYPE html>
      <html>
        <head>
          <title>Install Biypod Customizer</title>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1">
          <style>
            body {
              font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
              background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
              min-height: 100vh;
              display: flex;
              align-items: center;
              justify-content: center;
              margin: 0;
              padding: 20px;
            }
            .container {
              background: white;
              padding: 40px;
              border-radius: 12px;
              box-shadow: 0 20px 40px rgba(0,0,0,0.1);
              max-width: 400px;
              width: 100%;
              text-align: center;
            }
            .logo {
              width: 60px;
              height: 60px;
              background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
              border-radius: 12px;
              margin: 0 auto 20px;
            }
            h1 {
              color: #333;
              margin-bottom: 10px;
              font-size: 24px;
            }
            p {
              color: #666;
              margin-bottom: 30px;
              line-height: 1.5;
            }
            .form-group {
              margin-bottom: 20px;
              text-align: left;
            }
            label {
              display: block;
              margin-bottom: 5px;
              color: #333;
              font-weight: 500;
            }
            input {
              width: 100%;
              padding: 12px;
              border: 2px solid #e1e5e9;
              border-radius: 8px;
              font-size: 16px;
              transition: border-color 0.2s;
              box-sizing: border-box;
            }
            input:focus {
              outline: none;
              border-color: #667eea;
            }
            button {
              width: 100%;
              background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
              color: white;
              border: none;
              padding: 12px 24px;
              border-radius: 8px;
              font-size: 16px;
              font-weight: 600;
              cursor: pointer;
              transition: transform 0.2s;
            }
            button:hover {
              transform: translateY(-1px);
            }
            .help-text {
              font-size: 14px;
              color: #888;
              margin-top: 15px;
            }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="logo"></div>
            <h1>Install Biypod Customizer</h1>
            <p>Enter your Shopify store domain to install the Biypod Customizer app</p>
            
            <form onsubmit="handleSubmit(event)">
              <div class="form-group">
                <label for="shop">Shopify Store Domain</label>
                <input 
                  type="text" 
                  id="shop" 
                  name="shop" 
                  placeholder="your-store.myshopify.com"
                  required
                  pattern="[a-zA-Z0-9-]+\\.myshopify\\.com"
                  title="Please enter a valid Shopify domain (e.g., your-store.myshopify.com)"
                />
              </div>
              <button type="submit">Install App</button>
            </form>
            
            <p class="help-text">
              Don't have a Shopify store? <a href="https://www.shopify.com" target="_blank" style="color: #667eea;">Create one here</a>
            </p>
          </div>
          
          <script>
            function handleSubmit(event) {
              event.preventDefault();
              const shop = document.getElementById('shop').value.trim();
              
              // Ensure the domain ends with .myshopify.com
              let shopDomain = shop;
              if (!shopDomain.includes('.')) {
                shopDomain = shopDomain + '.myshopify.com';
              } else if (!shopDomain.endsWith('.myshopify.com')) {
                alert('Please enter a valid Shopify domain ending with .myshopify.com');
                return;
              }
              
              // Redirect to OAuth flow
              window.location.href = '/api/auth/shopify/install?shop=' + encodeURIComponent(shopDomain);
            }
          </script>
        </body>
      </html>
    `, {
      headers: {
        'Content-Type': 'text/html',
      },
    })
  }

  // Sanitize and validate shop domain
  const sanitizedShop = sanitizeShopDomain(shop)

  if (!validateShopDomain(sanitizedShop)) {
    return NextResponse.json(
      { error: 'Invalid shop domain' },
      { status: 400 }
    )
  }



  // Generate state parameter for security
  const state = crypto.randomBytes(16).toString('hex')

  // Generate OAuth URL using Shopify service
  const installUrl = ShopifyOAuth.generateInstallUrl(sanitizedShop, state)

  // Store state in session/cookie for verification
  const response = NextResponse.redirect(installUrl)
  response.cookies.set('shopify_oauth_state', state, {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'lax',
    maxAge: 600, // 10 minutes
  })

  return response
}
