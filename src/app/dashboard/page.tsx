import { Suspense } from 'react'
import { requireAuth } from '@/lib/auth'
import { createSupabaseServerClient } from '@/lib/supabase-server'
import { ShopifyClient } from '@/lib/shopify'
import DashboardContent from '@/components/dashboard/dashboard-content'
import DashboardSkeleton from '@/components/dashboard/dashboard-skeleton'
import { redirect } from 'next/navigation'

interface DashboardPageProps {
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>
}

export default async function DashboardPage({ searchParams }: DashboardPageProps) {
  const params = await searchParams
  const shop = params.shop as string
  const host = params.host as string

  // If shop parameter is present, this is an embedded Shopify app access
  if (shop) {
    // Redirect to the embedded app handler
    const redirectUrl = `/shopify/dashboard?shop=${encodeURIComponent(shop)}`
    if (host) {
      redirect(`${redirectUrl}&host=${encodeURIComponent(host)}`)
    } else {
      redirect(redirectUrl)
    }
  }

  const user = await requireAuth()
  const supabase = await createSupabaseServerClient()

  // Get user profile and role
  const { data: profile } = await supabase
    .from('user_profiles')
    .select('*')
    .eq('user_id', user.id)
    .single()

  const { data: userRole } = await supabase
    .from('users')
    .select('role')
    .eq('id', user.id)
    .single()

  if (!profile || !userRole) {
    throw new Error('User profile or role not found')
  }

  // Get dashboard data based on user role
  let dashboardData = null

  if (userRole.role === 'super_admin') {
    // Super admin dashboard data
    const { data: merchants } = await supabase
      .from('merchants')
      .select('*')
      .order('created_at', { ascending: false })
      .limit(10)

    const { data: recentOrders } = await supabase
      .from('orders')
      .select(`
        *,
        merchants(shop_name, shop_domain)
      `)
      .order('created_at', { ascending: false })
      .limit(10)

    // Get basic stats for super admin
    const { count: merchantCount } = await supabase
      .from('merchants')
      .select('*', { count: 'exact', head: true })

    const { count: orderCount } = await supabase
      .from('orders')
      .select('*', { count: 'exact', head: true })

    const { count: productCount } = await supabase
      .from('products')
      .select('*', { count: 'exact', head: true })

    const stats = {
      total_merchants: merchantCount || 0,
      total_orders: orderCount || 0,
      total_products: productCount || 0,
    }

    dashboardData = {
      type: 'super_admin',
      merchants,
      recentOrders,
      stats,
    }
  } else if (userRole.role === 'merchant') {
    // Merchant dashboard data
    const { data: merchantAccount } = await supabase
      .from('merchants')
      .select('*')
      .eq('user_id', user.id)
      .single()

    if (!merchantAccount) {
      throw new Error('Merchant account not found')
    }

    // Get Shopify data if merchant is connected
    let shopifyData = null
    if (merchantAccount.access_token && merchantAccount.shop_domain) {
      try {
        const shopifyClient = new ShopifyClient(
          merchantAccount.shop_domain,
          merchantAccount.access_token
        )

        const [shopInfo, products, orders] = await Promise.all([
          shopifyClient.getShopInfo(),
          shopifyClient.getProducts(10),
          shopifyClient.getOrders(10),
        ])

        shopifyData = {
          shopInfo,
          products: products.products,
          orders,
        }
      } catch (error) {
        console.error('Failed to fetch Shopify data:', error)
      }
    }

    // Get orders from our database
    const { data: customOrders } = await supabase
      .from('orders')
      .select(`
        *,
        order_items(*)
      `)
      .eq('merchant_id', merchantAccount.id)
      .order('created_at', { ascending: false })
      .limit(10)

    // Get products from our database
    const { data: customProducts } = await supabase
      .from('merchant_products')
      .select(`
        *,
        products(*)
      `)
      .eq('merchant_id', merchantAccount.id)
      .order('created_at', { ascending: false })
      .limit(10)

    dashboardData = {
      type: 'merchant',
      merchantAccount,
      shopifyData,
      customOrders,
      customProducts,
    }
  } else if (userRole.role === 'customer') {
    // Customer dashboard data
    const { data: customerOrders } = await supabase
      .from('orders')
      .select(`
        *,
        order_items(*),
        merchants(shop_name, shop_domain)
      `)
      .eq('customer_email', user.email)
      .order('created_at', { ascending: false })
      .limit(10)

    const { data: designs } = await supabase
      .from('designs')
      .select('*')
      .eq('customer_id', user.id)
      .order('created_at', { ascending: false })
      .limit(10)

    dashboardData = {
      type: 'customer',
      orders: customerOrders,
      designs,
    }
  } else {
    // Unknown role, redirect or show error
    throw new Error('Invalid user role')
  }

  return (
    <div className="min-h-screen bg-gray-50 pb-safe-bottom">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">
            {userRole.role === 'super_admin' && 'Admin Dashboard'}
            {userRole.role === 'merchant' && 'Merchant Dashboard'}
            {userRole.role === 'customer' && 'My Account'}
          </h1>
          <p className="mt-2 text-gray-600">
            {userRole.role === 'super_admin' && 'Manage the entire Biypod Customizer platform'}
            {userRole.role === 'merchant' && 'Manage your store and customizable products'}
            {userRole.role === 'customer' && 'View your orders and designs'}
          </p>
        </div>

        <Suspense fallback={<DashboardSkeleton />}>
          <DashboardContent
            user={user}
            profile={profile}
            userRole={userRole.role}
            dashboardData={dashboardData}
          />
        </Suspense>
      </div>
    </div>
  )
}
