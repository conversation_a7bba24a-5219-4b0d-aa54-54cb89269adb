'use client';

import React, { useState } from 'react';
import G<PERSON><PERSON><PERSON>ie<PERSON> from '@/components/GLBViewer';
import DesignCanvas from '@/components/DesignCanvas';
import { DesignAreaUV } from '@/lib/uv-extractor';
import { FinalTextureEngine } from '@/lib/final-texture-engine';

export default function GLBTestPage() {
  const [selectedView, setSelectedView] = useState<'front' | 'back' | 'combination'>('front');
  const [selectedProduct] = useState('YS_C11'); // We only have one product for now
  const [designAreas, setDesignAreas] = useState<DesignAreaUV[]>([]);
  const [selectedDesignArea, setSelectedDesignArea] = useState<DesignAreaUV | undefined>();
  const [textureEngine, setTextureEngine] = useState<FinalTextureEngine | undefined>();

  const getGLBPath = (view: string) => {
    const viewMap = {
      front: '1-Front.glb',
      back: '2-Back.glb',
      combination: '3-Combination.glb'
    };
    return `/glb-assets/${selectedProduct}/${viewMap[view as keyof typeof viewMap]}`;
  };

  const handleMeshClick = (meshName: string) => {
    console.log('Clicked mesh:', meshName);
    const area = designAreas.find(area => area.areaName === meshName);
    if (area) {
      setSelectedDesignArea(area);
      console.log(`Selected design area: ${meshName}`);
    }
  };

  const handleDesignAreasExtracted = (areas: DesignAreaUV[]) => {
    setDesignAreas(areas);
    if (areas.length > 0) {
      setSelectedDesignArea(areas[0]); // Select first area by default
    }
    console.log(`Extracted ${areas.length} design areas:`, areas.map(a => a.areaName));
  };

  const handleTextureEngineReady = (engine: FinalTextureEngine) => {
    setTextureEngine(engine);
    console.log('Final texture engine ready! - UPDATED');
  };

  const handleCanvasChange = (canvas: HTMLCanvasElement) => {
    if (selectedDesignArea && textureEngine) {
      console.log('🎨 MAIN: Canvas changed, applying to', selectedDesignArea.areaName);
      textureEngine.applyDesignToMesh(selectedDesignArea, canvas);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-7xl mx-auto">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-black mb-2">
            GLB Model Analyzer
          </h1>
          <p className="text-black">
            Analyzing PopCustoms 3D models to understand design areas and UV mapping
          </p>
        </div>

        {/* Product Info */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">Product: {selectedProduct}</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">3</div>
              <div className="text-sm text-black">GLB Views</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">~220KB</div>
              <div className="text-sm text-black">KTX2 Textures</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">~16KB</div>
              <div className="text-sm text-black">GLB Models</div>
            </div>
          </div>
        </div>

        {/* View Selector */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <h3 className="text-lg font-semibold mb-4">Select View</h3>
          <div className="flex space-x-4">
            {(['front', 'back', 'combination'] as const).map((view) => (
              <button
                key={view}
                onClick={() => setSelectedView(view)}
                className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                  selectedView === view
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-100 text-black hover:bg-gray-200'
                }`}
              >
                {view.charAt(0).toUpperCase() + view.slice(1)}
              </button>
            ))}
          </div>
        </div>

        {/* Main Content - GLB Viewer and Design Canvas */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* GLB Viewer */}
          <div className="bg-white rounded-lg shadow-sm p-6">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold">
                3D Model Viewer - {selectedView.charAt(0).toUpperCase() + selectedView.slice(1)} View
              </h3>
              <div className="text-sm text-black">
                Click design areas to select
              </div>
            </div>

            <div className="flex justify-center">
              <GLBViewer
                glbPath={getGLBPath(selectedView)}
                width={600}
                height={450}
                highlightDesignAreas={true}
                onMeshClick={handleMeshClick}
                onDesignAreasExtracted={handleDesignAreasExtracted}
                onTextureEngineReady={handleTextureEngineReady}
              />
            </div>
          </div>

          {/* Design Canvas */}
          <div className="bg-white rounded-lg shadow-sm p-6">
            <DesignCanvas
              width={400}
              height={400}
              selectedDesignArea={selectedDesignArea}
              onCanvasChange={handleCanvasChange}
            />

            {/* Design Area Selector */}
            {designAreas.length > 0 && (
              <div className="mt-4">
                <h4 className="font-medium mb-2">Select Design Area:</h4>
                <div className="grid grid-cols-2 gap-2">
                  {designAreas.map((area, index) => (
                    <button
                      key={index}
                      onClick={() => setSelectedDesignArea(area)}
                      className={`px-3 py-2 text-sm rounded border ${
                        selectedDesignArea?.areaName === area.areaName
                          ? 'bg-blue-500 text-white border-blue-500'
                          : 'bg-gray-50 text-black border-gray-300 hover:bg-gray-100'
                      }`}
                    >
                      {area.areaName}
                    </button>
                  ))}
                </div>
              </div>
            )}

            {/* Status */}
            <div className="mt-4 p-3 bg-green-50 rounded">
              <p className="text-green-800 text-sm">
                <strong>Status:</strong> {textureEngine ? '✅ Ready for real-time design application!' : '⏳ Loading texture engine...'}
              </p>
              {selectedDesignArea && (
                <p className="text-green-700 text-xs mt-1">
                  Currently editing: <strong>{selectedDesignArea.areaName}</strong>
                  ({selectedDesignArea.mapping.textureSize.width}x{selectedDesignArea.mapping.textureSize.height})
                </p>
              )}
            </div>
          </div>
        </div>

        {/* Analysis Results */}
        <div className="mt-6 bg-white rounded-lg shadow-sm p-6">
          <h3 className="text-lg font-semibold mb-4">Analysis Results</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-medium mb-2">Expected Design Areas (from Blender):</h4>
              <ul className="text-sm text-black space-y-1">
                <li>• Front - Main design area</li>
                <li>• Back - Back design area</li>
                <li>• Left_Sleeve - Left sleeve design</li>
                <li>• Right_Sleeve - Right sleeve design</li>
                <li>• Collar - Collar design area</li>
                <li>• Mask - Clipping/masking areas</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium mb-2">Technical Details:</h4>
              <ul className="text-sm text-black space-y-1">
                <li>• <strong>Compression:</strong> Draco geometry compression</li>
                <li>• <strong>Textures:</strong> KTX2 format (~80% smaller)</li>
                <li>• <strong>UV Mapping:</strong> Optimized for design application</li>
                <li>• <strong>Materials:</strong> Separate material slots per area</li>
                <li>• <strong>File Size:</strong> Highly optimized for web</li>
              </ul>
            </div>
          </div>
        </div>

        {/* Debug Info */}
        <div className="mt-6 bg-green-50 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-green-900 mb-2">Debug Information</h3>
          <div className="text-green-800 space-y-2 text-sm">
            <p><strong>Status:</strong> ✅ Draco decoder files installed correctly</p>
            <p><strong>GLB Path:</strong> <code>{getGLBPath(selectedView)}</code></p>
            <p><strong>Compression:</strong> Files use KHR_draco_mesh_compression (confirmed)</p>
            <p><strong>File Sizes:</strong> Front: 16.3KB, Back: 15.1KB, Combination: 23.2KB</p>
            <p><strong>Design Areas:</strong> Front, Back, Left_Sleeve, Right_Sleeve, Collar, Mask</p>
          </div>
        </div>

        {/* Next Steps */}
        <div className="mt-6 bg-blue-50 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-blue-900 mb-2">Next Steps</h3>
          <div className="text-blue-800 space-y-2">
            <p>🔄 <strong>GLB Loading:</strong> Testing with proper Draco decoder</p>
            <p>✅ <strong>Draco Support:</strong> Correct decoder files installed</p>
            <p>⏳ <strong>Design Area Detection:</strong> Identifying customizable areas</p>
            <p>⏳ <strong>UV Mapping Analysis:</strong> Extract coordinates for design application</p>
            <p>⏳ <strong>Texture Application:</strong> Apply designs from Fabric.js canvas</p>
          </div>
        </div>
      </div>
    </div>
  );
}