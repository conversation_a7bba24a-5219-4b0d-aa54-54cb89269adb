'use client'

import { useEffect, useState } from 'react'

export default function TestSafeAreaPage() {
  const [debugInfo, setDebugInfo] = useState<any>({})

  useEffect(() => {
    const updateDebugInfo = () => {
      const info = {
        userAgent: navigator.userAgent,
        isIOS: /iPad|iPhone|iPod/.test(navigator.userAgent),
        isSafari: /Safari/.test(navigator.userAgent) && !/Chrome/.test(navigator.userAgent),
        viewportHeight: window.innerHeight,
        visualViewportHeight: window.visualViewport?.height || 'Not supported',
        documentHeight: document.documentElement.clientHeight,
        safeAreaBottom: getComputedStyle(document.documentElement).getPropertyValue('env(safe-area-inset-bottom)') || 'Not available',
        calculatedSafeArea: getComputedStyle(document.documentElement).getPropertyValue('--calculated-safe-area-bottom') || 'Not set',
        bodyPaddingBottom: getComputedStyle(document.body).paddingBottom,
        hasIOSSafeAreaClass: document.documentElement.classList.contains('ios-safari-safe-area-active')
      }
      setDebugInfo(info)
    }

    updateDebugInfo()

    // Update on resize
    window.addEventListener('resize', updateDebugInfo)
    if (window.visualViewport) {
      window.visualViewport.addEventListener('resize', updateDebugInfo)
    }

    return () => {
      window.removeEventListener('resize', updateDebugInfo)
      if (window.visualViewport) {
        window.visualViewport.removeEventListener('resize', updateDebugInfo)
      }
    }
  }, [])

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-purple-50 pb-safe-bottom">
      <div className="max-w-4xl mx-auto px-4 py-8">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            Mobile Safari Safe Area Test
          </h1>
          <p className="text-lg text-gray-600">
            This page tests the safe area implementation for mobile Safari
          </p>
        </div>

        <div className="space-y-6">
          {/* Test content */}
          <div className="bg-white rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-semibold text-gray-800 mb-4">
              Safe Area Implementation
            </h2>
            <div className="space-y-4 text-gray-600">
              <p>
                ✅ <strong>Global body padding:</strong> Applied via CSS to prevent content from being hidden behind Safari's bottom navigation bar
              </p>
              <p>
                ✅ <strong>Viewport configuration:</strong> Set to 'cover' to enable safe area insets
              </p>
              <p>
                ✅ <strong>CSS utility classes:</strong> Available for fine-grained control (.pb-safe-bottom, .pt-safe-top, etc.)
              </p>
              <p>
                ✅ <strong>Responsive design:</strong> Safe area fixes only apply when needed, won't affect desktop layouts
              </p>
            </div>
          </div>

          {/* Visual indicators */}
          <div className="bg-white rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-semibold text-gray-800 mb-4">
              Visual Test Elements
            </h2>
            <div className="space-y-4">
              <div className="h-20 bg-blue-100 rounded-lg flex items-center justify-center">
                <span className="text-blue-800 font-medium">Content Block 1</span>
              </div>
              <div className="h-20 bg-green-100 rounded-lg flex items-center justify-center">
                <span className="text-green-800 font-medium">Content Block 2</span>
              </div>
              <div className="h-20 bg-purple-100 rounded-lg flex items-center justify-center">
                <span className="text-purple-800 font-medium">Content Block 3</span>
              </div>
            </div>
          </div>

          {/* Bottom content that should be visible */}
          <div className="bg-red-50 border-2 border-red-200 rounded-lg p-6">
            <h2 className="text-xl font-semibold text-red-800 mb-4">
              🎯 Critical Bottom Content
            </h2>
            <p className="text-red-700">
              This content should ALWAYS be visible and accessible on mobile Safari, 
              even when the bottom navigation bar is present. If you can see and interact 
              with this content on mobile Safari, the safe area implementation is working correctly.
            </p>
            <button className="mt-4 bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors">
              Test Button - Should be clickable
            </button>
          </div>

          {/* Debug Information */}
          <div className="bg-blue-50 border-2 border-blue-200 rounded-lg p-6">
            <h2 className="text-xl font-semibold text-blue-800 mb-4">
              🔍 Debug Information
            </h2>
            <div className="text-blue-700 space-y-2 text-sm font-mono">
              <p><strong>iOS Device:</strong> {debugInfo.isIOS ? 'Yes' : 'No'}</p>
              <p><strong>Safari Browser:</strong> {debugInfo.isSafari ? 'Yes' : 'No'}</p>
              <p><strong>Viewport Height:</strong> {debugInfo.viewportHeight}px</p>
              <p><strong>Visual Viewport Height:</strong> {debugInfo.visualViewportHeight}px</p>
              <p><strong>Document Height:</strong> {debugInfo.documentHeight}px</p>
              <p><strong>Safe Area Bottom (CSS):</strong> {debugInfo.safeAreaBottom}</p>
              <p><strong>Calculated Safe Area:</strong> {debugInfo.calculatedSafeArea}</p>
              <p><strong>Body Padding Bottom:</strong> {debugInfo.bodyPaddingBottom}</p>
              <p><strong>iOS Safe Area Active:</strong> {debugInfo.hasIOSSafeAreaClass ? 'Yes' : 'No'}</p>
            </div>
          </div>

          {/* Instructions */}
          <div className="bg-yellow-50 border-2 border-yellow-200 rounded-lg p-6">
            <h2 className="text-xl font-semibold text-yellow-800 mb-4">
              📱 Testing Instructions
            </h2>
            <div className="text-yellow-700 space-y-2">
              <p><strong>On Mobile Safari:</strong></p>
              <ol className="list-decimal list-inside space-y-1 ml-4">
                <li>Scroll to the bottom of this page</li>
                <li>Verify the red "Critical Bottom Content" section is fully visible</li>
                <li>Try tapping the "Test Button" - it should be clickable</li>
                <li>The content should not be hidden behind Safari's bottom navigation bar</li>
                <li>Check the debug information above for technical details</li>
              </ol>
              <p className="mt-4"><strong>On Desktop:</strong></p>
              <p className="ml-4">The layout should appear normal with standard padding</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
