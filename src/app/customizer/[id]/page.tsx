'use client'

import { useState, useEffect } from 'react'
import { useParams } from 'next/navigation'
import CustomizerClientWrapper from '@/components/customizer/customizer-client-wrapper'

interface Product {
  id: string
  name: string
  description: string
  slug: string
  base_price: number
  print_files: any[]
  mockup_files: any[]
  glb_files: any[]
  product_images: any[]
  is_active: boolean
  created_at: string
  updated_at: string
}

export default function CustomizerPage() {
  const params = useParams()
  const productId = params.id as string
  
  const [product, setProduct] = useState<Product | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    if (!productId) return

    const fetchProduct = async () => {
      try {
        setLoading(true)
        setError(null)

        const response = await fetch(`/api/products/${productId}`)
        
        if (!response.ok) {
          throw new Error(`Failed to fetch product: ${response.status}`)
        }

        const productData = await response.json()
        setProduct(productData)
        console.log('Loaded product:', productData)

        // Debug GLB files specifically
        console.log('🔍 Frontend - Raw GLB files:', productData.glb_files)
        console.log('🔍 Frontend - GLB files type:', typeof productData.glb_files)
        console.log('🔍 Frontend - GLB files is array:', Array.isArray(productData.glb_files))

        // Log file counts for debugging
        console.log('Print files:', productData.print_files?.length || 0)
        console.log('Mockup files:', productData.mockup_files?.length || 0)
        console.log('GLB files:', productData.glb_files?.length || 0)
        console.log('Product images:', productData.product_images?.length || 0)

      } catch (err) {
        console.error('Error loading product:', err)
        setError(err instanceof Error ? err.message : 'Failed to load product')
      } finally {
        setLoading(false)
      }
    }

    fetchProduct()
  }, [productId])

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading product customizer...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="text-red-600 mb-4">
            <svg className="w-16 h-16 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <h1 className="text-2xl font-bold mb-2">Error Loading Product</h1>
            <p className="text-gray-600">{error}</p>
          </div>
          <button 
            onClick={() => window.location.reload()} 
            className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
          >
            Try Again
          </button>
        </div>
      </div>
    )
  }

  if (!product) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Product Not Found</h1>
          <p className="text-gray-600">The requested product could not be found.</p>
        </div>
      </div>
    )
  }

  return (
    <CustomizerClientWrapper
      product={product}
      isIframe={false}
      shop="biypod"
      variant="default"
    />
  )
}
