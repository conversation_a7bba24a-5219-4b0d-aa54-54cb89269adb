<?xml version="1.0" encoding="UTF-8"?>
<svg viewBox="0 0 800 600" xmlns="http://www.w3.org/2000/svg">
  <!-- T-Shirt <PERSON>up Mapping Areas -->
  
  <!-- Front design area on the chest -->
  <rect id="front" x="300" y="200" width="200" height="150" 
        fill="none" stroke="#ff0000" stroke-width="2" stroke-dasharray="5,5"/>
  
  <!-- Back design area -->
  <rect id="back" x="300" y="380" width="200" height="150" 
        fill="none" stroke="#0000ff" stroke-width="2" stroke-dasharray="5,5"/>
  
  <!-- Left sleeve area -->
  <circle id="left-sleeve" cx="200" cy="250" r="40" 
          fill="none" stroke="#00ff00" stroke-width="2" stroke-dasharray="5,5"/>
  
  <!-- Right sleeve area -->
  <circle id="right-sleeve" cx="600" cy="250" r="40" 
          fill="none" stroke="#00ff00" stroke-width="2" stroke-dasharray="5,5"/>
  
  <!-- Logo area (small, top center) -->
  <circle id="logo" cx="400" cy="150" r="25" 
          fill="none" stroke="#ff00ff" stroke-width="2" stroke-dasharray="5,5"/>
  
  <!-- Pocket area (small rectangle) -->
  <rect id="pocket" x="350" y="280" width="100" height="60" 
        fill="none" stroke="#ffaa00" stroke-width="2" stroke-dasharray="5,5"/>
  
  <!-- Custom shape area (curved design area) -->
  <path id="custom-area" d="M150,400 Q200,350 250,400 L250,450 Q200,500 150,450 Z" 
        fill="none" stroke="#aa00ff" stroke-width="2" stroke-dasharray="5,5"/>
  
  <!-- Labels for reference (these won't be extracted as mapping areas) -->
  <text x="400" y="190" text-anchor="middle" font-size="12" fill="#666">Front</text>
  <text x="400" y="470" text-anchor="middle" font-size="12" fill="#666">Back</text>
  <text x="200" y="300" text-anchor="middle" font-size="10" fill="#666">L.Sleeve</text>
  <text x="600" y="300" text-anchor="middle" font-size="10" fill="#666">R.Sleeve</text>
  <text x="400" y="140" text-anchor="middle" font-size="10" fill="#666">Logo</text>
  <text x="400" y="315" text-anchor="middle" font-size="10" fill="#666">Pocket</text>
  <text x="200" y="480" text-anchor="middle" font-size="10" fill="#666">Custom</text>
</svg>
