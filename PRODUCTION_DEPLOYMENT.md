# Production Deployment Guide

## Pre-Deployment Checklist

All critical production issues have been fixed:

- **User Account Creation**: Proper user accounts are now created during Shopify installation
- **Order Items**: Complete order items implementation with product mapping
- **Product Variants**: Full product variants system implemented
- **Database Schema**: Added `product_variants` and `order_items` tables
- **Environment Variables**: Production template created

## Required Manual Steps

### 1. Apply Database Migrations

Run the new migration to add product variants and order items tables:

```bash
# Navigate to your Supabase project
cd supabase

# Apply the new migration
supabase db push

# Or if using Supabase CLI remotely:
supabase db push --project-ref jxuyco<PERSON>iepqhndbvjto
```

### 2. Generate Production Secrets

**CRITICAL**: Replace these development values with production secrets:

```bash
# Generate a strong NextAuth secret (32+ characters)
NEXTAUTH_SECRET=$(openssl rand -base64 32)

# Generate a webhook secret for security
WEBHOOK_SECRET=$(openssl rand -base64 32)

# Create a strong super admin password
SUPER_ADMIN_PASSWORD=$(openssl rand -base64 16)
```

### 3. Update Environment Variables

Copy `.env.production.example` to your Vercel environment variables:

**Required Changes:**
- `SHOPIFY_APP_URL`: Update to your production domain
- `NEXTAUTH_SECRET`: Use generated secret
- `NEXTAUTH_URL`: Update to your production domain  
- `SUPER_ADMIN_EMAIL`: Use your real admin email
- `SUPER_ADMIN_PASSWORD`: Use generated password
- `WEBHOOK_SECRET`: Use generated secret

**Optional (for future features):**
- Stripe keys (if implementing billing)

### 4. Update Shopify Partner Dashboard

1. Go to [Shopify Partners](https://partners.shopify.com)
2. Select your app
3. Update these URLs:
   - **App URL**: `https://your-domain.vercel.app`
   - **Allowed redirection URL(s)**: `https://your-domain.vercel.app/api/auth/shopify/callback`

### 5. Deploy to Vercel

```bash
# Commit all changes
git add .
git commit -m "Production ready: Fixed all temporary implementations"
git push origin main

# Deploy to Vercel (if not auto-deployed)
vercel --prod
```

## Post-Deployment Testing

### Test Shopify Installation Flow

1. Visit: `https://your-domain.vercel.app/api/auth/shopify/install`
2. Install on a development store
3. Verify:
   - User account is created
   - Merchant record is created
   - Dashboard is accessible

### Test Webhook Processing

1. Create a product in your Shopify store
2. Verify in Supabase:
   - Product appears in `merchant_products`
   - Variants appear in `product_variants`

3. Create an order in your Shopify store
4. Verify in Supabase:
   - Order appears in `orders`
   - Order items appear in `order_items`

### Test Dashboard Access

1. Visit: `https://your-domain.vercel.app/dashboard`
2. Verify all role-based dashboards work:
   - Super Admin dashboard
   - Merchant dashboard
   - Customer dashboard

## Security Verification

- All environment secrets are production-grade
- No hardcoded credentials in code
- RLS policies are enabled on all tables
- Webhook HMAC verification is active
- User authentication is required for protected routes

## Monitoring Setup

### Supabase Monitoring

1. Enable real-time monitoring in Supabase dashboard
2. Set up alerts for:
   - Database connection limits
   - API rate limits
   - Storage usage

### Vercel Monitoring

1. Enable Vercel Analytics
2. Monitor:
   - Function execution times
   - Error rates
   - Build success rates

## 🚨 Rollback Plan

If issues occur:

1. **Immediate**: Revert to previous Vercel deployment
2. **Database**: Rollback migrations if needed:
   ```bash
   supabase db reset --project-ref jxuycozeiepqhndbvjto
   ```
3. **Shopify**: Update app URLs back to staging

## Support Contacts

- **Supabase Issues**: [Supabase Support](https://supabase.com/support)
- **Vercel Issues**: [Vercel Support](https://vercel.com/support)
- **Shopify Issues**: [Shopify Partners Support](https://partners.shopify.com/support)

## Success Criteria

Your app is production-ready when:

- All tests pass
- No console errors in production
- Shopify installation works end-to-end
- Webhooks process correctly
- All user roles can access their dashboards
- Database operations complete successfully

## Next Steps After Deployment

1. **Monitor**: Watch logs for 24-48 hours
2. **Test**: Install on multiple development stores
3. **Document**: Create user guides for merchants
4. **Scale**: Plan for increased usage and database optimization
5. **Features**: Begin implementing customization features

---

**🎯 Your Biypod Customizer app is now production-ready!**
